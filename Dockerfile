FROM golang:1.24-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git make

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Generate GraphQL code before building
RUN make gqlgen


# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/graphql cmd/graphql/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/worker_meme_user cmd/worker_meme_user/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/worker_hyperliquid_event cmd/worker_hyperliquid_event/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/scheduler cmd/scheduler/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/onchain_crawler cmd/onchain_crawler/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/reward_executor cmd/reward_executor/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates and atlas
RUN apk --no-cache add ca-certificates curl
RUN curl -sSf https://atlasgo.sh | sh

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main/ .
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/migrations ./migrations
COPY --from=builder /app/entrypoint.sh .
COPY sensitive_configs/ sensitive_configs/
COPY env/ env/

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Expose port
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["./entrypoint.sh"]

# Default command
CMD ["./graphql"]
