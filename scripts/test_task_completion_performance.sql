-- Performance Test Script for Task Completion Tables
-- This script tests the performance improvement of the new frequency-specific tables

-- Enable timing
\timing on

-- Create test data for performance comparison
-- Note: This should be run in a test environment only

-- 1. Test query performance on old table vs new tables
EXPLAIN ANALYZE
SELECT COUNT(*) FROM task_completion_history 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND completion_date >= CURRENT_DATE - INTERVAL '30 days';

-- 2. Test query performance on daily completions table
EXPLAIN ANALYZE
SELECT COUNT(*) FROM daily_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND completion_date >= CURRENT_DATE - INTERVAL '30 days';

-- 3. Test query performance for one-time tasks
EXPLAIN ANALYZE
SELECT * FROM one_time_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid;

-- 4. Test query performance for unlimited tasks with sequence
EXPLAIN ANALYZE
SELECT * FROM unlimited_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND task_id = '550e8400-e29b-41d4-a716-************'::uuid
ORDER BY sequence_number DESC
LIMIT 10;

-- 5. Test query performance for progressive tasks
EXPLAIN ANALYZE
SELECT * FROM progressive_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND task_id = '550e8400-e29b-41d4-a716-************'::uuid
ORDER BY level_completed DESC;

-- 6. Test query performance for manual tasks by status
EXPLAIN ANALYZE
SELECT * FROM manual_task_completions 
WHERE status = 'PENDING'
ORDER BY completion_date ASC
LIMIT 50;

-- 7. Test aggregation queries
-- Old table aggregation
EXPLAIN ANALYZE
SELECT 
    DATE_TRUNC('day', completion_date) as day,
    COUNT(*) as completions,
    SUM(points_awarded) as total_points
FROM task_completion_history 
WHERE completion_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE_TRUNC('day', completion_date)
ORDER BY day;

-- New tables aggregation (daily tasks)
EXPLAIN ANALYZE
SELECT 
    completion_date as day,
    COUNT(*) as completions,
    SUM(points_awarded) as total_points
FROM daily_task_completions 
WHERE completion_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY completion_date
ORDER BY completion_date;

-- 8. Test join performance
-- Old table join
EXPLAIN ANALYZE
SELECT 
    at.name,
    COUNT(tch.id) as completion_count,
    AVG(tch.points_awarded) as avg_points
FROM task_completion_history tch
JOIN activity_tasks at ON tch.task_id = at.id
WHERE tch.completion_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY at.name
ORDER BY completion_count DESC;

-- New tables join (daily tasks)
EXPLAIN ANALYZE
SELECT 
    at.name,
    COUNT(dtc.id) as completion_count,
    AVG(dtc.points_awarded) as avg_points
FROM daily_task_completions dtc
JOIN activity_tasks at ON dtc.task_id = at.id
WHERE dtc.completion_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY at.name
ORDER BY completion_count DESC;

-- 9. Test insert performance
-- Prepare test data
DO $$
DECLARE
    test_user_id UUID := '550e8400-e29b-41d4-a716-************'::uuid;
    test_task_id UUID := '550e8400-e29b-41d4-a716-************'::uuid;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
BEGIN
    -- Test insert performance for daily tasks
    start_time := clock_timestamp();
    
    FOR i IN 1..1000 LOOP
        INSERT INTO daily_task_completions (
            user_id, task_id, points_awarded, completion_date, completion_time
        ) VALUES (
            test_user_id, 
            test_task_id, 
            10, 
            CURRENT_DATE - (i % 30),
            CURRENT_TIMESTAMP - (i % 30) * INTERVAL '1 day'
        );
    END LOOP;
    
    end_time := clock_timestamp();
    duration := end_time - start_time;
    
    RAISE NOTICE 'Daily task completions insert time for 1000 records: %', duration;
    
    -- Clean up test data
    DELETE FROM daily_task_completions WHERE user_id = test_user_id;
END $$;

-- 10. Test partition pruning for daily tasks
-- This should show that only relevant partitions are scanned
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) FROM daily_task_completions 
WHERE completion_date = CURRENT_DATE;

-- 11. Test unique constraint performance for one-time tasks
-- This should be very fast due to the unique index
EXPLAIN ANALYZE
SELECT EXISTS(
    SELECT 1 FROM one_time_task_completions 
    WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
    AND task_id = '550e8400-e29b-41d4-a716-************'::uuid
);

-- 12. Test sequence performance for unlimited tasks
EXPLAIN ANALYZE
SELECT MAX(sequence_number) FROM unlimited_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND task_id = '550e8400-e29b-41d4-a716-************'::uuid;

-- 13. Test level-based queries for progressive tasks
EXPLAIN ANALYZE
SELECT 
    level_completed,
    total_progress,
    points_awarded,
    completion_date
FROM progressive_task_completions 
WHERE user_id = '550e8400-e29b-41d4-a716-************'::uuid
AND task_id = '550e8400-e29b-41d4-a716-************'::uuid
ORDER BY level_completed;

-- 14. Test approval workflow queries for manual tasks
EXPLAIN ANALYZE
SELECT 
    COUNT(*) FILTER (WHERE status = 'PENDING') as pending,
    COUNT(*) FILTER (WHERE status = 'APPROVED') as approved,
    COUNT(*) FILTER (WHERE status = 'REJECTED') as rejected
FROM manual_task_completions 
WHERE completion_date >= CURRENT_DATE - INTERVAL '30 days';

-- 15. Compare table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE tablename IN (
    'task_completion_history',
    'daily_task_completions',
    'one_time_task_completions',
    'unlimited_task_completions',
    'progressive_task_completions',
    'manual_task_completions'
)
ORDER BY size_bytes DESC;

-- 16. Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN (
    'task_completion_history',
    'daily_task_completions',
    'one_time_task_completions',
    'unlimited_task_completions',
    'progressive_task_completions',
    'manual_task_completions'
)
ORDER BY idx_scan DESC;

-- 17. Performance summary
DO $$
BEGIN
    RAISE NOTICE '=== PERFORMANCE TEST SUMMARY ===';
    RAISE NOTICE 'The new frequency-specific tables should show:';
    RAISE NOTICE '1. Faster queries due to smaller table sizes';
    RAISE NOTICE '2. Better index utilization for specific access patterns';
    RAISE NOTICE '3. Partition pruning for daily tasks';
    RAISE NOTICE '4. Unique constraint benefits for one-time tasks';
    RAISE NOTICE '5. Sequence-based ordering for unlimited tasks';
    RAISE NOTICE '6. Level-based queries for progressive tasks';
    RAISE NOTICE '7. Status-based filtering for manual tasks';
    RAISE NOTICE '================================';
END $$;

-- Disable timing
\timing off
