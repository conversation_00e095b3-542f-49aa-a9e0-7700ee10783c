#!/bin/bash

# Test script for Activity Cashback Summary API
# Usage: ./scripts/test-activity-cashback-summary.sh [JWT_TOKEN]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENDPOINT="http://localhost:8080/graphql"
JWT_TOKEN="${1:-$JWT_TOKEN}"

echo -e "${BLUE}=== Activity Cashback Summary API Test ===${NC}"
echo

# Check if JWT token is provided
if [ -z "$JWT_TOKEN" ]; then
    echo -e "${RED}Error: JWT token is required${NC}"
    echo "Usage: $0 [JWT_TOKEN]"
    echo "Or set JWT_TOKEN environment variable"
    echo
    echo "Generate a token with: make jwt-token"
    exit 1
fi

# Check if server is running
echo -e "${YELLOW}Checking if server is running...${NC}"
if ! curl -s "$ENDPOINT" > /dev/null; then
    echo -e "${RED}Error: Server is not running at $ENDPOINT${NC}"
    echo "Start the server with: make run or make dev"
    exit 1
fi
echo -e "${GREEN}✓ Server is running${NC}"
echo

# GraphQL query
QUERY='{
  "query": "query { activityCashbackSummary { success message data { currentLevel currentLevelName nextLevel nextLevelName currentScore totalScoreForNextLevel scoreRequiredToUpgrade progressPercentage accumulatedTradingVolumeUsd activeLogonDays accumulatedCashbackUsd claimableCashbackUsd claimedCashbackUsd currentTierColor currentTierIcon nextTierColor nextTierIcon } } }"
}'

echo -e "${YELLOW}Testing Activity Cashback Summary API...${NC}"
echo "Endpoint: $ENDPOINT"
echo "Query: activityCashbackSummary"
echo

# Make the request
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "$QUERY" \
  "$ENDPOINT")

# Check if request was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to make request${NC}"
    exit 1
fi

# Parse response
echo -e "${BLUE}Response:${NC}"
echo "$RESPONSE" | jq '.'

# Check for GraphQL errors
ERRORS=$(echo "$RESPONSE" | jq -r '.errors // empty | length')
if [ "$ERRORS" != "" ] && [ "$ERRORS" -gt 0 ]; then
    echo
    echo -e "${RED}❌ GraphQL Errors found:${NC}"
    echo "$RESPONSE" | jq -r '.errors[] | "  - \(.message)"'
    exit 1
fi

# Check success field
SUCCESS=$(echo "$RESPONSE" | jq -r '.data.activityCashbackSummary.success // false')
if [ "$SUCCESS" != "true" ]; then
    echo
    echo -e "${RED}❌ API returned success: false${NC}"
    MESSAGE=$(echo "$RESPONSE" | jq -r '.data.activityCashbackSummary.message // "No message"')
    echo "Message: $MESSAGE"
    exit 1
fi

# Extract and display key data
echo
echo -e "${GREEN}✓ API call successful!${NC}"
echo

DATA=$(echo "$RESPONSE" | jq -r '.data.activityCashbackSummary.data')
if [ "$DATA" != "null" ]; then
    echo -e "${BLUE}Summary Data:${NC}"
    
    # Current Level
    CURRENT_LEVEL=$(echo "$DATA" | jq -r '.currentLevel // "N/A"')
    CURRENT_LEVEL_NAME=$(echo "$DATA" | jq -r '.currentLevelName // "N/A"')
    echo "  Current Level: $CURRENT_LEVEL ($CURRENT_LEVEL_NAME)"
    
    # Next Level
    NEXT_LEVEL=$(echo "$DATA" | jq -r '.nextLevel // "Max Level"')
    NEXT_LEVEL_NAME=$(echo "$DATA" | jq -r '.nextLevelName // "Max Level"')
    echo "  Next Level: $NEXT_LEVEL ($NEXT_LEVEL_NAME)"
    
    # Progress
    CURRENT_SCORE=$(echo "$DATA" | jq -r '.currentScore // 0')
    TOTAL_SCORE=$(echo "$DATA" | jq -r '.totalScoreForNextLevel // "N/A"')
    SCORE_REQUIRED=$(echo "$DATA" | jq -r '.scoreRequiredToUpgrade // 0')
    PROGRESS=$(echo "$DATA" | jq -r '.progressPercentage // 0')
    echo "  Progress: $CURRENT_SCORE / $TOTAL_SCORE ($PROGRESS%)"
    echo "  Score Required to Upgrade: $SCORE_REQUIRED"
    
    # Trading Volume
    TRADING_VOLUME=$(echo "$DATA" | jq -r '.accumulatedTradingVolumeUsd // 0')
    echo "  Trading Volume: \$$(printf "%.2f" $TRADING_VOLUME)"
    
    # Activity
    ACTIVE_DAYS=$(echo "$DATA" | jq -r '.activeLogonDays // 0')
    echo "  Active Days: $ACTIVE_DAYS"
    
    # Cashback
    ACCUMULATED=$(echo "$DATA" | jq -r '.accumulatedCashbackUsd // 0')
    CLAIMABLE=$(echo "$DATA" | jq -r '.claimableCashbackUsd // 0')
    CLAIMED=$(echo "$DATA" | jq -r '.claimedCashbackUsd // 0')
    echo "  Cashback - Accumulated: \$$(printf "%.2f" $ACCUMULATED)"
    echo "  Cashback - Claimable: \$$(printf "%.2f" $CLAIMABLE)"
    echo "  Cashback - Claimed: \$$(printf "%.2f" $CLAIMED)"
    
else
    echo -e "${YELLOW}⚠ No data returned${NC}"
fi

echo
echo -e "${GREEN}✅ Test completed successfully!${NC}"
