#!/bin/bash

# Auto-Fix Build Script
# This script monitors build output and automatically fixes common issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[AUTO-FIX]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[FIXED]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to fix specific build errors
fix_build_error() {
    local error_output="$1"
    local fixed=false
    
    # Fix 1: Undefined symbols (missing imports)
    if echo "$error_output" | grep -q "undefined:"; then
        print_status "Fixing undefined symbols..."
        
        # Extract undefined symbols
        undefined_symbols=$(echo "$error_output" | grep "undefined:" | sed 's/.*undefined: //' | cut -d' ' -f1 | sort -u)
        
        for symbol in $undefined_symbols; do
            case $symbol in
                "uuid.UUID"|"uuid.New"|"uuid.Parse")
                    print_status "Adding github.com/google/uuid import..."
                    find internal/service/activity_cashback/ -name "*.go" -exec grep -l "$symbol" {} \; | \
                    xargs -I {} sh -c 'if ! grep -q "github.com/google/uuid" "{}"; then sed -i "1a\\import \"github.com/google/uuid\"" "{}"; fi'
                    fixed=true
                    ;;
                "decimal.Decimal"|"decimal.NewFromFloat")
                    print_status "Adding github.com/shopspring/decimal import..."
                    find internal/service/activity_cashback/ -name "*.go" -exec grep -l "$symbol" {} \; | \
                    xargs -I {} sh -c 'if ! grep -q "github.com/shopspring/decimal" "{}"; then sed -i "1a\\import \"github.com/shopspring/decimal\"" "{}"; fi'
                    fixed=true
                    ;;
                "fmt.Errorf"|"fmt.Sprintf")
                    print_status "Adding fmt import..."
                    find internal/service/activity_cashback/ -name "*.go" -exec grep -l "$symbol" {} \; | \
                    xargs -I {} sh -c 'if ! grep -q "\"fmt\"" "{}"; then sed -i "1a\\import \"fmt\"" "{}"; fi'
                    fixed=true
                    ;;
            esac
        done
        
        if [ "$fixed" = true ]; then
            print_success "Fixed undefined symbols"
            goimports -w internal/service/activity_cashback/
            goimports -w internal/controller/graphql/resolvers/
        fi
    fi
    
    # Fix 2: Type conversion errors
    if echo "$error_output" | grep -q "cannot use.*as.*value in argument"; then
        print_status "Fixing type conversion errors..."
        
        # Fix string to UUID conversion
        if echo "$error_output" | grep -q "cannot use.*string.*as uuid.UUID"; then
            print_status "Fixing string to UUID conversion..."
            
            # Find files with this issue and fix them
            find internal/service/activity_cashback/ -name "*.go" -exec grep -l "ProcessTradingEvent\|ProcessTaskByName" {} \; | \
            while read file; do
                if grep -q "ProcessTradingEvent(ctx, userID," "$file" && ! grep -q "uuid.Parse(userID)" "$file"; then
                    print_status "Fixing $file..."
                    sed -i '' 's/ProcessTradingEvent(ctx, userID,/userUUID, _ := uuid.Parse(userID); ProcessTradingEvent(ctx, userUUID,/' "$file"
                    fixed=true
                fi
            done
        fi
        
        # Fix pointer dereference issues
        if echo "$error_output" | grep -q "SortOrder"; then
            print_status "Fixing SortOrder pointer issues..."
            find internal/controller/graphql/resolvers/ -name "*.go" -exec sed -i '' 's/SortOrder:   input.SortOrder,/SortOrder:   *input.SortOrder,/' {} \;
            fixed=true
        fi
        
        if [ "$fixed" = true ]; then
            print_success "Fixed type conversion errors"
        fi
    fi
    
    # Fix 3: Unused variable errors
    if echo "$error_output" | grep -q "declared and not used"; then
        print_status "Fixing unused variable errors..."
        
        unused_vars=$(echo "$error_output" | grep "declared and not used" | sed 's/.*declared and not used: //' | cut -d' ' -f1)
        
        for var in $unused_vars; do
            case $var in
                "ctx")
                    print_status "Removing unused ctx variable..."
                    find internal/service/activity_cashback/ -name "*.go" -exec sed -i '' '/ctx := context.Background()/d' {} \;
                    fixed=true
                    ;;
                *)
                    print_status "Adding blank identifier for unused variable: $var"
                    # This is more complex and might need manual intervention
                    ;;
            esac
        done
        
        if [ "$fixed" = true ]; then
            print_success "Fixed unused variable errors"
        fi
    fi
    
    # Fix 4: Import issues
    if echo "$error_output" | grep -q "imported and not used"; then
        print_status "Fixing unused imports..."
        goimports -w internal/service/activity_cashback/
        goimports -w internal/controller/graphql/resolvers/
        fixed=true
        print_success "Fixed unused imports"
    fi
    
    return $fixed
}

# Main auto-fix function
auto_fix_build() {
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Build attempt $attempt/$max_attempts..."
        
        # Capture build output
        build_output=$(go build cmd/graphql/main.go 2>&1) || build_failed=true
        
        if [ -z "$build_failed" ]; then
            print_success "Build successful on attempt $attempt!"
            rm -f main  # Clean up the built binary
            return 0
        fi
        
        print_warning "Build failed on attempt $attempt"
        echo "$build_output"
        
        # Try to fix the errors
        if fix_build_error "$build_output"; then
            print_status "Applied fixes, retrying build..."
            unset build_failed
            attempt=$((attempt + 1))
        else
            print_error "Could not automatically fix build errors"
            echo ""
            echo "Manual intervention required. Common fixes:"
            echo "1. Run: make gqlgen-fix"
            echo "2. Run: make format-code"
            echo "3. Run: ./scripts/fix-build-issues.sh"
            echo ""
            echo "Build output:"
            echo "$build_output"
            return 1
        fi
    done
    
    print_error "Failed to fix build after $max_attempts attempts"
    return 1
}

# Check if this script is being run directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    print_status "🔧 Auto-fixing build issues..."
    
    # First, try the comprehensive fix script
    if [ -f "scripts/fix-build-issues.sh" ]; then
        print_status "Running comprehensive fix script first..."
        chmod +x scripts/fix-build-issues.sh
        if ./scripts/fix-build-issues.sh; then
            print_success "Comprehensive fix completed successfully"
            exit 0
        else
            print_warning "Comprehensive fix had issues, trying targeted fixes..."
        fi
    fi
    
    # Then try the auto-fix approach
    if auto_fix_build; then
        print_success "Auto-fix completed successfully! 🎉"
        exit 0
    else
        print_error "Auto-fix failed. Manual intervention required."
        exit 1
    fi
fi
