#!/bin/bash

# GraphQL Generation Doctor Script
# This script diagnoses and fixes common GraphQL generation issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "gqlgen.yml" ]; then
        print_error "gqlgen.yml not found. Please run this script from the project root."
        exit 1
    fi
    print_success "Found gqlgen.yml configuration"
}

# Check Go environment
check_go_environment() {
    print_header "Checking Go Environment"
    
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    GO_VERSION=$(go version | cut -d' ' -f3)
    print_success "Go version: $GO_VERSION"
    
    GO_MOD=$(go list -m)
    print_success "Go module: $GO_MOD"
}

# Check GraphQL files
check_graphql_files() {
    print_header "Checking GraphQL Files"
    
    # Check schema files
    SCHEMA_FILES=$(find . -name "*.graphqls" -o -name "*.gql" | wc -l)
    if [ "$SCHEMA_FILES" -eq 0 ]; then
        print_error "No GraphQL schema files found"
        exit 1
    fi
    print_success "Found $SCHEMA_FILES schema files"
    
    # List schema files
    print_info "Schema files:"
    find . -name "*.graphqls" -o -name "*.gql" | head -10 | while read file; do
        echo "  - $file"
    done
    
    # Check generated files
    if [ -f "internal/controller/graphql/generated.go" ]; then
        print_success "Generated file exists"
    else
        print_warning "Generated file missing"
    fi
    
    if [ -f "internal/controller/graphql/gql_model/models_gen.go" ]; then
        print_success "Models file exists"
    else
        print_warning "Models file missing"
    fi
}

# Check dependencies
check_dependencies() {
    print_header "Checking Dependencies"
    
    # Check if gqlgen is available
    if go list -m github.com/99designs/gqlgen &> /dev/null; then
        GQLGEN_VERSION=$(go list -m github.com/99designs/gqlgen | cut -d' ' -f2)
        print_success "gqlgen version: $GQLGEN_VERSION"
    else
        print_warning "gqlgen not found in go.mod"
    fi
    
    # Check tools dependencies
    TOOLS_DEPS=("golang.org/x/tools/go/packages" "github.com/urfave/cli/v2")
    for dep in "${TOOLS_DEPS[@]}"; do
        if go list -m "$dep" &> /dev/null; then
            VERSION=$(go list -m "$dep" | cut -d' ' -f2)
            print_success "$dep: $VERSION"
        else
            print_warning "$dep not found"
        fi
    done
}

# Check resolver files
check_resolver_files() {
    print_header "Checking Resolver Files"
    
    RESOLVER_FILES=$(ls internal/controller/graphql/*.resolvers.go 2>/dev/null | wc -l)
    if [ "$RESOLVER_FILES" -eq 0 ]; then
        print_warning "No resolver files found"
    else
        print_success "Found $RESOLVER_FILES resolver files"
        ls -la internal/controller/graphql/*.resolvers.go 2>/dev/null | while read line; do
            echo "  $line"
        done
    fi
    
    # Check for backup files
    BACKUP_FILES=$(ls internal/controller/graphql/*.resolvers.go.bak 2>/dev/null | wc -l)
    if [ "$BACKUP_FILES" -gt 0 ]; then
        print_warning "Found $BACKUP_FILES backup files (may indicate interrupted generation)"
        ls -la internal/controller/graphql/*.resolvers.go.bak 2>/dev/null | while read line; do
            echo "  $line"
        done
    fi
}

# Test build
test_build() {
    print_header "Testing Build"
    
    if go build ./internal/controller/graphql/... 2>/dev/null; then
        print_success "GraphQL package builds successfully"
    else
        print_error "GraphQL package build failed"
        print_info "Build errors:"
        go build ./internal/controller/graphql/... 2>&1 | head -10
        return 1
    fi
}

# Fix common issues
fix_issues() {
    print_header "Fixing Common Issues"
    
    # Update dependencies
    print_info "Updating dependencies..."
    go get github.com/99designs/gqlgen@latest
    go get golang.org/x/tools/go/packages@latest
    go get github.com/urfave/cli/v2@latest
    go mod tidy
    print_success "Dependencies updated"
    
    # Clean backup files
    if ls internal/controller/graphql/*.resolvers.go.bak 2>/dev/null; then
        print_info "Cleaning backup files..."
        rm -f internal/controller/graphql/*.resolvers.go.bak
        print_success "Backup files cleaned"
    fi
}

# Generate GraphQL code
generate_code() {
    print_header "Generating GraphQL Code"
    
    if make gqlgen; then
        print_success "GraphQL code generated successfully"
    else
        print_error "GraphQL generation failed"
        return 1
    fi
}

# Main function
main() {
    print_header "GraphQL Generation Doctor"
    echo "This script will diagnose and fix common GraphQL generation issues."
    echo ""
    
    # Parse command line arguments
    FIX_MODE=false
    GENERATE_MODE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --fix)
                FIX_MODE=true
                shift
                ;;
            --generate)
                GENERATE_MODE=true
                shift
                ;;
            --help)
                echo "Usage: $0 [--fix] [--generate] [--help]"
                echo ""
                echo "Options:"
                echo "  --fix       Fix common issues automatically"
                echo "  --generate  Generate GraphQL code after checks"
                echo "  --help      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Run checks
    check_directory
    check_go_environment
    check_graphql_files
    check_dependencies
    check_resolver_files
    
    # Test build
    if ! test_build; then
        if [ "$FIX_MODE" = true ]; then
            print_info "Build failed, attempting to fix..."
            fix_issues
            if ! test_build; then
                print_error "Build still fails after fixes. Manual intervention required."
                exit 1
            fi
        else
            print_info "Use --fix to attempt automatic fixes"
            exit 1
        fi
    fi
    
    # Generate if requested
    if [ "$GENERATE_MODE" = true ]; then
        generate_code
    fi
    
    print_header "Summary"
    print_success "All checks passed! GraphQL generation should work correctly."
    print_info "Use 'make gqlgen' to generate GraphQL code"
    print_info "Use 'make gqlgen-status' to check status"
    print_info "Use 'make help' for all available targets"
}

# Run main function
main "$@"
