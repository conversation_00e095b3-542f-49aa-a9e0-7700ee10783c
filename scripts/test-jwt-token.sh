#!/bin/bash

# Test JWT Token Script
# This script generates a JWT token and tests it against the GraphQL endpoint

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
SERVER_URL="http://localhost:8080/api/dex-agent/graphql"
ENV_FILE="env/local.env"

# Function to show help
show_help() {
    echo "JWT Token Tester for xbit-agent"
    echo "==============================="
    echo ""
    echo "This script generates a JWT token and tests it against the GraphQL endpoint."
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --server URL         GraphQL server URL (default: http://localhost:8080/api/dex-agent/graphql)"
    echo "  -f, --env-file FILE      Environment file to load (default: env/local.env)"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  # Test with default settings"
    echo "  $0"
    echo ""
    echo "  # Test with custom server URL"
    echo "  $0 -s http://localhost:3000/graphql"
    echo ""
    echo "  # Test with different environment"
    echo "  $0 -f env/docker.env"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--server)
            SERVER_URL="$2"
            shift 2
            ;;
        -f|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

print_info "Testing JWT token against GraphQL endpoint"
print_info "Server URL: $SERVER_URL"
print_info "Environment file: $ENV_FILE"
echo ""

# Generate JWT token
print_info "Step 1: Generating JWT token..."
TOKEN_OUTPUT=$(./scripts/generate-jwt.sh -f "$ENV_FILE" 2>/dev/null)

# Extract the raw token from the output
TOKEN=$(echo "$TOKEN_OUTPUT" | grep "=== Raw Token ===" -A 1 | tail -n 1)

if [[ -z "$TOKEN" ]]; then
    print_error "Failed to generate JWT token"
    exit 1
fi

print_success "JWT token generated successfully"
echo "Token: ${TOKEN:0:50}..."
echo ""

# Test the token with a simple GraphQL query
print_info "Step 2: Testing token with GraphQL query..."

# Simple introspection query to test authentication
QUERY='{"query": "{ __schema { queryType { name } } }"}'

# Make the request
print_info "Making GraphQL request..."
RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -H "X-Consumer-Username: xbit" \
    -X POST \
    -d "$QUERY" \
    "$SERVER_URL")

# Extract HTTP status code
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')

echo ""
print_info "Response received:"
echo "HTTP Status: $HTTP_CODE"
echo "Response Body: $RESPONSE_BODY"
echo ""

# Check the response
if [[ "$HTTP_CODE" == "200" ]]; then
    if echo "$RESPONSE_BODY" | grep -q '"queryType"'; then
        print_success "✓ JWT token is working correctly!"
        print_success "✓ GraphQL endpoint is accessible"
        print_success "✓ Authentication is successful"
    else
        print_warning "Token accepted but unexpected response format"
        print_info "This might be normal depending on your GraphQL schema"
    fi
else
    print_error "✗ Request failed with HTTP status: $HTTP_CODE"
    print_error "Response: $RESPONSE_BODY"
    exit 1
fi

echo ""
print_info "Test completed successfully!"
print_info "You can now use this token for your API testing."
