#!/bin/bash

# JWT Token Generator Script for xbit-agent
# This script provides a convenient way to generate JWT tokens for local testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
USER_ID=""
EMAIL="<EMAIL>"
ENV_FILE="env/local.env"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    echo "JWT Token Generator for xbit-agent"
    echo "=================================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --user-id USER_ID    User ID (UUID). If not provided, a random UUID will be generated"
    echo "  -e, --email EMAIL        User email (default: <EMAIL>)"
    echo "  -f, --env-file FILE      Environment file to load (default: env/local.env)"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  # Generate token with random user ID"
    echo "  $0"
    echo ""
    echo "  # Generate token with specific user ID and email"
    echo "  $0 -u 123e4567-e89b-12d3-a456-************ -e <EMAIL>"
    echo ""
    echo "  # Generate token with specific email only"
    echo "  $0 -e <EMAIL>"
    echo ""
    echo "  # Use different environment file"
    echo "  $0 -f env/docker.env"
    echo ""
    echo "Environment Files:"
    echo "  - env/local.env     (Local development)"
    echo "  - env/docker.env    (Docker environment)"
    echo "  - env/unstable.env  (Unstable environment)"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--user-id)
            USER_ID="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        -f|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Check if we're in the right directory
if [[ ! -f "go.mod" ]] || [[ ! -d "cmd/jwt-generator" ]]; then
    print_error "Please run this script from the xbit-agent project root directory"
    exit 1
fi

# Check if environment file exists
if [[ ! -f "$ENV_FILE" ]]; then
    print_error "Environment file not found: $ENV_FILE"
    print_info "Available environment files:"
    ls -la env/*.env 2>/dev/null || echo "No environment files found in env/ directory"
    exit 1
fi

print_info "Using environment file: $ENV_FILE"

# Load environment variables
set -a  # automatically export all variables
source "$ENV_FILE"
set +a  # stop automatically exporting

# Verify required environment variables
if [[ -z "$JWT_SECRET" ]]; then
    print_error "JWT_SECRET not found in environment file: $ENV_FILE"
    exit 1
fi

print_info "JWT Configuration:"
echo "  - Secret: ${JWT_SECRET:0:10}... (truncated for security)"
echo "  - Expires: ${JWT_EXPIRES_TIME:-7d}"
echo "  - Issuer: ${JWT_ISSUER:-xbit-agent}"

# Build the command
CMD="go run cmd/jwt-generator/main.go"

if [[ -n "$USER_ID" ]]; then
    CMD="$CMD -user-id=$USER_ID"
fi

if [[ -n "$EMAIL" ]]; then
    CMD="$CMD -email=$EMAIL"
fi

print_info "Generating JWT token..."
echo ""

# Run the JWT generator
eval $CMD

echo ""
print_success "JWT token generated successfully!"
print_info "You can now use this token for API testing with curl, Postman, or your GraphQL playground."
