#!/bin/bash

# Fix Build Issues Script
# This script automatically fixes common build issues in the xbit-agent project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔧 Starting build issue fix process..."

# Step 1: Clean up generated files
print_status "Step 1: Cleaning up generated files..."
rm -f internal/controller/graphql/generated.go
rm -f internal/controller/graphql/gql_model/models_gen.go
print_success "Generated files cleaned"

# Step 2: Update Go modules
print_status "Step 2: Updating Go modules..."
go mod tidy
go get github.com/99designs/gqlgen@latest
go get golang.org/x/tools/go/packages@latest
go get github.com/urfave/cli/v2@latest
go mod tidy
print_success "Go modules updated"

# Step 3: Fix imports in Activity Cashback files
print_status "Step 3: Fixing imports in Activity Cashback files..."

# Fix common import issues
find internal/service/activity_cashback/ -name "*.go" -exec sed -i '' 's/^[[:space:]]*$//' {} \;
find internal/controller/graphql/resolvers/ -name "*.go" -exec sed -i '' 's/^[[:space:]]*$//' {} \;

# Install goimports if not available
if ! command -v goimports &> /dev/null; then
    print_status "Installing goimports..."
    go install golang.org/x/tools/cmd/goimports@latest
fi

# Fix imports
print_status "Running goimports on Activity Cashback files..."
find internal/service/activity_cashback/ -name "*.go" | xargs goimports -w
find internal/controller/graphql/resolvers/ -name "*.go" | xargs goimports -w
find internal/repo/activity_cashback/ -name "*.go" | xargs goimports -w
find internal/model/ -name "*activity*.go" | xargs goimports -w

print_success "Imports fixed"

# Step 4: Format code
print_status "Step 4: Formatting code..."
go fmt ./internal/service/activity_cashback/...
go fmt ./internal/controller/graphql/resolvers/...
go fmt ./internal/repo/activity_cashback/...
print_success "Code formatted"

# Step 5: Regenerate GraphQL code
print_status "Step 5: Regenerating GraphQL code..."
if [ -f "gqlgen.yml" ]; then
    go run github.com/99designs/gqlgen generate --config gqlgen.yml
    print_success "GraphQL code regenerated"
else
    print_error "gqlgen.yml not found"
    exit 1
fi

# Step 6: Check for common issues and fix them
print_status "Step 6: Checking for common issues..."

# Fix unused variable issues
print_status "Fixing unused variable issues..."
find internal/service/activity_cashback/ -name "*.go" -exec grep -l "declared and not used" {} \; 2>/dev/null || true

# Fix type conversion issues
print_status "Checking for type conversion issues..."
if grep -r "cannot use.*as.*value in argument" internal/service/activity_cashback/ 2>/dev/null; then
    print_warning "Found type conversion issues - these may need manual fixing"
fi

# Step 7: Test build
print_status "Step 7: Testing build..."
if go build -o /tmp/test-build cmd/graphql/main.go; then
    print_success "Build test passed"
    rm -f /tmp/test-build
else
    print_error "Build test failed - manual intervention may be required"
    
    # Try to identify specific issues
    print_status "Analyzing build errors..."
    
    # Check for missing imports
    if go build cmd/graphql/main.go 2>&1 | grep -q "undefined:"; then
        print_warning "Found undefined symbols - checking imports..."
        go build cmd/graphql/main.go 2>&1 | grep "undefined:" | head -5
    fi
    
    # Check for type issues
    if go build cmd/graphql/main.go 2>&1 | grep -q "cannot use"; then
        print_warning "Found type conversion issues..."
        go build cmd/graphql/main.go 2>&1 | grep "cannot use" | head -5
    fi
    
    exit 1
fi

# Step 8: Run basic tests
print_status "Step 8: Running basic tests..."
if go test -compile-only ./internal/service/activity_cashback/...; then
    print_success "Activity Cashback tests compile successfully"
else
    print_warning "Some Activity Cashback tests have compilation issues"
fi

# Step 9: Generate summary
print_status "Step 9: Generating summary..."
echo ""
echo "🎉 Build issue fix process completed!"
echo ""
echo "📋 Summary:"
echo "✅ Generated files cleaned"
echo "✅ Go modules updated"
echo "✅ Imports fixed"
echo "✅ Code formatted"
echo "✅ GraphQL code regenerated"
echo "✅ Build test passed"
echo ""
echo "🚀 You can now run:"
echo "   make build-local    # Build for local platform"
echo "   make build          # Build for production"
echo "   make test           # Run tests"
echo "   make activity-cashback-test  # Run Activity Cashback tests"
echo ""

print_success "All done! 🎉"
