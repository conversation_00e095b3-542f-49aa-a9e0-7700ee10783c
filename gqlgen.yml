# Where are all the schema files located? globs are supported eg  src/**/*.graphqls
schema:
  - internal/controller/graphql/*.graphqls
  - internal/controller/graphql/schemas/*.gql

# Where should the generated server code go?
exec:
  filename: internal/controller/graphql/generated.go
  package: graphql

# Where should any generated models go?
model:
  filename: internal/controller/graphql/gql_model/models_gen.go
  package: gql_model

directives:
  auth:
    implementation: AuthDirective

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: internal/controller/graphql/
  package: graphql
  filename_template: "{name}.resolvers.go"

# Optional: turn on use `gqlgen:"fieldName"` tags in your models
# struct_tag: json

# Optional: turn on to use []Thing instead of []*Thing
# omit_slice_element_pointers: false

# Optional: turn on to omit Is<Name>() methods to interface and unions
# omit_interface_checks : true

# Optional: turn on to skip generation of ComplexityRoot struct content and Complexity function
# omit_complexity: false

# Optional: turn on to not generate any file notice comments in generated files
# omit_gqlgen_file_notice: false

# Optional: turn on to exclude the gqlgen version in the generated file notice. No effect if `omit_gqlgen_file_notice` is true.
# omit_gqlgen_version_in_file_notice: false

# Optional: turn off to make struct-type struct fields not use pointers
# e.g. type Thing struct { FieldA OtherThing } instead of { FieldA *OtherThing }
# struct_fields_always_pointers: true

# Optional: turn off to make resolvers return values instead of pointers for structs
# resolvers_always_return_pointers: true

# Optional: turn on to return pointers instead of values in unmarshalInput
# return_pointers_in_unmarshalinput: false

# Optional: wrap nullable input fields with Omittable
# nullable_input_omittable: true

# Optional: set to speed up generation time by not performing a final validation pass.
# skip_validation: true

# Optional: set to skip running `go mod tidy` when generating server code
# skip_mod_tidy: true

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
#  - "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"

# This section declares type mapping between the GraphQL and go type systems
models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Time:
    model:
      - github.com/99designs/gqlgen/graphql.Time
  Int64:
    model:
      - github.com/99designs/gqlgen/graphql.Int64
