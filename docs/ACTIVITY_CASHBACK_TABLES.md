erDiagram
    users ||--o{ user_tier_info : has
    users ||--o{ user_task_progress : tracks
    users ||--o{ daily_task_completions : completes
    users ||--o{ one_time_task_completions : completes
    users ||--o{ unlimited_task_completions : completes
    users ||--o{ progressive_task_completions : completes
    users ||--o{ manual_task_completions : completes
    users ||--o{ activity_cashback_claims : claims
    users ||--o{ activity_cashback : earns

    task_categories ||--o{ activity_tasks : contains
    activity_tasks ||--o{ user_task_progress : tracked_by
    activity_tasks ||--o{ daily_task_completions : completed_in
    activity_tasks ||--o{ one_time_task_completions : completed_in
    activity_tasks ||--o{ unlimited_task_completions : completed_in
    activity_tasks ||--o{ progressive_task_completions : completed_in
    activity_tasks ||--o{ manual_task_completions : completed_in

    tier_benefits ||--o{ user_tier_info : defines

    users {
        uuid id PK
        text email
        text invitation_code
        bigint agent_level_id
        timestamp level_grace_period_started_at
        timestamp level_upgraded_at
        timestamp first_transaction_at
    }

    task_categories {
        bigint id PK
        varchar name UK
        varchar display_name
        text description
        varchar icon
        int sort_order
        boolean is_active
    }

    activity_tasks {
        uuid id PK
        bigint category_id FK
        varchar name
        text description

        varchar frequency
        int points
        int max_completions
        varchar reset_period
        jsonb conditions
        varchar action_target
        varchar verification_method
        varchar external_link
        varchar task_icon
        varchar button_text
        boolean is_active
        timestamp start_date
        timestamp end_date
        int sort_order
        uuid created_by
        uuid updated_by
    }

    user_task_progress {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        varchar status
        int progress_value
        int target_value
        int completion_count
        int points_earned
        timestamp last_completed_at
        timestamp last_reset_at
        int streak_count
        jsonb metadata
    }

    user_tier_info {
        uuid user_id PK
        int current_tier
        int total_points
        int points_this_month
        decimal trading_volume_usd
        int active_days_this_month
        decimal cumulative_cashback_usd
        decimal claimable_cashback_usd
        decimal claimed_cashback_usd
        timestamp last_activity_date
        timestamp tier_upgraded_at
        timestamp monthly_reset_at
    }

    tier_benefits {
        bigint id PK
        int tier_level UK
        varchar tier_name
        int min_points
        decimal cashback_percentage
        decimal net_fee
        text benefits_description
        varchar tier_color
        varchar tier_icon
        boolean is_active
    }

    daily_task_completions {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        int points_awarded
        date completion_date
        timestamp completion_time
        jsonb verification_data
        inet ip_address
        text user_agent
    }

    one_time_task_completions {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        int points_awarded
        timestamp completion_date
        jsonb verification_data
        inet ip_address
        text user_agent
    }

    unlimited_task_completions {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        bigint sequence_number
        int points_awarded
        timestamp completion_date
        jsonb verification_data
        inet ip_address
        text user_agent
    }

    progressive_task_completions {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        int level_completed
        int total_progress
        int points_awarded
        timestamp completion_date
        jsonb verification_data
        jsonb milestone_data
        inet ip_address
        text user_agent
    }

    manual_task_completions {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        int points_awarded
        timestamp completion_date
        jsonb verification_data
        uuid approved_by FK
        timestamp approval_date
        text approval_notes
        varchar status
        inet ip_address
        text user_agent
        timestamp updated_at
    }

    activity_cashback_claims {
        uuid id PK
        uuid user_id FK
        varchar claim_type
        decimal total_amount_usd
        decimal total_amount_sol
        varchar transaction_hash
        varchar status
        timestamp claimed_at
        timestamp processed_at
        jsonb metadata
    }

    activity_cashback {
        uuid id PK
        uuid user_id FK
        varchar user_address
        varchar status
        bigint affiliate_transaction_id
        decimal sol_price_usd
        decimal cashback_amount_usd
        decimal cashback_amount_sol
        timestamp created_at
        timestamp updated_at
        timestamp claimed_at
    }