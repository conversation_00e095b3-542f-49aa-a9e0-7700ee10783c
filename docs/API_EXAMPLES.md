# Activity Cashback API Examples

This document provides practical examples for integrating with the Activity Cashback System APIs.

## GraphQL Examples

### Frontend Integration Examples

#### React/TypeScript Example

```typescript
import { gql, useQuery, useMutation } from '@apollo/client';

// GraphQL Queries
const GET_USER_DASHBOARD = gql`
  query GetUserDashboard {
    activityCashbackDashboard {
      success
      message
      data {
        userTierInfo {
          currentTier
          totalPoints
          pointsThisMonth
          claimableCashbackUsd
        }
        tierBenefit {
          tierName
          cashbackPercentage
          benefitsDescription
        }
        nextTier {
          tierName
          minPoints
        }
        pointsToNextTier
        userRank
        recentClaims {
          id
          totalAmountUsd
          status
          claimedAt
        }
      }
    }
  }
`;

const GET_TASK_CENTER = gql`
  query GetTaskCenter {
    taskCenter {
      success
      message
      data {
        categories {
          category {
            id
            name
            displayName
            icon
          }
          tasks {
            task {
              id
              name
              description
              points
              taskType
              frequency
              externalLink
            }
            progress {
              status
              progressValue
              targetValue
              progressPercentage
              canBeClaimed
              streakCount
            }
          }
        }
        completedToday
        pointsEarnedToday
        streakTasks {
          taskId
          streakCount
        }
      }
    }
  }
`;

// GraphQL Mutations
const COMPLETE_TASK = gql`
  mutation CompleteTask($input: CompleteTaskInput!) {
    completeTask(input: $input) {
      success
      message
      pointsAwarded
      tierUpgraded
      newTierLevel
    }
  }
`;

const CLAIM_TASK_REWARD = gql`
  mutation ClaimTaskReward($input: ClaimTaskRewardInput!) {
    claimTaskReward(input: $input) {
      success
      message
      pointsClaimed
    }
  }
`;

const CLAIM_CASHBACK = gql`
  mutation ClaimCashback($input: ClaimCashbackInput!) {
    claimCashback(input: $input) {
      success
      message
      claimId
      amountUsd
      amountSol
    }
  }
`;

// React Components
export const UserDashboard: React.FC = () => {
  const { data, loading, error } = useQuery(GET_USER_DASHBOARD);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const dashboard = data?.activityCashbackDashboard?.data;

  return (
    <div className="dashboard">
      <div className="tier-info">
        <h2>Tier: {dashboard?.tierBenefit?.tierName}</h2>
        <p>Points: {dashboard?.userTierInfo?.totalPoints}</p>
        <p>Rank: #{dashboard?.userRank}</p>
        <p>Cashback Rate: {dashboard?.tierBenefit?.cashbackPercentage}%</p>
        <p>Claimable: ${dashboard?.userTierInfo?.claimableCashbackUsd}</p>
      </div>
      
      {dashboard?.nextTier && (
        <div className="next-tier">
          <h3>Next Tier: {dashboard.nextTier.tierName}</h3>
          <p>Points needed: {dashboard.pointsToNextTier}</p>
          <div className="progress-bar">
            <div 
              className="progress" 
              style={{ 
                width: `${(dashboard.userTierInfo.totalPoints / dashboard.nextTier.minPoints) * 100}%` 
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export const TaskCenter: React.FC = () => {
  const { data, loading, refetch } = useQuery(GET_TASK_CENTER);
  const [completeTask] = useMutation(COMPLETE_TASK);
  const [claimReward] = useMutation(CLAIM_TASK_REWARD);

  const handleCompleteTask = async (taskId: string, verificationData?: any) => {
    try {
      const result = await completeTask({
        variables: {
          input: {
            taskId,
            verificationData: verificationData ? JSON.stringify(verificationData) : undefined
          }
        }
      });

      if (result.data?.completeTask?.success) {
        alert(`Task completed! Earned ${result.data.completeTask.pointsAwarded} points`);
        if (result.data.completeTask.tierUpgraded) {
          alert(`Congratulations! You've been upgraded to tier ${result.data.completeTask.newTierLevel}!`);
        }
        refetch();
      }
    } catch (error) {
      console.error('Failed to complete task:', error);
    }
  };

  const handleClaimReward = async (taskId: string) => {
    try {
      const result = await claimReward({
        variables: { input: { taskId } }
      });

      if (result.data?.claimTaskReward?.success) {
        alert(`Reward claimed! Earned ${result.data.claimTaskReward.pointsClaimed} points`);
        refetch();
      }
    } catch (error) {
      console.error('Failed to claim reward:', error);
    }
  };

  if (loading) return <div>Loading tasks...</div>;

  const taskCenter = data?.taskCenter?.data;

  return (
    <div className="task-center">
      <div className="stats">
        <p>Completed Today: {taskCenter?.completedToday}</p>
        <p>Points Earned Today: {taskCenter?.pointsEarnedToday}</p>
      </div>

      {taskCenter?.categories?.map((category) => (
        <div key={category.category.id} className="task-category">
          <h3>{category.category.displayName}</h3>
          
          {category.tasks.map((taskWithProgress) => (
            <TaskCard
              key={taskWithProgress.task.id}
              task={taskWithProgress.task}
              progress={taskWithProgress.progress}
              onComplete={handleCompleteTask}
              onClaim={handleClaimReward}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

interface TaskCardProps {
  task: any;
  progress: any;
  onComplete: (taskId: string, verificationData?: any) => void;
  onClaim: (taskId: string) => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, progress, onComplete, onClaim }) => {
  const handleTaskAction = () => {
    if (progress?.canBeClaimed) {
      onClaim(task.id);
    } else if (progress?.status === 'NOT_STARTED' || progress?.status === 'IN_PROGRESS') {
      // Handle different task types
      if (task.taskType === 'DAILY' && task.name === 'Daily Check-in') {
        onComplete(task.id);
      } else if (task.externalLink) {
        // Open external link for social media tasks
        window.open(task.externalLink, '_blank');
        // Then complete the task
        setTimeout(() => onComplete(task.id), 1000);
      } else {
        onComplete(task.id);
      }
    }
  };

  const getButtonText = () => {
    if (progress?.canBeClaimed) return 'Claim Reward';
    if (progress?.status === 'COMPLETED') return 'Completed';
    if (progress?.status === 'CLAIMED') return 'Claimed';
    return 'Complete Task';
  };

  const isDisabled = progress?.status === 'COMPLETED' || progress?.status === 'CLAIMED';

  return (
    <div className="task-card">
      <div className="task-info">
        <h4>{task.name}</h4>
        <p>{task.description}</p>
        <span className="points">+{task.points} points</span>
      </div>
      
      {progress && (
        <div className="task-progress">
          <div className="progress-bar">
            <div 
              className="progress" 
              style={{ width: `${progress.progressPercentage}%` }}
            />
          </div>
          <span>{progress.progressValue}/{progress.targetValue || 1}</span>
        </div>
      )}
      
      <button 
        onClick={handleTaskAction}
        disabled={isDisabled}
        className={`task-button ${progress?.canBeClaimed ? 'claim' : 'complete'}`}
      >
        {getButtonText()}
      </button>
    </div>
  );
};

// Cashback Claim Component
export const CashbackClaim: React.FC = () => {
  const [claimAmount, setClaimAmount] = useState('');
  const [claimCashback] = useMutation(CLAIM_CASHBACK);

  const handleClaim = async () => {
    try {
      const result = await claimCashback({
        variables: {
          input: {
            amountUsd: parseFloat(claimAmount)
          }
        }
      });

      if (result.data?.claimCashback?.success) {
        alert(`Cashback claim submitted! Claim ID: ${result.data.claimCashback.claimId}`);
        setClaimAmount('');
      }
    } catch (error) {
      console.error('Failed to claim cashback:', error);
    }
  };

  return (
    <div className="cashback-claim">
      <h3>Claim Cashback</h3>
      <input
        type="number"
        value={claimAmount}
        onChange={(e) => setClaimAmount(e.target.value)}
        placeholder="Amount in USD"
        step="0.01"
        min="0"
      />
      <button onClick={handleClaim} disabled={!claimAmount}>
        Claim Cashback
      </button>
    </div>
  );
};
```

### JavaScript/Fetch API Example

```javascript
// API Base URL
const API_BASE_URL = 'http://localhost:8080';

// GraphQL Client
class ActivityCashbackAPI {
  constructor(authToken) {
    this.authToken = authToken;
  }

  async graphqlRequest(query, variables = {}) {
    const response = await fetch(`${API_BASE_URL}/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      throw new Error(result.errors[0].message);
    }
    
    return result.data;
  }

  // Get user dashboard
  async getUserDashboard() {
    const query = `
      query {
        activityCashbackDashboard {
          success
          message
          data {
            userTierInfo {
              currentTier
              totalPoints
              claimableCashbackUsd
            }
            tierBenefit {
              tierName
              cashbackPercentage
            }
            pointsToNextTier
            userRank
          }
        }
      }
    `;

    return this.graphqlRequest(query);
  }

  // Get task center
  async getTaskCenter() {
    const query = `
      query {
        taskCenter {
          success
          message
          data {
            categories {
              category {
                name
                displayName
              }
              tasks {
                task {
                  id
                  name
                  points
                  taskType
                }
                progress {
                  status
                  progressPercentage
                  canBeClaimed
                }
              }
            }
            completedToday
            pointsEarnedToday
          }
        }
      }
    `;

    return this.graphqlRequest(query);
  }

  // Complete task
  async completeTask(taskId, verificationData = null) {
    const mutation = `
      mutation CompleteTask($input: CompleteTaskInput!) {
        completeTask(input: $input) {
          success
          message
          pointsAwarded
          tierUpgraded
        }
      }
    `;

    const variables = {
      input: {
        taskId,
        verificationData: verificationData ? JSON.stringify(verificationData) : null
      }
    };

    return this.graphqlRequest(mutation, variables);
  }

  // Claim cashback
  async claimCashback(amountUsd) {
    const mutation = `
      mutation ClaimCashback($input: ClaimCashbackInput!) {
        claimCashback(input: $input) {
          success
          message
          claimId
          amountUsd
        }
      }
    `;

    const variables = {
      input: { amountUsd }
    };

    return this.graphqlRequest(mutation, variables);
  }
}

// Usage example
const api = new ActivityCashbackAPI('your-jwt-token');

// Get dashboard data
api.getUserDashboard()
  .then(data => {
    console.log('Dashboard:', data.activityCashbackDashboard.data);
  })
  .catch(error => {
    console.error('Error:', error);
  });

// Complete a task
api.completeTask('task-uuid-here')
  .then(result => {
    if (result.completeTask.success) {
      console.log(`Task completed! Earned ${result.completeTask.pointsAwarded} points`);
    }
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

## REST API Examples

### Webhook Integration

```javascript
// Express.js webhook handler
app.post('/webhook/trading', (req, res) => {
  const { userId, tradeData } = req.body;
  
  // Send to Activity Cashback System
  fetch('http://localhost:8080/api/activity-cashback/webhook', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      event_type: 'trade_completed',
      user_id: userId,
      data: {
        volume: tradeData.volume,
        trade_type: tradeData.type,
        symbol: tradeData.symbol
      }
    })
  })
  .then(response => response.json())
  .then(result => {
    console.log('Event processed:', result);
  })
  .catch(error => {
    console.error('Webhook error:', error);
  });
  
  res.json({ success: true });
});
```

### Health Check Integration

```javascript
// Health check monitoring
async function checkActivityCashbackHealth() {
  try {
    const response = await fetch('http://localhost:8080/api/activity-cashback/health');
    const health = await response.json();
    
    if (health.status === 'healthy') {
      console.log('Activity Cashback System is healthy');
    } else {
      console.warn('Activity Cashback System health issue:', health);
    }
    
    return health;
  } catch (error) {
    console.error('Health check failed:', error);
    return { status: 'error', error: error.message };
  }
}

// Run health check every 5 minutes
setInterval(checkActivityCashbackHealth, 5 * 60 * 1000);
```

## Mobile App Integration (React Native)

```typescript
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Apollo Client setup
const httpLink = createHttpLink({
  uri: 'http://localhost:8080/graphql',
});

const authLink = setContext((_, { headers }) => {
  const token = getAuthToken(); // Your auth token function
  
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  }
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});

// React Native component
import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { useQuery, useMutation } from '@apollo/client';

export const MobileTaskCenter = () => {
  const { data, loading, refetch } = useQuery(GET_TASK_CENTER);
  const [completeTask] = useMutation(COMPLETE_TASK);

  const handleTaskComplete = async (taskId) => {
    try {
      const result = await completeTask({
        variables: { input: { taskId } }
      });
      
      if (result.data?.completeTask?.success) {
        // Show success notification
        Alert.alert('Success', `Earned ${result.data.completeTask.pointsAwarded} points!`);
        refetch();
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  const renderTask = ({ item }) => (
    <View style={styles.taskCard}>
      <Text style={styles.taskName}>{item.task.name}</Text>
      <Text style={styles.taskPoints}>+{item.task.points} points</Text>
      
      <TouchableOpacity
        style={styles.completeButton}
        onPress={() => handleTaskComplete(item.task.id)}
        disabled={item.progress?.status === 'COMPLETED'}
      >
        <Text style={styles.buttonText}>
          {item.progress?.status === 'COMPLETED' ? 'Completed' : 'Complete'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return <Text>Loading tasks...</Text>;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={data?.taskCenter?.data?.categories?.flatMap(cat => cat.tasks) || []}
        renderItem={renderTask}
        keyExtractor={(item) => item.task.id}
      />
    </View>
  );
};
```

## Error Handling Examples

```typescript
// Comprehensive error handling
const handleActivityCashbackError = (error: any) => {
  if (error.networkError) {
    console.error('Network error:', error.networkError);
    // Show network error message
    return 'Network connection failed. Please check your internet connection.';
  }
  
  if (error.graphQLErrors?.length > 0) {
    const graphQLError = error.graphQLErrors[0];
    
    switch (graphQLError.extensions?.code) {
      case 'UNAUTHENTICATED':
        // Redirect to login
        window.location.href = '/login';
        return 'Please log in to continue.';
        
      case 'FORBIDDEN':
        return 'You do not have permission to perform this action.';
        
      case 'TASK_NOT_FOUND':
        return 'Task not found or no longer available.';
        
      case 'TASK_ALREADY_COMPLETED':
        return 'This task has already been completed.';
        
      case 'INSUFFICIENT_BALANCE':
        return 'Insufficient balance for cashback claim.';
        
      default:
        return graphQLError.message || 'An unexpected error occurred.';
    }
  }
  
  return 'An unexpected error occurred. Please try again.';
};

// Usage in React component
const [completeTask] = useMutation(COMPLETE_TASK, {
  onError: (error) => {
    const errorMessage = handleActivityCashbackError(error);
    setErrorMessage(errorMessage);
  }
});
```

These examples provide a comprehensive guide for integrating with the Activity Cashback System from various frontend technologies and platforms.
