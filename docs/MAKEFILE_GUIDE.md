# Makefile Guide - Build & Development Commands

This guide covers all available Makefile targets and scripts for the xbit-agent project, with special focus on Activity Cashback system integration.

## 🚀 Quick Start Commands

### Basic Build Commands
```bash
# Build for local development (auto-detects platform)
make build-local

# Build for production (Linux/AMD64)
make build

# Build with debug information
make debug-build
```

### GraphQL Management
```bash
# Generate GraphQL code (with auto-fix)
make gqlgen

# Clean and regenerate GraphQL code
make gqlgen-clean

# Fix GraphQL generation issues
make gqlgen-fix

# Check if GraphQL files need regeneration
make gqlgen-check

# Debug GraphQL generation
make debug-gqlgen
```

## 🔧 Activity Cashback Specific Commands

### Development & Testing
```bash
# Build with Activity Cashback system
make activity-cashback-build

# Start development with Activity Cashback
make activity-cashback-dev

# Run Activity Cashback tests
make activity-cashback-test
```

## 🛠️ Troubleshooting & Maintenance

### Auto-Fix Scripts
```bash
# Run comprehensive build issue fix
./scripts/fix-build-issues.sh

# Auto-fix build errors with retry logic
./scripts/auto-fix-build.sh

# Fix Go imports and formatting
make fix-imports
make format-code
```

### Dependency Management
```bash
# Install/update all dependencies
make install-deps

# Update Go modules
go mod tidy
```

## 📋 Complete Command Reference

### Build Commands
| Command | Description | Auto-Fix |
|---------|-------------|----------|
| `make build` | Build for production (Linux/AMD64) | ✅ |
| `make build-local` | Build for local platform | ✅ |
| `make debug-build` | Build with verbose output | ❌ |

### GraphQL Commands
| Command | Description | Auto-Fix |
|---------|-------------|----------|
| `make gqlgen` | Generate GraphQL code | ✅ |
| `make gqlgen-check` | Check if generation needed | ✅ |
| `make gqlgen-clean` | Clean and regenerate | ✅ |
| `make gqlgen-fix` | Fix generation issues | ✅ |
| `make debug-gqlgen` | Debug generation process | ❌ |

### Activity Cashback Commands
| Command | Description | Auto-Fix |
|---------|-------------|----------|
| `make activity-cashback-build` | Build with AC system | ✅ |
| `make activity-cashback-dev` | Start AC development | ✅ |
| `make activity-cashback-test` | Run AC tests | ❌ |

### Code Quality Commands
| Command | Description |
|---------|-------------|
| `make fix-imports` | Fix Go imports |
| `make format-code` | Format code and fix imports |
| `make test` | Run all tests |
| `make test-verbose` | Run tests with verbose output |
| `make test-coverage` | Generate coverage report |

### Database Commands
| Command | Description |
|---------|-------------|
| `make db-apply` | Apply migrations (local) |
| `make db-apply-docker` | Apply migrations (docker) |
| `make db-diff` | Generate migration diff |

### Development Commands
| Command | Description |
|---------|-------------|
| `make run` | Run application |
| `make dev` | Run in development mode |
| `make dev-air` | Run with hot reload |

## 🔄 Auto-Fix Features

### Build Error Auto-Fix
The enhanced Makefile automatically handles common build errors:

1. **GraphQL Generation Issues**
   - Missing generated files
   - Outdated dependencies
   - Schema conflicts

2. **Import Issues**
   - Missing imports
   - Unused imports
   - Import formatting

3. **Type Conversion Errors**
   - String to UUID conversion
   - Pointer dereference issues
   - Interface mismatches

### Error Recovery Process
```
Build Attempt → Error Detection → Auto-Fix → Retry → Success
     ↓              ↓              ↓         ↓        ↓
   Failed    →  Analyze Error  →  Apply Fix → Build → Done
                      ↓              ↓
                 Known Issue?  →  Manual Fix Required
```

## 🚨 Common Issues & Solutions

### Issue 1: GraphQL Generation Fails
```bash
# Solution 1: Clean regeneration
make gqlgen-clean

# Solution 2: Fix dependencies
make gqlgen-fix

# Solution 3: Debug generation
make debug-gqlgen
```

### Issue 2: Build Fails with Type Errors
```bash
# Solution 1: Auto-fix script
./scripts/fix-build-issues.sh

# Solution 2: Manual fix
make fix-imports
make format-code
make build-local
```

### Issue 3: Activity Cashback Tests Fail
```bash
# Solution 1: Run specific test script
make activity-cashback-test

# Solution 2: Run with verbose output
go test -v ./internal/service/activity_cashback/...

# Solution 3: Check test dependencies
make install-deps
```

### Issue 4: Import/Format Issues
```bash
# Solution: Auto-fix imports and formatting
make format-code
```

## 📝 Script Details

### fix-build-issues.sh
Comprehensive script that:
- Cleans generated files
- Updates dependencies
- Fixes imports and formatting
- Regenerates GraphQL code
- Tests build
- Provides detailed feedback

### auto-fix-build.sh
Intelligent script that:
- Monitors build output
- Identifies specific errors
- Applies targeted fixes
- Retries build automatically
- Falls back to manual intervention

## 🎯 Best Practices

### Development Workflow
1. **Start Development**
   ```bash
   make activity-cashback-dev
   ```

2. **Make Changes**
   - Edit code
   - Auto-save triggers formatting

3. **Test Changes**
   ```bash
   make activity-cashback-test
   ```

4. **Build for Production**
   ```bash
   make build
   ```

### Troubleshooting Workflow
1. **Build Fails**
   ```bash
   make build-local  # Auto-fix enabled
   ```

2. **Still Fails**
   ```bash
   ./scripts/fix-build-issues.sh
   ```

3. **Complex Issues**
   ```bash
   make debug-build
   make debug-gqlgen
   ```

### Code Quality Workflow
1. **Before Commit**
   ```bash
   make format-code
   make test
   ```

2. **Before Push**
   ```bash
   make build
   make test-coverage
   ```

## 🔍 Monitoring & Debugging

### Build Monitoring
```bash
# Watch build process
make debug-build

# Monitor GraphQL generation
make debug-gqlgen

# Check system status
make activity-cashback-test
```

### Log Analysis
- Build logs: Check terminal output
- GraphQL logs: Look for generation errors
- Test logs: Review test failures

## 📚 Additional Resources

- [Activity Cashback System Documentation](ACTIVITY_CASHBACK_SYSTEM.md)
- [API Examples](API_EXAMPLES.md)
- [Database Schema](DATABASE_SCHEMA.md)
- [Testing Guide](../scripts/test_activity_cashback.sh)

---

**Need Help?**
- Run `make` to see all available targets
- Check `./scripts/` directory for additional tools
- Review error messages for specific guidance
