# Activity Cashback Summary API

## Overview

The Activity Cashback Summary API provides optimized data for the frontend Activity Cashback UI. This API returns all the essential information needed to display the user's ranking progress, trading volume, and cashback status in a single request.

## API Endpoint

**GraphQL Query**: `activityCashbackSummary`

**Authentication**: Required (JWT Bearer token)

## Query Structure

```graphql
query {
  activityCashbackSummary {
    success
    message
    data {
      # Current ranking info
      currentLevel
      currentLevelName
      nextLevel
      nextLevelName
      
      # Progress calculation
      currentScore
      totalScoreForNextLevel
      scoreRequiredToUpgrade
      progressPercentage
      
      # Trading volume
      accumulatedTradingVolumeUsd
      
      # Activity tracking
      activeLogonDays
      
      # Cashback information
      accumulatedCashbackUsd
      claimableCashbackUsd
      claimedCashbackUsd
      
      # UI styling info
      currentTierColor
      currentTierIcon
      nextTierColor
      nextTierIcon
    }
  }
}
```

## Response Fields

### Response Wrapper
- `success`: Bo<PERSON>an indicating if the request was successful
- `message`: String with status message
- `data`: ActivityCashbackSummary object (null if error)

### ActivityCashbackSummary Fields

#### Ranking Information
- `currentLevel`: Current user level (e.g., 3 for Lv3)
- `currentLevelName`: Display name of current level (e.g., "Lv3")
- `nextLevel`: Next level number (null if at max level)
- `nextLevelName`: Display name of next level (null if at max level)

#### Progress Calculation
- `currentScore`: User's current total points
- `totalScoreForNextLevel`: Total points required for next level (null if at max level)
- `scoreRequiredToUpgrade`: Points still needed to upgrade (null if at max level)
- `progressPercentage`: Progress percentage for UI progress bar (0-100)

#### Trading Volume
- `accumulatedTradingVolumeUsd`: Total MEME trading volume in USD

#### Activity Tracking
- `activeLogonDays`: Number of active days this month

#### Cashback Information
- `accumulatedCashbackUsd`: Total cashback earned (cumulative)
- `claimableCashbackUsd`: Available cashback that can be claimed
- `claimedCashbackUsd`: Total cashback already claimed

#### UI Styling (Optional)
- `currentTierColor`: Hex color code for current tier
- `currentTierIcon`: Icon/emoji for current tier
- `nextTierColor`: Hex color code for next tier
- `nextTierIcon`: Icon/emoji for next tier

## Example Response

```json
{
  "data": {
    "activityCashbackSummary": {
      "success": true,
      "message": "Summary data retrieved successfully",
      "data": {
        "currentLevel": 3,
        "currentLevelName": "Lv3",
        "nextLevel": 4,
        "nextLevelName": "Lv4",
        "currentScore": 2250,
        "totalScoreForNextLevel": 4000,
        "scoreRequiredToUpgrade": 1750,
        "progressPercentage": 64.3,
        "accumulatedTradingVolumeUsd": 969858.12,
        "activeLogonDays": 15,
        "accumulatedCashbackUsd": 18858.12,
        "claimableCashbackUsd": 150.50,
        "claimedCashbackUsd": 18707.62,
        "currentTierColor": "#8B5CF6",
        "currentTierIcon": "🔥",
        "nextTierColor": "#F59E0B",
        "nextTierIcon": "⭐"
      }
    }
  }
}
```

## Frontend Usage

This API is designed to provide all the data needed for the Activity Cashback UI in a single request:

1. **Current Ranking Display**: Use `currentLevel` and `currentLevelName`
2. **Next Ranking Display**: Use `nextLevel` and `nextLevelName`
3. **Progress Bar**: Use `progressPercentage` for the progress bar fill
4. **Score Display**: Show `currentScore` / `totalScoreForNextLevel`
5. **Upgrade Requirements**: Display `scoreRequiredToUpgrade` points needed
6. **Trading Volume**: Show `accumulatedTradingVolumeUsd`
7. **Activity Days**: Display `activeLogonDays`
8. **Cashback Summary**: Show all three cashback values

## Error Handling

If there's an error, the response will have:
- `success`: false
- `message`: Error description
- `data`: null

## Performance Notes

This API is optimized for frontend display and combines data from multiple sources:
- User tier information
- Tier benefit definitions
- Trading volume calculations
- Activity tracking
- Cashback calculations

The API automatically initializes user data if needed and handles edge cases like users at maximum tier level.
