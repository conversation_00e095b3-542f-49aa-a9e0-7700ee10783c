# Activity Cashback Database Schema

This document describes the database schema for the Activity Cashback System.

## Overview

The Activity Cashback System uses PostgreSQL as the primary database with the following main entities:

- **Task Categories**: Organize tasks into logical groups
- **Activity Tasks**: Define available tasks and their requirements
- **User Task Progress**: Track user progress on tasks
- **User Tier Info**: Store user tier and points information
- **Tier Benefits**: Define tier levels and their benefits
- **Task Completion Tables**: Log task completion events (split by frequency type for performance)
- **Activity Cashback Claims**: Track cashback claim requests

## Entity Relationship Diagram

```mermaid
erDiagram
    TASK_CATEGORIES ||--o{ ACTIVITY_TASKS : contains
    ACTIVITY_TASKS ||--o{ USER_TASK_PROGRESS : tracks
    ACTIVITY_TASKS ||--o{ DAILY_TASK_COMPLETIONS : logs
    ACTIVITY_TASKS ||--o{ ONE_TIME_TASK_COMPLETIONS : logs
    ACTIVITY_TASKS ||--o{ UNLIMITED_TASK_COMPLETIONS : logs
    ACTIVITY_TASKS ||--o{ PROGRESSIVE_TASK_COMPLETIONS : logs
    ACTIVITY_TASKS ||--o{ MANUAL_TASK_COMPLETIONS : logs
    USER_TIER_INFO ||--|| TIER_BENEFITS : has
    USER_TASK_PROGRESS }o--|| USERS : belongs_to
    USER_TIER_INFO }o--|| USERS : belongs_to
    DAILY_TASK_COMPLETIONS }o--|| USERS : belongs_to
    ONE_TIME_TASK_COMPLETIONS }o--|| USERS : belongs_to
    UNLIMITED_TASK_COMPLETIONS }o--|| USERS : belongs_to
    PROGRESSIVE_TASK_COMPLETIONS }o--|| USERS : belongs_to
    MANUAL_TASK_COMPLETIONS }o--|| USERS : belongs_to
    ACTIVITY_CASHBACK_CLAIMS }o--|| USERS : belongs_to
```

## Table Definitions

### task_categories

Stores task category information.

```sql
CREATE TABLE task_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_task_categories_name ON task_categories(name);
CREATE INDEX idx_task_categories_active ON task_categories(is_active);
CREATE INDEX idx_task_categories_sort ON task_categories(sort_order);
```

**Sample Data:**
```sql
INSERT INTO task_categories (name, display_name, description, icon, sort_order) VALUES
('daily', 'Daily Tasks', 'Complete these tasks daily to earn points', 'calendar', 1),
('community', 'Community Tasks', 'Engage with our community to earn rewards', 'users', 2),
('trading', 'Trading Tasks', 'Complete trading activities to earn points', 'trending-up', 3);
```

### activity_tasks

Stores task definitions and requirements.

```sql
CREATE TABLE activity_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id INTEGER REFERENCES task_categories(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    task_type VARCHAR(20) NOT NULL CHECK (task_type IN ('DAILY', 'ONE_TIME', 'UNLIMITED', 'PROGRESSIVE', 'MANUAL_UPDATE')),
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('DAILY', 'ONE_TIME', 'UNLIMITED', 'PROGRESSIVE', 'MANUAL')),
    points INTEGER NOT NULL DEFAULT 0,
    max_completions INTEGER,
    reset_period VARCHAR(20) CHECK (reset_period IN ('DAILY', 'WEEKLY', 'MONTHLY', 'NEVER')),
    conditions JSONB,
    action_target VARCHAR(200),
    verification_method VARCHAR(20) CHECK (verification_method IN ('AUTO', 'MANUAL', 'CLICK_VERIFY')),
    external_link TEXT,
    task_icon VARCHAR(255),
    button_text VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    sort_order INTEGER DEFAULT 0,
    created_by UUID,
    updated_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_activity_tasks_category ON activity_tasks(category_id);
CREATE INDEX idx_activity_tasks_type ON activity_tasks(task_type);
CREATE INDEX idx_activity_tasks_active ON activity_tasks(is_active);
CREATE INDEX idx_activity_tasks_dates ON activity_tasks(start_date, end_date);
CREATE INDEX idx_activity_tasks_sort ON activity_tasks(sort_order);
```

**Sample Data:**
```sql
INSERT INTO activity_tasks (category_id, name, description, task_type, frequency, points, reset_period, verification_method, sort_order) VALUES
(1, 'Daily Check-in', 'Check in daily to earn points', 'DAILY', 'DAILY', 5, 'DAILY', 'AUTO', 1),
(1, 'Complete 1 MEME Trade', 'Complete at least 1 MEME trade', 'DAILY', 'DAILY', 200, 'DAILY', 'AUTO', 2),
(2, 'Follow Twitter', 'Follow our Twitter account', 'ONE_TIME', 'ONE_TIME', 50, 'NEVER', 'CLICK_VERIFY', 1),
(3, 'Trading Points', 'Earn points based on trading volume', 'UNLIMITED', 'UNLIMITED', 0, 'NEVER', 'AUTO', 1);
```

### user_task_progress

Tracks user progress on tasks.

```sql
CREATE TABLE user_task_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID REFERENCES activity_tasks(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'NOT_STARTED' CHECK (status IN ('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'CLAIMED', 'EXPIRED')),
    progress_value INTEGER DEFAULT 0,
    target_value INTEGER,
    completion_count INTEGER DEFAULT 0,
    points_earned INTEGER DEFAULT 0,
    last_completed_at TIMESTAMP,
    last_reset_at TIMESTAMP,
    streak_count INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, task_id)
);

-- Indexes
CREATE INDEX idx_user_task_progress_user ON user_task_progress(user_id);
CREATE INDEX idx_user_task_progress_task ON user_task_progress(task_id);
CREATE INDEX idx_user_task_progress_status ON user_task_progress(status);
CREATE INDEX idx_user_task_progress_completed ON user_task_progress(last_completed_at);
CREATE INDEX idx_user_task_progress_streak ON user_task_progress(streak_count) WHERE streak_count > 0;
```

### user_tier_info

Stores user tier and points information.

```sql
CREATE TABLE user_tier_info (
    user_id UUID PRIMARY KEY,
    current_tier INTEGER NOT NULL DEFAULT 1,
    total_points INTEGER NOT NULL DEFAULT 0,
    points_this_month INTEGER NOT NULL DEFAULT 0,
    trading_volume_usd DECIMAL(20,8) NOT NULL DEFAULT 0,
    active_days_this_month INTEGER NOT NULL DEFAULT 0,
    cumulative_cashback_usd DECIMAL(20,8) NOT NULL DEFAULT 0,
    claimable_cashback_usd DECIMAL(20,8) NOT NULL DEFAULT 0,
    claimed_cashback_usd DECIMAL(20,8) NOT NULL DEFAULT 0,
    last_activity_date TIMESTAMP,
    tier_upgraded_at TIMESTAMP,
    monthly_reset_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_user_tier_info_tier ON user_tier_info(current_tier);
CREATE INDEX idx_user_tier_info_points ON user_tier_info(total_points DESC);
CREATE INDEX idx_user_tier_info_activity ON user_tier_info(last_activity_date);
CREATE INDEX idx_user_tier_info_monthly_reset ON user_tier_info(monthly_reset_at);
```

### tier_benefits

Defines tier levels and their benefits.

```sql
CREATE TABLE tier_benefits (
    id SERIAL PRIMARY KEY,
    tier_level INTEGER UNIQUE NOT NULL,
    tier_name VARCHAR(50) NOT NULL,
    min_points INTEGER NOT NULL,
    cashback_percentage DECIMAL(5,4) NOT NULL DEFAULT 0,
    net_fee DECIMAL(5,4) NOT NULL DEFAULT 0,
    benefits_description TEXT,
    tier_color VARCHAR(7),
    tier_icon VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_tier_benefits_level ON tier_benefits(tier_level);
CREATE INDEX idx_tier_benefits_points ON tier_benefits(min_points);
CREATE INDEX idx_tier_benefits_active ON tier_benefits(is_active);
```

**Sample Data:**
```sql
INSERT INTO tier_benefits (tier_level, tier_name, min_points, cashback_percentage, net_fee, benefits_description, tier_color, tier_icon) VALUES
(1, 'Lv1', 0, 0.0095, 0.0005, 'Basic tier with 0.95% cashback and 0.05% net fee', '#CD7F32', 'bronze-medal'),
(2, 'Lv2', 500, 0.0090, 0.0010, 'Silver tier with 0.90% cashback and 0.10% net fee', '#C0C0C0', 'silver-medal'),
(3, 'Lv3', 2000, 0.0085, 0.0015, 'Gold tier with 0.85% cashback and 0.15% net fee', '#FFD700', 'gold-medal'),
(4, 'Lv4', 10000, 0.0080, 0.0020, 'Platinum tier with 0.80% cashback and 0.20% net fee', '#E5E4E2', 'crown'),
(5, 'Lv5', 50000, 0.0075, 0.0025, 'Diamond tier with 0.75% cashback and 0.25% net fee', '#B9F2FF', 'diamond');
```

### Task Completion Tables (Frequency-Specific)

Task completion events are now stored in separate tables based on task frequency for optimal performance.

#### daily_task_completions

Stores completions for tasks with DAILY frequency, partitioned by date.

```sql
CREATE TABLE daily_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date DATE NOT NULL DEFAULT CURRENT_DATE,
    completion_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (completion_date);

-- Indexes
CREATE INDEX idx_daily_completions_user_date ON daily_task_completions (user_id, completion_date);
CREATE INDEX idx_daily_completions_task_date ON daily_task_completions (task_id, completion_date);
```

#### one_time_task_completions

Stores completions for tasks with ONE_TIME frequency, with unique constraints.

```sql
CREATE TABLE one_time_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_onetime_user_task UNIQUE (user_id, task_id)
);
```

#### unlimited_task_completions

Stores completions for tasks with UNLIMITED frequency, with sequence tracking.

```sql
CREATE TABLE unlimited_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    sequence_number BIGINT NOT NULL DEFAULT nextval('unlimited_completion_sequence'),
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_unlimited_completions_sequence ON unlimited_task_completions (user_id, task_id, sequence_number);
```

#### progressive_task_completions

Stores completions for tasks with PROGRESSIVE frequency, with level tracking.

```sql
CREATE TABLE progressive_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    level_completed INTEGER NOT NULL,
    total_progress INTEGER NOT NULL DEFAULT 0,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    milestone_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_progressive_user_task_level UNIQUE (user_id, task_id, level_completed)
);
```

#### manual_task_completions

Stores completions for tasks with MANUAL frequency, with approval workflow.

```sql
CREATE TABLE manual_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    approved_by UUID,
    approval_date TIMESTAMP,
    approval_notes TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_manual_completions_status ON manual_task_completions (status);
```

### activity_cashback_claims

Tracks cashback claim requests.

```sql
CREATE TABLE activity_cashback_claims (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    claim_type VARCHAR(20) NOT NULL CHECK (claim_type IN ('TRADING_CASHBACK', 'TASK_REWARD', 'TIER_BONUS', 'REFERRAL_BONUS')),
    total_amount_usd DECIMAL(20,8) NOT NULL,
    total_amount_sol DECIMAL(20,8) NOT NULL,
    transaction_hash VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')),
    claimed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_activity_cashback_claims_user ON activity_cashback_claims(user_id);
CREATE INDEX idx_activity_cashback_claims_status ON activity_cashback_claims(status);
CREATE INDEX idx_activity_cashback_claims_type ON activity_cashback_claims(claim_type);
CREATE INDEX idx_activity_cashback_claims_date ON activity_cashback_claims(claimed_at);
CREATE INDEX idx_activity_cashback_claims_tx ON activity_cashback_claims(transaction_hash) WHERE transaction_hash IS NOT NULL;
```

## Views

### user_dashboard_view

Provides a consolidated view of user dashboard data.

```sql
CREATE VIEW user_dashboard_view AS
SELECT
    uti.user_id,
    uti.current_tier,
    uti.total_points,
    uti.points_this_month,
    uti.claimable_cashback_usd,
    tb.tier_name,
    tb.cashback_percentage,
    tb.benefits_description,
    next_tb.tier_name as next_tier_name,
    next_tb.min_points as next_tier_min_points,
    GREATEST(0, next_tb.min_points - uti.total_points) as points_to_next_tier,
    RANK() OVER (ORDER BY uti.total_points DESC) as user_rank
FROM user_tier_info uti
LEFT JOIN tier_benefits tb ON uti.current_tier = tb.tier_level
LEFT JOIN tier_benefits next_tb ON next_tb.tier_level = uti.current_tier + 1
WHERE tb.is_active = true;
```

### task_progress_summary

Provides task progress summary by category.

```sql
CREATE VIEW task_progress_summary AS
SELECT
    tc.name as category_name,
    tc.display_name as category_display_name,
    COUNT(at.id) as total_tasks,
    COUNT(CASE WHEN at.is_active THEN 1 END) as active_tasks,
    AVG(CASE WHEN utp.status = 'COMPLETED' THEN 1.0 ELSE 0.0 END) as completion_rate
FROM task_categories tc
LEFT JOIN activity_tasks at ON tc.id = at.category_id
LEFT JOIN user_task_progress utp ON at.id = utp.task_id
GROUP BY tc.id, tc.name, tc.display_name, tc.sort_order
ORDER BY tc.sort_order;
```

## Triggers

### Update timestamps

```sql
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables with updated_at column
CREATE TRIGGER update_task_categories_updated_at BEFORE UPDATE ON task_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_activity_tasks_updated_at BEFORE UPDATE ON activity_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_task_progress_updated_at BEFORE UPDATE ON user_task_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_tier_info_updated_at BEFORE UPDATE ON user_tier_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tier_benefits_updated_at BEFORE UPDATE ON tier_benefits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_activity_cashback_claims_updated_at BEFORE UPDATE ON activity_cashback_claims FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Performance Considerations

### Indexing Strategy

1. **Primary Keys**: All tables use UUID primary keys for better distribution
2. **Foreign Keys**: All foreign key columns are indexed
3. **Query Patterns**: Indexes are created based on common query patterns
4. **Composite Indexes**: Used for multi-column queries

### Partitioning

1. **daily_task_completions**: Partitioned by completion_date for optimal performance
2. **activity_cashback_claims**: Consider partitioning by date for large datasets

### Performance Optimizations

1. **Frequency-Specific Tables**: Task completions are split by frequency type for optimal indexing and query performance
2. **Specialized Indexes**: Each table has indexes optimized for its specific access patterns
3. **Unique Constraints**: Prevent duplicate completions where appropriate (one-time, progressive levels)
4. **Sequence Tracking**: Unlimited tasks use sequences for efficient ordering

### Archival Strategy

1. **Daily Task Completions**: Archive partitions older than 1 year
2. **Other Completion Tables**: Archive records older than 2 years based on volume
2. **Inactive Tasks**: Soft delete with is_active flag
3. **Processed Claims**: Archive completed claims older than 1 year

## Backup and Recovery

### Daily Backups

```bash
# Full database backup
pg_dump -h localhost -U postgres -d xbit_agent > backup_$(date +%Y%m%d).sql

# Table-specific backups for critical data
pg_dump -h localhost -U postgres -d xbit_agent -t user_tier_info > user_tier_backup_$(date +%Y%m%d).sql
```

### Point-in-Time Recovery

Enable WAL archiving for point-in-time recovery:

```sql
-- postgresql.conf
wal_level = replica
archive_mode = on
archive_command = 'cp %p /path/to/archive/%f'
```

## Migration Scripts

### Initial Setup

```sql
-- Create database
CREATE DATABASE xbit_agent;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Run table creation scripts in order:
-- 1. task_categories
-- 2. tier_benefits
-- 3. activity_tasks
-- 4. user_tier_info
-- 5. user_task_progress
-- 6. task_completion_history
-- 7. activity_cashback_claims
-- 8. Views and triggers
```

This schema provides a robust foundation for the Activity Cashback System with proper indexing, constraints, and performance optimizations.
