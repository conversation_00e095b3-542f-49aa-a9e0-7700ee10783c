# GraphQL Code Generation Guide

## Overview

This project uses [gqlgen](https://github.com/99designs/gqlgen) to generate GraphQL server code from schema definitions. The Makefile has been enhanced with robust GraphQL generation targets that handle common issues automatically.

## Quick Start

```bash
# Generate GraphQL code (recommended)
make gqlgen

# Check generation status
make gqlgen-status

# Clean and regenerate everything
make gqlgen-clean

# Get help
make help
```

## Available Targets

### `make gqlgen`
**Primary target for GraphQL code generation**

Features:
- Automatically backs up existing resolver files
- Handles dependency issues automatically
- Restores resolver files after generation
- Verifies build success
- Robust error handling with fallback strategies

### `make gqlgen-clean`
**Clean slate regeneration**

- Backs up resolver files
- Removes all generated files
- Generates fresh code
- Restores resolver files
- Verifies build

### `make gqlgen-status`
**Check current status**

Shows:
- Generated file existence
- Schema files found
- Resolver files status
- Backup files status

### `make gqlgen-fix`
**Fix generation issues**

Alias for `make gqlgen` - uses the same robust approach.

### `make gqlgen-prepare`
**Prepare environment**

- Cleans Go module cache
- Installs required dependencies
- Updates go.mod and go.sum

## How It Works

### The Backup/Restore Strategy

The generation process uses a backup/restore strategy to handle the chicken-and-egg problem where resolver files import generated types that don't exist yet:

1. **Backup**: Move existing resolver files to `.bak` extensions
2. **Generate**: Run gqlgen without resolver files interfering
3. **Restore**: Move resolver files back
4. **Verify**: Ensure everything builds correctly

### Error Handling

The system includes multiple fallback strategies:

1. **First attempt**: Standard generation with dependency updates
2. **Second attempt**: Clean module cache and retry
3. **Third attempt**: Clean slate with fresh directory structure

### Files Managed

**Generated files** (auto-created):
- `internal/controller/graphql/generated.go` - Main GraphQL server code
- `internal/controller/graphql/gql_model/models_gen.go` - GraphQL model types

**Resolver files** (manually maintained):
- `internal/controller/graphql/schema.resolvers.go` - Main schema resolvers
- `internal/controller/graphql/activity_cashback.resolvers.go` - Activity cashback resolvers

**Schema files** (manually maintained):
- `internal/controller/graphql/schema.graphqls` - Main schema
- `internal/controller/graphql/schemas/*.gql` - Feature-specific schemas

## Common Issues and Solutions

### Issue: "undefined: MutationResolver"
**Solution**: Run `make gqlgen` - this generates the missing interface definitions.

### Issue: Module resolution errors
**Solution**: The Makefile handles this automatically with dependency updates and module cache cleaning.

### Issue: Import cycle or missing gql_model
**Solution**: Use `make gqlgen-clean` for a fresh start.

### Issue: Build fails after generation
**Solution**: Check resolver implementations match the schema. The build verification will catch this.

## Development Workflow

### Adding New GraphQL Features

1. **Update schema files** in `internal/controller/graphql/schemas/`
2. **Run generation**: `make gqlgen`
3. **Implement resolvers** in the generated resolver files
4. **Test**: `make test`

### Updating Existing Features

1. **Modify schema files**
2. **Regenerate**: `make gqlgen`
3. **Update resolver implementations** if needed
4. **Test**: `make test`

### Troubleshooting

1. **Check status**: `make gqlgen-status`
2. **Clean regenerate**: `make gqlgen-clean`
3. **Check build**: `go build ./internal/controller/graphql/...`

## Configuration

The generation is configured via `gqlgen.yml`:

```yaml
schema:
  - internal/controller/graphql/*.graphqls
  - internal/controller/graphql/schemas/*.gql

exec:
  filename: internal/controller/graphql/generated.go
  package: graphql

model:
  filename: internal/controller/graphql/gql_model/models_gen.go
  package: gql_model

resolver:
  layout: follow-schema
  dir: internal/controller/graphql/
  package: graphql
  filename_template: "{name}.resolvers.go"
```

## Best Practices

1. **Always use `make gqlgen`** instead of running gqlgen directly
2. **Check status** before and after generation with `make gqlgen-status`
3. **Commit generated files** to version control
4. **Test after generation** to ensure resolvers are correctly implemented
5. **Use `make gqlgen-clean`** when in doubt or facing persistent issues

## Integration with CI/CD

The robust error handling makes these targets suitable for CI/CD pipelines:

```bash
# In CI/CD pipeline
make gqlgen-status  # Check current state
make gqlgen         # Generate if needed
make test           # Verify everything works
```

The targets will exit with appropriate error codes if generation fails.
