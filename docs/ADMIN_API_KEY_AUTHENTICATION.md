# Admin API Key Authentication Implementation

This document describes the implementation of API Key authentication for Activity Cashback Admin APIs.

## Overview

The admin APIs for Activity Cashback now use API Key authentication instead of simple header tokens. This provides better security and aligns with the xbit-admin client's authentication mechanism.

## Changes Made

### 1. Configuration Updates

#### New Configuration Structure
- Added `config/admin.go` with `Admin` struct containing `InternalAPIKey` field
- Updated `config/config.go` to include `Admin` configuration
- Added `admin` section to `config.yaml` with `internal-api-key` field

#### Environment Variables
Added `INTERNAL_API_KEY` to all environment files:
- `env/local.env`: `local-internal-api-key-for-development-only`
- `env/docker.env`: `docker-internal-api-key-change-in-production`
- `env/unstable.env`: `unstable-internal-api-key-change-in-production`
- `env/production.env`: Empty (must be set in production)

### 2. Authentication Middleware

#### New API Key Middleware
- Created `internal/controller/graphql/middlewares/api_key.go`
- Implements `ApiKeyAuth()` middleware for Gin routes
- Validates API key from multiple header sources:
  - `x-api-key`
  - `X-API-Key`
  - `Authorization: Bearer <key>`
- Sets admin context for GraphQL resolvers

#### Updated Activity Cashback Middleware
- Modified `internal/middleware/activity_cashback.go`
- Updated `isAdmin()` method to use API key validation
- Replaced simple token check with proper API key authentication

### 3. GraphQL Authentication

#### New Admin Directive
- Added `@adminAuth` directive to GraphQL schema
- Created `AdminAuthDirective` function in `internal/controller/graphql/directive.go`
- Validates API key authentication for admin-only operations
- Updated `gqlgen.yml` to register the new directive

#### Schema Updates
- Added `@adminAuth` directive definition to `schema.graphqls`
- Updated all admin mutations to use `@adminAuth` instead of `@auth`
- Added new admin-specific queries and mutations

### 4. New Admin GraphQL Operations

#### Admin Queries
- `adminGetAllTasks`: Get all tasks (admin only)
- `adminGetTaskCompletionStats`: Get task completion statistics
- `adminGetUserActivityStats`: Get user activity statistics  
- `adminGetTierDistribution`: Get tier distribution statistics
- `adminGetTopUsers`: Get top users by points

#### Admin Mutations
- `adminResetDailyTasks`: Reset all daily tasks
- `adminResetWeeklyTasks`: Reset all weekly tasks
- `adminResetMonthlyTasks`: Reset all monthly tasks
- `adminRecalculateAllUserTiers`: Recalculate all user tiers
- `adminSeedInitialTasks`: Seed initial tasks

#### New GraphQL Types
- `AdminStatsInput`: Input for statistics queries
- `AdminTaskCompletionStatsResponse`: Response for task completion stats
- `AdminUserActivityStatsResponse`: Response for user activity stats
- `AdminTierDistributionResponse`: Response for tier distribution
- Various supporting types for statistics data

### 5. Resolver Implementation

#### Updated Resolvers
- All existing admin resolvers updated to use new authentication
- Added implementations for new admin queries and mutations
- Updated `internal/controller/graphql/activity_cashback.resolvers.go`
- Added comprehensive admin resolver methods in `internal/controller/graphql/resolvers/admin_activity_cashback.go`

## Usage

### For xbit-admin Client

The xbit-admin client should use the following configuration:

```javascript
this.client = new GraphQLClient(Configs.graphQlTradingUrl, {
  headers: {
    'x-api-key': Configs.internalApiKey,
  },
})
```

### API Key Headers

The system accepts API keys from these headers (in order of precedence):
1. `x-api-key: <api-key>`
2. `X-API-Key: <api-key>`
3. `Authorization: Bearer <api-key>`

### Environment Configuration

Set the `INTERNAL_API_KEY` environment variable:

```bash
# Development
INTERNAL_API_KEY=your-development-api-key

# Production (use a strong, randomly generated key)
INTERNAL_API_KEY=your-production-api-key
```

## Security Considerations

1. **API Key Storage**: Store API keys securely and never commit them to version control
2. **Key Rotation**: Implement regular API key rotation in production
3. **Environment Separation**: Use different API keys for different environments
4. **Access Logging**: Monitor API key usage for security auditing
5. **HTTPS Only**: Always use HTTPS in production to protect API keys in transit

## Testing

The implementation has been tested and compiles successfully. All admin operations now require valid API key authentication.

## Migration Notes

- Existing admin clients must be updated to include the API key header
- The old `X-Admin-Token` header is no longer supported
- All admin GraphQL operations now use `@adminAuth` directive
- REST admin endpoints continue to work with the updated authentication
