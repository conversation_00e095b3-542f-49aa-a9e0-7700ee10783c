# Activity Cashback System Documentation

## Overview

The Activity Cashback System is a comprehensive gamification and rewards system designed to incentivize user engagement through tasks, tier progression, and cashback rewards.

## Architecture

### Core Components

1. **Models**: Database entities and business logic
2. **Repositories**: Data access layer
3. **Services**: Business logic layer
4. **GraphQL Resolvers**: API layer
5. **Background Jobs**: Automated task processing
6. **Admin APIs**: Management interface

### Key Features

- ✅ Task Management (Daily, One-time, Progressive, Unlimited)
- ✅ Tier System with Benefits
- ✅ Points and Cashback Rewards
- ✅ User Progress Tracking
- ✅ Automated Task Reset
- ✅ Background Job Processing
- ✅ Admin Management Interface
- ✅ GraphQL API
- ✅ Comprehensive Testing

## Getting Started

### Prerequisites

- Go 1.19+
- PostgreSQL 13+
- Redis (optional, for caching)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd xbit-agent
```

2. **Install dependencies**
```bash
go mod tidy
```

3. **Set up database**
```bash
# Create database
createdb xbit_agent

# Run migrations (if available)
# migrate -path migrations -database "postgres://user:password@localhost/xbit_agent?sslmode=disable" up
```

4. **Configure environment variables**
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=xbit_agent
export DB_USER=postgres
export DB_PASSWORD=your_password
```

5. **Initialize the system**
```go
import "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"

// Initialize the system
ctx := context.Background()
err := activity_cashback.InitializeGlobalSystem(ctx)
if err != nil {
    log.Fatal("Failed to initialize Activity Cashback System:", err)
}
```

## Usage

### GraphQL API

#### User Operations

**Get User Dashboard**
```graphql
query {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        claimableCashbackUsd
      }
      tierBenefit {
        tierName
        cashbackPercentage
      }
      pointsToNextTier
      userRank
    }
  }
}
```

**Get Task Center**
```graphql
query {
  taskCenter {
    success
    message
    data {
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            id
            name
            points
            taskType
          }
          progress {
            status
            progressValue
            progressPercentage
          }
        }
      }
      completedToday
      pointsEarnedToday
    }
  }
}
```

**Complete a Task**
```graphql
mutation {
  completeTask(input: {
    taskId: "task-uuid"
    verificationData: "{\"volume\": 100.0, \"trade_type\": \"MEME\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
    newTierLevel
  }
}
```

**Claim Task Reward**
```graphql
mutation {
  claimTaskReward(input: {
    taskId: "task-uuid"
  }) {
    success
    message
    pointsClaimed
  }
}
```

**Claim Cashback**
```graphql
mutation {
  claimCashback(input: {
    amountUsd: 10.0
  }) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

#### Admin Operations

**Create Task**
```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "New Daily Task"
    description: "Complete this task daily"
    taskType: DAILY
    frequency: DAILY
    points: 10
    sortOrder: 1
  }) {
    id
    name
    points
    isActive
  }
}
```

**Update Task**
```graphql
mutation {
  updateTask(input: {
    id: "task-uuid"
    name: "Updated Task Name"
    points: 15
    isActive: true
  }) {
    id
    name
    points
    isActive
  }
}
```

### REST API Endpoints

**Health Check**
```bash
GET /api/activity-cashback/health
```

**System Status**
```bash
GET /api/activity-cashback/status
```

**Process Webhook**
```bash
POST /api/activity-cashback/webhook
Content-Type: application/json

{
  "event_type": "trade_completed",
  "user_id": "user-uuid",
  "data": {
    "volume": 1000.0,
    "trade_type": "MEME",
    "symbol": "BTC/USDT"
  }
}
```

**Admin: Force Task Reset**
```bash
POST /api/activity-cashback/admin/reset/daily
X-Admin-Token: admin-secret-token
```

**Admin: Recalculate Tiers**
```bash
POST /api/activity-cashback/admin/recalculate-tiers
X-Admin-Token: admin-secret-token
```

## Task Types

### Daily Tasks
- Reset every day at UTC 00:00
- Examples: Daily check-in, Complete 1 trade, Check market conditions

### One-time Tasks
- Can only be completed once per user
- Examples: Follow Twitter, Join Telegram

### Progressive Tasks
- Accumulate progress over time
- Examples: Accumulated trading volume milestones

### Unlimited Tasks
- Can be completed multiple times
- Examples: Invite friends, Trading points

## Tier System

### Tier Benefits
- **Tier 1**: 0.1% cashback, Basic benefits
- **Tier 2**: 0.2% cashback, Enhanced benefits
- **Tier 3**: 0.3% cashback, Premium benefits
- **Tier 4**: 0.5% cashback, VIP benefits

### Tier Progression
Users advance tiers based on total points earned:
- Tier 1: 0+ points
- Tier 2: 500+ points
- Tier 3: 2000+ points
- Tier 4: 10000+ points

## Background Jobs

### Scheduled Jobs
- **Daily Task Reset**: Runs at UTC 00:00 daily
- **Weekly Task Reset**: Runs on Mondays at UTC 00:00
- **Monthly Task Reset**: Runs on 1st of month at UTC 00:00
- **Tier Upgrade Check**: Runs every 30 minutes
- **Cashback Processing**: Runs every 5 minutes

### Event Processing
- **Trading Events**: Process trade completions
- **User Login**: Process daily check-ins
- **Market Check**: Process market condition checks

## Testing

### Run All Tests
```bash
chmod +x scripts/test_activity_cashback.sh
./scripts/test_activity_cashback.sh
```

### Run Specific Tests
```bash
# Unit tests
go test ./internal/service/activity_cashback/...

# Integration tests
go test ./internal/service/activity_cashback/... -tags=integration

# Benchmark tests
go test -bench=. ./internal/service/activity_cashback/...

# Race condition tests
go test -race ./internal/service/activity_cashback/...
```

## Monitoring and Observability

### Health Checks
- System health: `/api/activity-cashback/health`
- Background jobs status: `/api/activity-cashback/status`

### Metrics
- Task completion rates
- User tier distribution
- Cashback claim statistics
- System performance metrics

### Logging
All operations are logged with structured logging using Zap:
- Task completions
- Tier upgrades
- Cashback claims
- System errors

## Security Considerations

### Authentication
- All user operations require valid JWT token
- Admin operations require additional admin token

### Data Validation
- Input validation on all API endpoints
- SQL injection prevention through parameterized queries
- XSS protection in GraphQL responses

### Rate Limiting
- Implement rate limiting for task completions
- Prevent abuse of cashback claims

## Performance Optimization

### Database Optimization
- Proper indexing on frequently queried columns
- Connection pooling
- Query optimization

### Caching
- Redis caching for frequently accessed data
- In-memory caching for static data

### Background Processing
- Asynchronous task processing
- Queue-based job processing

## Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Check database connectivity
psql -h localhost -U postgres -d xbit_agent -c "SELECT 1;"
```

**Background Jobs Not Running**
```bash
# Check system status
curl http://localhost:8080/api/activity-cashback/status
```

**Task Completion Failures**
- Check user authentication
- Verify task availability
- Check verification data format

### Debug Mode
Enable debug logging by setting:
```bash
export LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
