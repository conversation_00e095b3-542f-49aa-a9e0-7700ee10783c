# JWT Token Generator for Local Testing

This document provides instructions on how to use the JWT Token Generator to create Bearer tokens for local testing.

## Overview

The JWT Token Generator is a CLI tool that helps you quickly create JWT Bearer tokens for API testing without needing to:
- Request tokens from frontend colleagues
- Create multiple accounts to get tokens
- Perform complex authentication processes

## Usage

### 1. Using Makefile (Recommended)

#### Create token with default information:
```bash
make jwt-token
```

#### View instructions:
```bash
make jwt-token-help
```

#### Create token with custom information:
```bash
# Only with custom email
make jwt-token-custom EMAIL=<EMAIL>

# With both User ID and custom email
make jwt-token-custom USER_ID=123e4567-e89b-12d3-a456-************ EMAIL=<EMAIL>
```

### 2. Using script directly

#### Create token with default information:
```bash
./scripts/generate-jwt.sh
```

#### Create token with custom information:
```bash
# With custom email
./scripts/generate-jwt.sh -e <EMAIL>

# With User ID and custom email
./scripts/generate-jwt.sh -u 123e4567-e89b-12d3-a456-************ -e <EMAIL>

# Use different environment file
./scripts/generate-jwt.sh -f env/docker.env
```

### 3. Using Go command directly

```bash
# Create token with default information
go run cmd/jwt-generator/main.go

# With custom information
go run cmd/jwt-generator/main.go -user-id=123e4567-e89b-12d3-a456-************ -email=<EMAIL>

# View help
go run cmd/jwt-generator/main.go -help
```

## Sample Output

When run successfully, you will see output like this:

```
Generated new User ID: a1b2c3d4-e5f6-7890-abcd-ef1234567890

=== JWT Token Generated Successfully ===
User ID: a1b2c3d4-e5f6-7890-abcd-ef1234567890
Email: <EMAIL>
Issuer: xbit-agent-local
Expires: 7d

=== Bearer Token ===
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

=== For curl/Postman ===
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

=== Raw Token ===
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

=== Token Validation ===
✓ Token is valid
✓ User ID from token: a1b2c3d4-e5f6-7890-abcd-ef1234567890
✓ Email from token: <EMAIL>
✓ Expires at: 2024-01-08 15:30:45
```

## Using the token

### With curl:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"query": "{ users { id email } }"}' \
     http://localhost:8080/api/dex-agent/graphql
```

### With Postman:
1. Open Postman
2. Go to the "Authorization" tab
3. Select "Bearer Token"
4. Paste the token into the "Token" field

### With GraphQL Playground:
1. Open GraphQL Playground at `http://localhost:8080/api/dex-agent/playground`
2. Go to "HTTP HEADERS" in the bottom left corner
3. Add header:
```json
{
  "Authorization": "Bearer YOUR_TOKEN_HERE"
}
```

## Environment Files

The tool supports different environment files:

- `env/local.env` - Local development (default)
- `env/docker.env` - Docker environment
- `env/unstable.env` - Unstable environment
- `env/staging.env` - Staging environment
- `env/production.env` - Production environment

## Security Notes

- Tokens are created using the same JWT secret as the application
- Tokens have expiration time according to configuration in environment file
- Do not use production tokens for testing
- Tokens contain User ID and Email information in claims

## Troubleshooting

### Error "JWT_SECRET not found"
Ensure the environment file contains `JWT_SECRET`:
```bash
# Check env file
cat env/local.env | grep JWT_SECRET
```

### Error "Invalid User ID format"
User ID must be a valid UUID:
```bash
# Example valid UUID
123e4567-e89b-12d3-a456-************
```

### Error "Please run this script from the xbit-agent project root directory"
Ensure you are in the project root directory:
```bash
# Check
ls -la | grep go.mod
```
