# JWT Token Generator - Quick Start

Quickly create JWT Bearer tokens for local testing without needing to request tokens from frontend or create multiple accounts.

## Quick Usage

### 1. Create token with default information
```bash
make jwt-token
```

### 2. Create token with custom email
```bash
make jwt-token-custom EMAIL=<EMAIL>
```

### 3. Create token with custom User ID and email
```bash
make jwt-token-custom USER_ID=123e4567-e89b-12d3-a456-************ EMAIL=<EMAIL>
```

### 4. View detailed instructions
```bash
make jwt-token-help
```

## Sample Output
```
=== JWT Token Generated Successfully ===
User ID: f90b2ad1-320a-4cb4-9ff0-7b3caf470082
Email: <EMAIL>
Issuer: xbit-agent-local
Expires: 7d

=== Bearer Token ===
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

=== For curl/Postman ===
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

=== Raw Token ===
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Using the token

### With curl:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"query": "{ users { id email } }"}' \
     http://localhost:8080/api/dex-agent/graphql
```

### With GraphQL Playground:
1. Open `http://localhost:8080/api/dex-agent/playground`
2. Add header:
```json
{
  "Authorization": "Bearer YOUR_TOKEN_HERE"
}
```

## Detailed Documentation
See [docs/JWT_TOKEN_GENERATOR.md](docs/JWT_TOKEN_GENERATOR.md) for more details and other options.
