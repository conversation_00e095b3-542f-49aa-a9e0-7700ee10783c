#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_AGENCY_USER:$POSTGRES_AGENCY_PASS@$POSTGRES_AGENCY_HOST:$POSTGRES_AGENCY_PORT/agent?sslmode=${POSTGRES_AGENCY_SSL_MODE:-disable}"
atlas migrate apply --url $DATABASE_URL --dir file://migrations


# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
