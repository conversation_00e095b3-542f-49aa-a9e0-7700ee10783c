# Activity Cashback System

A comprehensive gamification and rewards system designed to incentivize user engagement through tasks, tier progression, and cashback rewards.

## 🚀 Features

- ✅ **Task Management**: Daily, One-time, Progressive, and Unlimited tasks
- ✅ **Tier System**: 4-tier progression system with increasing benefits
- ✅ **Points & Rewards**: Earn points for completing tasks and activities
- ✅ **Cashback System**: Claim cashback rewards based on tier level
- ✅ **Progress Tracking**: Real-time tracking of user progress and streaks
- ✅ **Admin Interface**: Comprehensive admin tools for task management
- ✅ **GraphQL API**: Modern API with type-safe queries and mutations
- ✅ **Background Jobs**: Automated task resets and tier calculations
- ✅ **Comprehensive Testing**: Unit tests, integration tests, and benchmarks

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [API Documentation](#api-documentation)
- [Database Schema](#database-schema)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)

## 🏃‍♂️ Quick Start

### Prerequisites

- Go 1.19+
- PostgreSQL 13+
- Redis (optional)

### Installation

1. **Clone and setup**
```bash
git clone <repository-url>
cd xbit-agent
go mod tidy
```

2. **Database setup**
```bash
createdb xbit_agent
# Run migrations (see docs/DATABASE_SCHEMA.md)
```

3. **Environment configuration**
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=xbit_agent
export DB_USER=postgres
export DB_PASSWORD=your_password
```

4. **Initialize the system**
```go
import "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initialize"

// In your main.go
err := initialize.InitializeActivityCashbackSystem()
if err != nil {
    log.Fatal("Failed to initialize Activity Cashback System:", err)
}

// Setup routes
initialize.SetupActivityCashbackRoutes(router)
```

5. **Seed initial data**
```go
err := initialize.SeedActivityCashbackData()
if err != nil {
    log.Fatal("Failed to seed initial data:", err)
}
```

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GraphQL API   │    │   REST API      │    │   Background    │
│   Resolvers     │    │   Endpoints     │    │   Jobs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Service Layer                      │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
         │  │    Task     │  │    Tier     │  │ Cashback │ │
         │  │ Management  │  │ Management  │  │   Claim  │ │
         │  └─────────────┘  └─────────────┘  └──────────┘ │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │             Repository Layer                    │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
         │  │    Task     │  │    User     │  │  Claim   │ │
         │  │    Repo     │  │    Repo     │  │   Repo   │ │
         │  └─────────────┘  └─────────────┘  └──────────┘ │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │                Database                         │
         │              PostgreSQL                         │
         └─────────────────────────────────────────────────┘
```

### Key Components

- **Models**: Database entities and business logic
- **Repositories**: Data access layer with interfaces
- **Services**: Business logic and orchestration
- **GraphQL Resolvers**: API layer for frontend integration
- **Background Jobs**: Automated processing and scheduling
- **Admin APIs**: Management interface for administrators

## 📚 API Documentation

### GraphQL Endpoints

**Main Endpoint**: `POST /graphql`

#### User Operations

```graphql
# Get user dashboard
query {
  activityCashbackDashboard {
    success
    data {
      userTierInfo { currentTier totalPoints }
      tierBenefit { tierName cashbackPercentage }
      pointsToNextTier
      userRank
    }
  }
}

# Complete a task
mutation {
  completeTask(input: { taskId: "uuid", verificationData: "{}" }) {
    success
    pointsAwarded
    tierUpgraded
  }
}

# Claim cashback
mutation {
  claimCashback(input: { amountUsd: 10.0 }) {
    success
    claimId
    amountUsd
  }
}
```

#### Admin Operations

```graphql
# Create task
mutation {
  createTask(input: {
    categoryId: "1"
    name: "New Task"
    taskType: DAILY
    points: 10
  }) {
    id
    name
    points
  }
}
```

### REST Endpoints

- `GET /api/activity-cashback/health` - Health check
- `GET /api/activity-cashback/status` - System status
- `POST /api/activity-cashback/webhook` - Process external events
- `POST /api/activity-cashback/admin/reset/:type` - Force task reset (admin)

## 🗄️ Database Schema

### Core Tables

- **task_categories**: Task organization
- **activity_tasks**: Task definitions
- **user_task_progress**: Progress tracking
- **user_tier_info**: User tier and points
- **tier_benefits**: Tier level definitions
- **task_completion_history**: Completion logs
- **activity_cashback_claims**: Cashback requests

See [docs/DATABASE_SCHEMA.md](docs/DATABASE_SCHEMA.md) for detailed schema.

## 🧪 Testing

### Run All Tests

```bash
chmod +x scripts/test_activity_cashback.sh
./scripts/test_activity_cashback.sh
```

### Individual Test Suites

```bash
# Unit tests
go test ./internal/service/activity_cashback/...

# Integration tests
go test ./internal/service/activity_cashback/... -tags=integration

# Benchmark tests
go test -bench=. ./internal/service/activity_cashback/...

# Race condition tests
go test -race ./internal/service/activity_cashback/...
```

### Test Coverage

The test suite includes:
- ✅ Unit tests for all services
- ✅ Integration tests for complete workflows
- ✅ GraphQL resolver tests
- ✅ Repository layer tests
- ✅ Benchmark tests for performance
- ✅ Race condition tests for concurrency

## 🚀 Deployment

### Docker Deployment

```dockerfile
# Dockerfile
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_NAME=xbit_agent
      - DB_USER=postgres
      - DB_PASSWORD=password
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=xbit_agent
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### Environment Variables

```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xbit_agent
DB_USER=postgres
DB_PASSWORD=password

# Redis (optional)
REDIS_HOST=localhost
REDIS_PORT=6379

# Application
LOG_LEVEL=info
PORT=8080
```

## 📊 Monitoring

### Health Checks

```bash
# System health
curl http://localhost:8080/api/activity-cashback/health

# System status
curl http://localhost:8080/api/activity-cashback/status
```

### Metrics

The system provides metrics for:
- Task completion rates
- User tier distribution
- Cashback claim statistics
- System performance metrics

### Logging

Structured logging with Zap:
- Task completions and failures
- Tier upgrades
- Cashback claims
- System errors and warnings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Ensure all tests pass (`./scripts/test_activity_cashback.sh`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines

- Write tests for all new functionality
- Follow Go best practices and conventions
- Update documentation for API changes
- Ensure backward compatibility
- Add appropriate logging and error handling

## 📖 Documentation

- [System Documentation](docs/ACTIVITY_CASHBACK_SYSTEM.md) - Complete system guide
- [API Examples](docs/API_EXAMPLES.md) - Frontend integration examples
- [Database Schema](docs/DATABASE_SCHEMA.md) - Database design and schema

## 🔧 Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Test database connectivity
psql -h localhost -U postgres -d xbit_agent -c "SELECT 1;"
```

**Background Jobs Not Running**
```bash
# Check system status
curl http://localhost:8080/api/activity-cashback/status
```

**Task Completion Failures**
- Verify user authentication
- Check task availability and requirements
- Validate verification data format

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=debug
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with Go and PostgreSQL
- GraphQL API powered by gqlgen
- Background jobs with custom scheduler
- Comprehensive testing with testify

---

**Made with ❤️ for the XBIT DEX platform**
