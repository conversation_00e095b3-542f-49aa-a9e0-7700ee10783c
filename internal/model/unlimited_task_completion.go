package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// UnlimitedTaskCompletion represents the unlimited_task_completions table
type UnlimitedTaskCompletion struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_unlimited_completions_user_task" json:"user_id"`
	TaskID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_unlimited_completions_user_task" json:"task_id"`
	SequenceNumber   int64             `gorm:"not null;index:idx_unlimited_completions_sequence" json:"sequence_number"`
	PointsAwarded    int               `gorm:"not null;default:0" json:"points_awarded"`
	CompletionDate   time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP;index:idx_unlimited_completions_date" json:"completion_date"`
	VerificationData *VerificationData `gorm:"type:jsonb" json:"verification_data"`
	IPAddress        *net.IP           `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string           `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for UnlimitedTaskCompletion
func (UnlimitedTaskCompletion) TableName() string {
	return "unlimited_task_completions"
}

// IsVerified checks if the task completion was verified
func (utc *UnlimitedTaskCompletion) IsVerified() bool {
	return utc.VerificationData != nil && utc.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (utc *UnlimitedTaskCompletion) GetVerificationMethod() string {
	if utc.VerificationData == nil {
		return ""
	}
	return utc.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (utc *UnlimitedTaskCompletion) SetVerificationData(method, source string, customData map[string]interface{}) {
	utc.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}
