package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// VerificationData represents verification information for task completions
type VerificationData struct {
	VerificationMethod string                 `json:"verification_method"`
	VerifiedAt         time.Time              `json:"verified_at"`
	VerificationSource string                 `json:"verification_source"`
	CustomData         map[string]interface{} `json:"custom_data,omitempty"`
}

// Value implements the driver.Valuer interface for VerificationData
func (vd VerificationData) Value() (driver.Value, error) {
	return json.Marshal(vd)
}

// Scan implements the sql.Scanner interface for VerificationData
func (vd *VerificationData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, vd)
}
