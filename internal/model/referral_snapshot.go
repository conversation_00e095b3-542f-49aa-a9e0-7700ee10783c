package model

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ReferralSnapshot represents the referral_snapshots table
type ReferralSnapshot struct { //用户自己的信息
	UserID                  uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`
	DirectCount             int             `gorm:"default:0" json:"direct_count"`
	TotalDownlineCount      int             `gorm:"default:0" json:"total_downline_count"`
	TotalVolumeUSD          decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_volume_usd"`
	TotalRewardsDistributed decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_rewards_distributed"`
	TradingUserCount        int             `gorm:"default:0" json:"trading_user_count"`

	// --- Personal Trading Stats ---
	TotalPerpsVolumeUSD   decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_perps_volume_usd"`
	TotalPerpsFees        decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees"`
	TotalPerpsFeesPaid    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees_paid"`
	TotalPerpsFeesUnPaid  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees_unpaid"`
	TotalPerpsTradesCount int64           `gorm:"default:0" json:"total_perps_trades_count"`

	TotalMemeVolumeUSD   decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_meme_volume_usd"`
	TotalMemeFees        decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees"`
	TotalMemeFeesPaid    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees_paid"`
	TotalMemeFeesUnPaid  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees_unpaid"`
	TotalMemeTradesCount int64           `gorm:"default:0" json:"total_meme_trades_count"`

	// --- Personal Rewards Stats ---
	// contract commission
	TotalCommissionEarnedUSD decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_commission_earned_usd"`
	ClaimedCommissionUSD     decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"claimed_commission_usd"`
	UnclaimedCommissionUSD   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"unclaimed_commission_usd"`
	// Meme cashback
	TotalCashbackEarnedUSD decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_cashback_earned_usd"`
	ClaimedCashbackUSD     decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"claimed_cashback_usd"`
	UnclaimedCashbackUSD   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"unclaimed_cashback_usd"`
	//a
	L1UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l1_upline_id"` //直接 b
	L2UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l2_upline_id"` //间接 c
	L3UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l3_upline_id"` //延长 d

	// Relationships
	User     User  `gorm:"foreignKey:UserID;references:ID;constraint:false" json:"user"`
	L1Upline *User `gorm:"foreignKey:L1UplineID" json:"l1_upline,omitempty"`
	L2Upline *User `gorm:"foreignKey:L2UplineID" json:"l2_upline,omitempty"`
	L3Upline *User `gorm:"foreignKey:L3UplineID" json:"l3_upline,omitempty"`
}

// TableName specifies the table name for ReferralSnapshot
func (ReferralSnapshot) TableName() string {
	return "referral_snapshots"
}
