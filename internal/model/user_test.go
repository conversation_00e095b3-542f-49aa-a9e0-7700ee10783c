package model

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func TestUser_BeforeCreate(t *testing.T) {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	t.Run("user with nil UUID gets new UUID", func(t *testing.T) {
		user := &User{
			ID:           uuid.Nil,
			AgentLevelID: 1,
		}

		// Execute BeforeCreate hook
		err := user.BeforeCreate(db)
		assert.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, user.ID)
	})

	t.Run("user with existing UUID keeps UUID", func(t *testing.T) {
		originalID := uuid.New()
		user := &User{
			ID:           originalID,
			AgentLevelID: 1,
		}

		// Execute BeforeCreate hook
		err := user.BeforeCreate(db)
		assert.NoError(t, err)
		assert.Equal(t, originalID, user.ID)
	})
}

func TestUser_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests due to schema complexity")
}
