package model

import (
	"time"

	"github.com/google/uuid"
)

type BuilderCrawlerHistory struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;"`
	BuilderAddress string    `json:"builder_address" gorm:"type:varchar(255);not null;index:idx_builder_address,type:HASH"`
	Date           time.Time `json:"date"`
	LastIndex      int64     `json:"last_index"`
	Url            string    `json:"url" gorm:"type:varchar(511);not null"`
	IsEnded        bool      `json:"is_ended" gorm:"default:false"`
	CreatedAt      time.Time `json:"created_at" gorm:"default:current_timestamp;"`
}
