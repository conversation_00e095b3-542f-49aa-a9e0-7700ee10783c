package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// ManualTaskCompletionStatus represents the status of manual task completion
type ManualTaskCompletionStatus string

const (
	ManualTaskStatusPending  ManualTaskCompletionStatus = "PENDING"
	ManualTaskStatusApproved ManualTaskCompletionStatus = "APPROVED"
	ManualTaskStatusRejected ManualTaskCompletionStatus = "REJECTED"
)

// ManualTaskCompletion represents the manual_task_completions table
type ManualTaskCompletion struct {
	ID               uuid.UUID                  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID                  `gorm:"type:uuid;not null;index:idx_manual_completions_user" json:"user_id"`
	TaskID           uuid.UUID                  `gorm:"type:uuid;not null;index:idx_manual_completions_task" json:"task_id"`
	PointsAwarded    int                        `gorm:"not null;default:0" json:"points_awarded"`
	CompletionDate   time.Time                  `gorm:"not null;default:CURRENT_TIMESTAMP;index:idx_manual_completions_date" json:"completion_date"`
	VerificationData *VerificationData          `gorm:"type:jsonb" json:"verification_data"`
	ApprovedBy       *uuid.UUID                 `gorm:"type:uuid;index:idx_manual_completions_approver" json:"approved_by"`
	ApprovalDate     *time.Time                 `json:"approval_date"`
	ApprovalNotes    *string                    `gorm:"type:text" json:"approval_notes"`
	Status           ManualTaskCompletionStatus `gorm:"type:varchar(20);not null;default:'PENDING';index:idx_manual_completions_status" json:"status"`
	IPAddress        *net.IP                    `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string                    `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time                  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time                  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User     User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task     ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
	Approver *User        `gorm:"foreignKey:ApprovedBy;references:ID" json:"approver,omitempty"`
}

// TableName specifies the table name for ManualTaskCompletion
func (ManualTaskCompletion) TableName() string {
	return "manual_task_completions"
}

// IsVerified checks if the task completion was verified
func (mtc *ManualTaskCompletion) IsVerified() bool {
	return mtc.VerificationData != nil && mtc.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (mtc *ManualTaskCompletion) GetVerificationMethod() string {
	if mtc.VerificationData == nil {
		return ""
	}
	return mtc.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (mtc *ManualTaskCompletion) SetVerificationData(method, source string, customData map[string]interface{}) {
	mtc.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}

// IsApproved checks if the manual task completion is approved
func (mtc *ManualTaskCompletion) IsApproved() bool {
	return mtc.Status == ManualTaskStatusApproved
}

// IsPending checks if the manual task completion is pending
func (mtc *ManualTaskCompletion) IsPending() bool {
	return mtc.Status == ManualTaskStatusPending
}

// IsRejected checks if the manual task completion is rejected
func (mtc *ManualTaskCompletion) IsRejected() bool {
	return mtc.Status == ManualTaskStatusRejected
}

// Approve approves the manual task completion
func (mtc *ManualTaskCompletion) Approve(approverID uuid.UUID, notes string) {
	mtc.Status = ManualTaskStatusApproved
	mtc.ApprovedBy = &approverID
	now := time.Now()
	mtc.ApprovalDate = &now
	if notes != "" {
		mtc.ApprovalNotes = &notes
	}
	mtc.UpdatedAt = now
}

// Reject rejects the manual task completion
func (mtc *ManualTaskCompletion) Reject(approverID uuid.UUID, notes string) {
	mtc.Status = ManualTaskStatusRejected
	mtc.ApprovedBy = &approverID
	now := time.Now()
	mtc.ApprovalDate = &now
	if notes != "" {
		mtc.ApprovalNotes = &notes
	}
	mtc.UpdatedAt = now
}
