package model

import (
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestAffiliateTransaction_IsReferralTransaction(t *testing.T) {
	referrerID := uuid.New()

	tests := []struct {
		name        string
		transaction *AffiliateTransaction
		expected    bool
	}{
		{
			name: "transaction with referrer ID and depth > 0 is referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:    &referrerID,
				ReferralDepth: 1,
			},
			expected: true,
		},
		{
			name: "transaction without referrer ID is not referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:    nil,
				ReferralDepth: 1,
			},
			expected: false,
		},
		{
			name: "transaction with referrer ID but depth = 0 is not referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:    &referrerID,
				ReferralDepth: 0,
			},
			expected: false,
		},
		{
			name: "transaction without referrer ID and depth = 0 is not referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:    nil,
				ReferralDepth: 0,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.transaction.IsReferralTransaction()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAffiliateTransaction_CalculateCommission(t *testing.T) {
	referrerID := uuid.New()

	tests := []struct {
		name        string
		transaction *AffiliateTransaction
		expected    decimal.Decimal
	}{
		{
			name: "calculate commission for referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:     &referrerID,
				ReferralDepth:  1,
				VolumeUSD:      decimal.NewFromFloat(1000.0),
				CommissionRate: decimal.NewFromFloat(0.30),
			},
			expected: decimal.NewFromFloat(300.0), // 1000 * 0.30
		},
		{
			name: "calculate commission for non-referral transaction",
			transaction: &AffiliateTransaction{
				ReferrerID:     nil,
				ReferralDepth:  0,
				VolumeUSD:      decimal.NewFromFloat(1000.0),
				CommissionRate: decimal.NewFromFloat(0.30),
			},
			expected: decimal.Zero,
		},
		{
			name: "calculate commission with small amounts",
			transaction: &AffiliateTransaction{
				ReferrerID:     &referrerID,
				ReferralDepth:  1,
				VolumeUSD:      decimal.NewFromFloat(10.50),
				CommissionRate: decimal.NewFromFloat(0.05),
			},
			expected: decimal.NewFromFloat(0.525), // 10.50 * 0.05
		},
		{
			name: "calculate commission with zero volume",
			transaction: &AffiliateTransaction{
				ReferrerID:     &referrerID,
				ReferralDepth:  1,
				VolumeUSD:      decimal.Zero,
				CommissionRate: decimal.NewFromFloat(0.30),
			},
			expected: decimal.Zero,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.transaction.CalculateCommission()
			assert.True(t, tt.expected.Equal(result))
		})
	}
}

func TestAffiliateTransaction_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests due to schema complexity")
}

func TestAffiliateTransaction_EnumValues(t *testing.T) {
	t.Run("transaction type enum values", func(t *testing.T) {
		// Test that enum values are correctly defined
		assert.Equal(t, TransactionType("Buy"), Buy)
		assert.Equal(t, TransactionType("Sell"), Sell)
	})

	t.Run("order type enum values", func(t *testing.T) {
		// Test that enum values are correctly defined
		assert.Equal(t, OrderType("Market"), Market)
		assert.Equal(t, OrderType("Limit"), Limit)
	})

	t.Run("status enum values", func(t *testing.T) {
		// Test that enum values are correctly defined
		assert.Equal(t, TransactionStatus("pending"), StatusPending)
		assert.Equal(t, TransactionStatus("completed"), StatusCompleted)
		assert.Equal(t, TransactionStatus("failed"), StatusFailed)
		assert.Equal(t, TransactionStatus("cancelled"), StatusCancelled)
	})
}

func TestAffiliateTransaction_Relationships(t *testing.T) {
	t.Skip("Skipping relationship tests due to schema complexity")
}
