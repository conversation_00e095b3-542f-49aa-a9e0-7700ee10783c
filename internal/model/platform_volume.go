package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type PlatformVolume struct {
	ID                  uuid.UUID       `json:"id" gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()"`
	Date                time.Time       `gorm:"type:date;not null;uniqueIndex" json:"date"`
	TotalMemeVolume     decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_meme_volume"`     // Total MEME volume for the day
	TotalContractVolume decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_contract_volume"` // Total contract volume for the day
	FourteenDayVolume   decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"fourteen_day_volume"`   // 14-day rolling volume
	CreatedAt           time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName specifies the table name for PlatformVolume
func (PlatformVolume) TableName() string {
	return "platform_volumes"
}
