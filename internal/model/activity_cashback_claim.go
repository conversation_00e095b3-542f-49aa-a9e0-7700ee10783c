package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ClaimType represents the type of cashback claim
type ClaimType string

const (
	ClaimTypeTradingCashback ClaimType = "TRADING_CASHBACK"
	ClaimTypeTaskReward      ClaimType = "TASK_REWARD"
	ClaimTypeTierBonus       ClaimType = "TIER_BONUS"
	ClaimTypeReferralBonus   ClaimType = "REFERRAL_BONUS"
)

// ClaimStatus represents the status of a cashback claim
type ClaimStatus string

const (
	ClaimStatusPending    ClaimStatus = "PENDING"
	ClaimStatusProcessing ClaimStatus = "PROCESSING"
	ClaimStatusCompleted  ClaimStatus = "COMPLETED"
	ClaimStatusFailed     ClaimStatus = "FAILED"
)

// ClaimMetadata represents additional metadata for claims
type ClaimMetadata struct {
	TaskIDs           []uuid.UUID            `json:"task_ids,omitempty"`
	TradingVolume     *decimal.Decimal       `json:"trading_volume,omitempty"`
	CashbackRate      *decimal.Decimal       `json:"cashback_rate,omitempty"`
	TierLevel         *int                   `json:"tier_level,omitempty"`
	ProcessingDetails map[string]interface{} `json:"processing_details,omitempty"`
	ErrorDetails      map[string]interface{} `json:"error_details,omitempty"`
}

// Scan implements the sql.Scanner interface for ClaimMetadata
func (cm *ClaimMetadata) Scan(value interface{}) error {
	if value == nil {
		*cm = ClaimMetadata{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, cm)
}

// Value implements the driver.Valuer interface for ClaimMetadata
func (cm ClaimMetadata) Value() (driver.Value, error) {
	return json.Marshal(cm)
}

// ActivityCashbackClaim represents the activity_cashback_claims table
type ActivityCashbackClaim struct {
	ID              uuid.UUID       `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          uuid.UUID       `gorm:"type:uuid;not null;index" json:"user_id"`
	ClaimType       ClaimType       `gorm:"type:varchar(50);not null" json:"claim_type"`
	TotalAmountUSD  decimal.Decimal `gorm:"type:numeric(38,6);not null" json:"total_amount_usd"`
	TotalAmountSOL  decimal.Decimal `gorm:"type:numeric(38,6);not null" json:"total_amount_sol"`
	TransactionHash *string         `gorm:"type:varchar(255)" json:"transaction_hash"`
	Status          ClaimStatus     `gorm:"type:varchar(20);not null;default:'PENDING'" json:"status"`
	ClaimedAt       time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"claimed_at"`
	ProcessedAt     *time.Time      `json:"processed_at"`
	Metadata        *ClaimMetadata  `gorm:"type:jsonb" json:"metadata"`
	CreatedAt       time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

// TableName specifies the table name for ActivityCashbackClaim
func (ActivityCashbackClaim) TableName() string {
	return "activity_cashback_claims"
}

// IsCompleted checks if the claim has been completed
func (acc *ActivityCashbackClaim) IsCompleted() bool {
	return acc.Status == ClaimStatusCompleted
}

// IsPending checks if the claim is pending
func (acc *ActivityCashbackClaim) IsPending() bool {
	return acc.Status == ClaimStatusPending
}

// IsProcessing checks if the claim is being processed
func (acc *ActivityCashbackClaim) IsProcessing() bool {
	return acc.Status == ClaimStatusProcessing
}

// IsFailed checks if the claim has failed
func (acc *ActivityCashbackClaim) IsFailed() bool {
	return acc.Status == ClaimStatusFailed
}

// MarkAsProcessing marks the claim as processing
func (acc *ActivityCashbackClaim) MarkAsProcessing() {
	acc.Status = ClaimStatusProcessing
	acc.UpdatedAt = time.Now()
}

// MarkAsCompleted marks the claim as completed
func (acc *ActivityCashbackClaim) MarkAsCompleted(transactionHash string) {
	now := time.Now()
	acc.Status = ClaimStatusCompleted
	acc.TransactionHash = &transactionHash
	acc.ProcessedAt = &now
	acc.UpdatedAt = now
}

// MarkAsFailed marks the claim as failed
func (acc *ActivityCashbackClaim) MarkAsFailed(errorDetails map[string]interface{}) {
	acc.Status = ClaimStatusFailed
	acc.UpdatedAt = time.Now()

	if acc.Metadata == nil {
		acc.Metadata = &ClaimMetadata{}
	}
	acc.Metadata.ErrorDetails = errorDetails
}

// GetTotalAmountUSDFloat returns the total USD amount as float64
func (acc *ActivityCashbackClaim) GetTotalAmountUSDFloat() float64 {
	amount, _ := acc.TotalAmountUSD.Float64()
	return amount
}

// GetTotalAmountSOLFloat returns the total SOL amount as float64
func (acc *ActivityCashbackClaim) GetTotalAmountSOLFloat() float64 {
	amount, _ := acc.TotalAmountSOL.Float64()
	return amount
}
