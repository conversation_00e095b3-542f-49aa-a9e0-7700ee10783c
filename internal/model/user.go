package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents the users table
type User struct {
	ID             uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Email          *string        `gorm:"type:text;unique" json:"email"`
	InvitationCode *string        `gorm:"type:text;size:15" json:"invitation_code"` //前端自己设置的 // User input, 5-15 characters, can be null initially
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at"`

	AgentLevelID uint       `gorm:"default:1" json:"agent_level_id"` //meme 1～5 合约 1-7 按照交易量   // Foreign key to AgentLevel. Defaults to Lv1.
	AgentLevel   AgentLevel `gorm:"foreignKey:AgentLevelID" json:"agent_level"`

	LevelGracePeriodStartedAt *time.Time `json:"level_grace_period_started_at"`     //60天的时效  // Null if user is meeting criteria, timestamp if grace period has started
	LevelUpgradedAt           *time.Time `json:"level_upgraded_at"`                 // Record level upgrade time node
	FirstTransactionAt        *time.Time `gorm:"index" json:"first_transaction_at"` // Defaults to NULL

	// Relationships
	// Wallets          []UserWallet      `gorm:"foreignKey:UserID;constraint:false" json:"wallets"`
	Referrals        []Referral        //邀请人        `gorm:"foreignKey:UserID;references:ID" json:"referrals"` // All referral relationships for this user
	ReferralSnapshot *ReferralSnapshot `gorm:"foreignKey:UserID" json:"referral_snapshot"`
	ReferredUsers    []Referral        //邀请别人     `gorm:"foreignKey:ReferrerID;references:ID" json:"referred_users"` // Users referred by this user
}

// BeforeCreate will set a UUID rather than numeric ID.
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}
