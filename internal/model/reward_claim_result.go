package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// RewardClaimResult represents the result status of a reward claim
type RewardClaimResult string

const (
	RewardClaimResultSuccess RewardClaimResult = "success"
	RewardClaimResultFailed  RewardClaimResult = "failed"
)

// RewardClaimResultRecord represents the reward_claim_results table
type RewardClaimResultRecord struct {
	ID           uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID       uuid.UUID         `gorm:"type:uuid;not null;index" json:"user_id"`
	Address      string            `gorm:"type:varchar(255);not null" json:"address"`
	Amount       decimal.Decimal   `gorm:"type:numeric(38,6);not null" json:"amount"`
	Token        string            `gorm:"type:varchar(50);not null" json:"token"`
	ChainID      int               `gorm:"type:int;not null" json:"chain_id"`
	Result       RewardClaimResult `gorm:"type:varchar(20);not null" json:"result"`
	ErrorMessage *string           `gorm:"type:text" json:"error_message,omitempty"`
	ErrorCode    *string           `gorm:"type:varchar(100)" json:"error_code,omitempty"`
	ProcessedAt  time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"processed_at"`
	CreatedAt    time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

// TableName specifies the table name for RewardClaimResultRecord
func (RewardClaimResultRecord) TableName() string {
	return "reward_claim_results"
}

// IsSuccess checks if the claim result was successful
func (rcr *RewardClaimResultRecord) IsSuccess() bool {
	return rcr.Result == RewardClaimResultSuccess
}

// IsFailed checks if the claim result failed
func (rcr *RewardClaimResultRecord) IsFailed() bool {
	return rcr.Result == RewardClaimResultFailed
}

// HasError checks if the claim result has error information
func (rcr *RewardClaimResultRecord) HasError() bool {
	return rcr.ErrorMessage != nil || rcr.ErrorCode != nil
}
