package model

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// UserTierInfo represents the user_tier_info table (separate from referral tiers)
type UserTierInfo struct {
	UserID                uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`
	CurrentTier           int             `gorm:"not null;default:1" json:"current_tier"`
	TotalPoints           int             `gorm:"not null;default:0" json:"total_points"`
	PointsThisMonth       int             `gorm:"not null;default:0" json:"points_this_month"`
	TradingVolumeUSD      decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"trading_volume_usd"`
	ActiveDaysThisMonth   int             `gorm:"default:0" json:"active_days_this_month"`
	CumulativeCashbackUSD decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"cumulative_cashback_usd"`
	ClaimableCashbackUSD  decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"claimable_cashback_usd"`
	ClaimedCashbackUSD    decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"claimed_cashback_usd"`
	LastActivityDate      *time.Time      `json:"last_activity_date"`
	TierUpgradedAt        *time.Time      `json:"tier_upgraded_at"`
	MonthlyResetAt        *time.Time      `json:"monthly_reset_at"`
	CreatedAt             time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt             time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User        User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	TierBenefit *TierBenefit `gorm:"foreignKey:CurrentTier;references:TierLevel" json:"tier_benefit,omitempty"`
}

// TableName specifies the table name for UserTierInfo
func (UserTierInfo) TableName() string {
	return "user_tier_info"
}

// AddPoints adds points to the user's total and monthly points
func (uti *UserTierInfo) AddPoints(points int) {
	uti.TotalPoints += points
	uti.PointsThisMonth += points
	uti.UpdatedAt = time.Now()
}

// AddTradingVolume adds trading volume to the user's total
func (uti *UserTierInfo) AddTradingVolume(volume decimal.Decimal) {
	uti.TradingVolumeUSD = uti.TradingVolumeUSD.Add(volume)
	uti.UpdatedAt = time.Now()
}

// AddCashback adds cashback to the user's claimable amount
func (uti *UserTierInfo) AddCashback(amount decimal.Decimal) {
	uti.ClaimableCashbackUSD = uti.ClaimableCashbackUSD.Add(amount)
	uti.CumulativeCashbackUSD = uti.CumulativeCashbackUSD.Add(amount)
	uti.UpdatedAt = time.Now()
}

// ClaimCashback moves claimable cashback to claimed
func (uti *UserTierInfo) ClaimCashback(amount decimal.Decimal) error {
	if uti.ClaimableCashbackUSD.LessThan(amount) {
		return errors.New("insufficient claimable cashback")
	}

	uti.ClaimableCashbackUSD = uti.ClaimableCashbackUSD.Sub(amount)
	uti.ClaimedCashbackUSD = uti.ClaimedCashbackUSD.Add(amount)
	uti.UpdatedAt = time.Now()

	return nil
}

// UpdateActivity updates the last activity date and increments active days if needed
func (uti *UserTierInfo) UpdateActivity() {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Check if this is a new day of activity
	if uti.LastActivityDate == nil || uti.LastActivityDate.Before(today) {
		uti.ActiveDaysThisMonth++
		uti.LastActivityDate = &now
	}

	uti.UpdatedAt = now
}

// ShouldResetMonthly checks if monthly stats should be reset
func (uti *UserTierInfo) ShouldResetMonthly() bool {
	if uti.MonthlyResetAt == nil {
		return true
	}

	now := time.Now()
	return now.Month() != uti.MonthlyResetAt.Month() || now.Year() != uti.MonthlyResetAt.Year()
}

// ResetMonthlyStats resets monthly statistics
func (uti *UserTierInfo) ResetMonthlyStats() {
	now := time.Now()
	uti.PointsThisMonth = 0
	uti.ActiveDaysThisMonth = 0
	uti.MonthlyResetAt = &now
	uti.UpdatedAt = now
}

// CanUpgradeTier checks if the user can upgrade to a higher tier
func (uti *UserTierInfo) CanUpgradeTier(nextTierMinPoints int) bool {
	return uti.TotalPoints >= nextTierMinPoints
}

// UpgradeTier upgrades the user to a new tier
func (uti *UserTierInfo) UpgradeTier(newTier int) {
	now := time.Now()
	uti.CurrentTier = newTier
	uti.TierUpgradedAt = &now
	uti.UpdatedAt = now
}

// GetPointsToNextTier calculates points needed for next tier
func (uti *UserTierInfo) GetPointsToNextTier(nextTierMinPoints int) int {
	remaining := nextTierMinPoints - uti.TotalPoints
	if remaining < 0 {
		return 0
	}
	return remaining
}
