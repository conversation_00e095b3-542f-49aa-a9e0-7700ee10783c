package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// TaskCompletionInterface defines a common interface for all task completion types
type TaskCompletionInterface interface {
	GetID() uuid.UUID
	GetUserID() uuid.UUID
	GetTaskID() uuid.UUID
	GetPointsAwarded() int
	GetCompletionDate() time.Time
	GetVerificationData() *VerificationData
	GetIPAddress() *net.IP
	GetUserAgent() *string
	GetCreatedAt() time.Time
	GetUser() User
	GetTask() ActivityTask

	IsVerified() bool
	GetVerificationMethod() string
	SetVerificationData(method, source string, customData map[string]interface{})
}

// Implement TaskCompletionInterface for DailyTaskCompletion
func (dtc *DailyTaskCompletion) GetID() uuid.UUID                       { return dtc.ID }
func (dtc *DailyTaskCompletion) GetUserID() uuid.UUID                   { return dtc.UserID }
func (dtc *DailyTaskCompletion) GetTaskID() uuid.UUID                   { return dtc.TaskID }
func (dtc *DailyTaskCompletion) GetPointsAwarded() int                  { return dtc.PointsAwarded }
func (dtc *DailyTaskCompletion) GetCompletionDate() time.Time           { return dtc.CompletionTime }
func (dtc *DailyTaskCompletion) GetVerificationData() *VerificationData { return dtc.VerificationData }
func (dtc *DailyTaskCompletion) GetIPAddress() *net.IP                  { return dtc.IPAddress }
func (dtc *DailyTaskCompletion) GetUserAgent() *string                  { return dtc.UserAgent }
func (dtc *DailyTaskCompletion) GetCreatedAt() time.Time                { return dtc.CreatedAt }
func (dtc *DailyTaskCompletion) GetUser() User                          { return dtc.User }
func (dtc *DailyTaskCompletion) GetTask() ActivityTask                  { return dtc.Task }

// Implement TaskCompletionInterface for OneTimeTaskCompletion
func (otc *OneTimeTaskCompletion) GetID() uuid.UUID             { return otc.ID }
func (otc *OneTimeTaskCompletion) GetUserID() uuid.UUID         { return otc.UserID }
func (otc *OneTimeTaskCompletion) GetTaskID() uuid.UUID         { return otc.TaskID }
func (otc *OneTimeTaskCompletion) GetPointsAwarded() int        { return otc.PointsAwarded }
func (otc *OneTimeTaskCompletion) GetCompletionDate() time.Time { return otc.CompletionDate }
func (otc *OneTimeTaskCompletion) GetVerificationData() *VerificationData {
	return otc.VerificationData
}
func (otc *OneTimeTaskCompletion) GetIPAddress() *net.IP   { return otc.IPAddress }
func (otc *OneTimeTaskCompletion) GetUserAgent() *string   { return otc.UserAgent }
func (otc *OneTimeTaskCompletion) GetCreatedAt() time.Time { return otc.CreatedAt }
func (otc *OneTimeTaskCompletion) GetUser() User           { return otc.User }
func (otc *OneTimeTaskCompletion) GetTask() ActivityTask   { return otc.Task }

// Implement TaskCompletionInterface for UnlimitedTaskCompletion
func (utc *UnlimitedTaskCompletion) GetID() uuid.UUID             { return utc.ID }
func (utc *UnlimitedTaskCompletion) GetUserID() uuid.UUID         { return utc.UserID }
func (utc *UnlimitedTaskCompletion) GetTaskID() uuid.UUID         { return utc.TaskID }
func (utc *UnlimitedTaskCompletion) GetPointsAwarded() int        { return utc.PointsAwarded }
func (utc *UnlimitedTaskCompletion) GetCompletionDate() time.Time { return utc.CompletionDate }
func (utc *UnlimitedTaskCompletion) GetVerificationData() *VerificationData {
	return utc.VerificationData
}
func (utc *UnlimitedTaskCompletion) GetIPAddress() *net.IP   { return utc.IPAddress }
func (utc *UnlimitedTaskCompletion) GetUserAgent() *string   { return utc.UserAgent }
func (utc *UnlimitedTaskCompletion) GetCreatedAt() time.Time { return utc.CreatedAt }
func (utc *UnlimitedTaskCompletion) GetUser() User           { return utc.User }
func (utc *UnlimitedTaskCompletion) GetTask() ActivityTask   { return utc.Task }

// Implement TaskCompletionInterface for ProgressiveTaskCompletion
func (ptc *ProgressiveTaskCompletion) GetID() uuid.UUID             { return ptc.ID }
func (ptc *ProgressiveTaskCompletion) GetUserID() uuid.UUID         { return ptc.UserID }
func (ptc *ProgressiveTaskCompletion) GetTaskID() uuid.UUID         { return ptc.TaskID }
func (ptc *ProgressiveTaskCompletion) GetPointsAwarded() int        { return ptc.PointsAwarded }
func (ptc *ProgressiveTaskCompletion) GetCompletionDate() time.Time { return ptc.CompletionDate }
func (ptc *ProgressiveTaskCompletion) GetVerificationData() *VerificationData {
	return ptc.VerificationData
}
func (ptc *ProgressiveTaskCompletion) GetIPAddress() *net.IP   { return ptc.IPAddress }
func (ptc *ProgressiveTaskCompletion) GetUserAgent() *string   { return ptc.UserAgent }
func (ptc *ProgressiveTaskCompletion) GetCreatedAt() time.Time { return ptc.CreatedAt }
func (ptc *ProgressiveTaskCompletion) GetUser() User           { return ptc.User }
func (ptc *ProgressiveTaskCompletion) GetTask() ActivityTask   { return ptc.Task }

// Implement TaskCompletionInterface for ManualTaskCompletion
func (mtc *ManualTaskCompletion) GetID() uuid.UUID                       { return mtc.ID }
func (mtc *ManualTaskCompletion) GetUserID() uuid.UUID                   { return mtc.UserID }
func (mtc *ManualTaskCompletion) GetTaskID() uuid.UUID                   { return mtc.TaskID }
func (mtc *ManualTaskCompletion) GetPointsAwarded() int                  { return mtc.PointsAwarded }
func (mtc *ManualTaskCompletion) GetCompletionDate() time.Time           { return mtc.CompletionDate }
func (mtc *ManualTaskCompletion) GetVerificationData() *VerificationData { return mtc.VerificationData }
func (mtc *ManualTaskCompletion) GetIPAddress() *net.IP                  { return mtc.IPAddress }
func (mtc *ManualTaskCompletion) GetUserAgent() *string                  { return mtc.UserAgent }
func (mtc *ManualTaskCompletion) GetCreatedAt() time.Time                { return mtc.CreatedAt }
func (mtc *ManualTaskCompletion) GetUser() User                          { return mtc.User }
func (mtc *ManualTaskCompletion) GetTask() ActivityTask                  { return mtc.Task }
