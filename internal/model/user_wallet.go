package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ChainType string

const (
	ChainEvm    ChainType = "EVM"
	ChainArb    ChainType = "ARB"
	ChainSolana ChainType = "SOLANA"
	ChainTron   ChainType = "TRON"
)

func (c ChainType) String() string {
	return string(c)
}

// UserWallet represents the user_wallets table
type UserWallet struct {
	ID              uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          uuid.UUID      `gorm:"type:uuid;not null;index" json:"user_id"`
	Chain           ChainType      `gorm:"type:text;not null;uniqueIndex:idx_wallet_address_chain" json:"chain"`
	WalletAddress   string         `gorm:"type:text;not null;uniqueIndex:idx_wallet_address_chain" json:"wallet_address"`
	WalletID        *uuid.UUID     `gorm:"type:uuid" json:"wallet_id"`
	WalletAccountID *uuid.UUID     `gorm:"type:uuid" json:"wallet_account_id"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

// BeforeCreate will set a UUID rather than numeric ID.
func (uw *UserWallet) BeforeCreate(tx *gorm.DB) error {
	if uw.ID == uuid.Nil {
		uw.ID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for UserWallet
func (UserWallet) TableName() string {
	return "user_wallets"
}

// Add unique constraint for wallet_address and chain combination
func (UserWallet) UniqueIndex() string {
	return "idx_wallet_address_chain"
}
