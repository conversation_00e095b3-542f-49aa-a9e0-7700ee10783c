package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// InfiniteAgentConfig stores the configuration for users who are designated as "infinite agents".
type InfiniteAgentConfig struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;unique;index" json:"user_id"` // Foreign key to the User table.

	// The 'n' value from the diagram. This is the total potential commission pool rate for the infinite agent's tree.
	CommissionRateN decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"commission_rate_n"`

	// --- DETAILED CALCULATION COMPONENTS ---
	// The sum of Net Fees generated by the entire tree.
	TotalNetFeeUSD decimal.Decimal `gorm:"type:numeric(38,18);not null" json:"total_net_fee_usd"`

	// The sum of all Direct, Indirect, and Extended commissions paid out to the 3-level uplines from this tree's transactions.
	TotalStandardCommissionPaidUSD decimal.Decimal `gorm:"type:numeric(38,18);not null" json:"total_standard_commission_paid_usd"`

	// --- FINAL RESULT ---
	// The final calculated commission for the infinite agent.
	// Formula: (TotalNetFeeUSD * PoolCommissionRateN) - TotalStandardCommissionPaidUSD
	FinalCommissionAmountUSD decimal.Decimal `gorm:"type:numeric(38,18);not null" json:"final_commission_amount_usd"`

	Status string `gorm:"type:varchar(20);not null;default:'ACTIVE';index" json:"status"` // "ACTIVE", "INACTIVE"

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationship
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}
