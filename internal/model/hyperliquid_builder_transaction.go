package model

import (
	"time"

	"github.com/google/uuid"
)

// transactions was crawl from hyper liquid endpoint, all transaction fee of our builder..
type HyperLiquidBuilderTransaction struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;"`
	Time             time.Time `json:"time" gorm:"type:timestamp;not null;"`
	User             string    `json:"user" gorm:"type:varchar(255);not null;index:idx_user_address,type:HASH"`
	Coin             string    `json:"coin" gorm:"type:varchar(255);not null;"`
	Side             string    `json:"side" gorm:"type:varchar(55);not null;"`
	Price            float64   `json:"px" gorm:"type:double precision;"`
	Size             float64   `json:"sz" gorm:"type:double precision;"`
	Crossed          bool      `json:"crossed" gorm:"type:boolean;not null;"`
	SpecialTradeType string    `json:"special_trade_type" gorm:"type:varchar(255);"`
	TIF              string    `json:"tif" gorm:"type:varchar(255);"`
	IsTrigger        bool      `json:"is_trigger" gorm:"type:boolean;"`
	Counterparty     string    `json:"counterparty" gorm:"type:varchar(255);"`
	ClosedPNL        float64   `json:"closed_pnl" gorm:"type:double precision;"`
	TwapID           int64     `json:"twap_id" gorm:"type:bigint;"`
	BuilderFee       float64   `json:"builder_fee" gorm:"type:double precision;"`
	CreatedAt        time.Time `json:"created_at"`
}

// NewHyperLiquidBuilderTransaction creates a new HyperLiquidBuilderTransaction with default values.
func NewHyperLiquidBuilderTransaction() *HyperLiquidBuilderTransaction {
	newUuid, err := uuid.NewV7()
	if err != nil {
		newUuid = uuid.New()
	}

	return &HyperLiquidBuilderTransaction{
		ID:        newUuid,
		CreatedAt: time.Now(),
	}
}
