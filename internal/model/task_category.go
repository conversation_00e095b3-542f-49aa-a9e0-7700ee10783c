package model

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// TaskCategory represents the task_categories table
type TaskCategory struct {
	ID          uint             `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        TaskCategoryName `gorm:"type:varchar(50);not null;unique" json:"name"`
	DisplayName string           `gorm:"type:varchar(100);not null" json:"display_name"`
	Description *string          `gorm:"type:text" json:"description"`
	Icon        *string          `gorm:"type:varchar(255)" json:"icon"`
	SortOrder   int              `gorm:"default:0" json:"sort_order"`
	IsActive    bool             `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time        `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time        `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	Tasks []ActivityTask `gorm:"foreignKey:CategoryID;references:ID" json:"tasks,omitempty"`
}

// Value implements the driver.Valuer interface for database storage
func (t TaskCategoryName) Value() (driver.Value, error) {
	return string(t), nil
}

// Scan implements the sql.Scanner interface for database retrieval
func (t *TaskCategoryName) Scan(value interface{}) error {
	if value == nil {
		*t = ""
		return nil
	}

	switch v := value.(type) {
	case string:
		*t = TaskCategoryName(v)
	case []byte:
		*t = TaskCategoryName(v)
	default:
		return fmt.Errorf("cannot scan %T into TaskCategoryName", value)
	}

	return nil
}

// TableName specifies the table name for TaskCategory
func (TaskCategory) TableName() string {
	return "task_categories"
}
