package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// PendingCommunityTaskStatus represents the status of a pending community task
type PendingCommunityTaskStatus string

const (
	PendingCommunityTaskStatusPending   PendingCommunityTaskStatus = "PENDING"
	PendingCommunityTaskStatusCompleted PendingCommunityTaskStatus = "COMPLETED"
	PendingCommunityTaskStatusExpired   PendingCommunityTaskStatus = "EXPIRED"
	PendingCommunityTaskStatusCancelled PendingCommunityTaskStatus = "CANCELLED"
)

// PendingCommunityTask represents the pending_community_tasks table
// This table stores community tasks that need to wait 2 minutes before completion
type PendingCommunityTask struct {
	ID                uuid.UUID                  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID            uuid.UUID                  `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID            uuid.UUID                  `gorm:"type:uuid;not null;index" json:"task_id"`
	Status            PendingCommunityTaskStatus `gorm:"type:varchar(20);not null;default:'PENDING'" json:"status"`
	ClickedAt         time.Time                  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"clicked_at"`
	CompletionTime    *time.Time                 `json:"completion_time"`     // When it should be completed (clicked_at + 2 minutes)
	ActualCompletedAt *time.Time                 `json:"actual_completed_at"` // When it was actually completed
	VerificationData  *VerificationData          `gorm:"type:jsonb" json:"verification_data"`
	IPAddress         *net.IP                    `gorm:"type:inet" json:"ip_address"`
	UserAgent         *string                    `gorm:"type:text" json:"user_agent"`
	CreatedAt         time.Time                  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time                  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for PendingCommunityTask
func (PendingCommunityTask) TableName() string {
	return "pending_community_tasks"
}

// IsReadyForCompletion checks if the task is ready to be completed (2 minutes have passed)
func (pct *PendingCommunityTask) IsReadyForCompletion() bool {
	if pct.Status != PendingCommunityTaskStatusPending {
		return false
	}

	if pct.CompletionTime == nil {
		return false
	}

	return time.Now().After(*pct.CompletionTime)
}

// MarkAsCompleted marks the task as completed
func (pct *PendingCommunityTask) MarkAsCompleted() {
	now := time.Now()
	pct.Status = PendingCommunityTaskStatusCompleted
	pct.ActualCompletedAt = &now
	pct.UpdatedAt = now
}

// MarkAsExpired marks the task as expired
func (pct *PendingCommunityTask) MarkAsExpired() {
	now := time.Now()
	pct.Status = PendingCommunityTaskStatusExpired
	pct.UpdatedAt = now
}

// MarkAsCancelled marks the task as cancelled
func (pct *PendingCommunityTask) MarkAsCancelled() {
	now := time.Now()
	pct.Status = PendingCommunityTaskStatusCancelled
	pct.UpdatedAt = now
}

// SetVerificationData sets the verification data for the pending task
func (pct *PendingCommunityTask) SetVerificationData(method, source string, customData map[string]interface{}) {
	pct.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}

// GetRemainingWaitTime returns the remaining wait time in seconds
func (pct *PendingCommunityTask) GetRemainingWaitTime() int64 {
	if pct.CompletionTime == nil {
		return 0
	}

	remaining := pct.CompletionTime.Sub(time.Now()).Seconds()
	if remaining < 0 {
		return 0
	}

	return int64(remaining)
}
