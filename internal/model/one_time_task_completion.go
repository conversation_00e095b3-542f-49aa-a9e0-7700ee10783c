package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// OneTimeTaskCompletion represents the one_time_task_completions table
type OneTimeTaskCompletion struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_onetime_completions_user;uniqueIndex:uk_onetime_user_task" json:"user_id"`
	TaskID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_onetime_completions_task;uniqueIndex:uk_onetime_user_task" json:"task_id"`
	PointsAwarded    int               `gorm:"not null;default:0" json:"points_awarded"`
	CompletionDate   time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP;index:idx_onetime_completions_date" json:"completion_date"`
	VerificationData *VerificationData `gorm:"type:jsonb" json:"verification_data"`
	IPAddress        *net.IP           `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string           `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for OneTimeTaskCompletion
func (OneTimeTaskCompletion) TableName() string {
	return "one_time_task_completions"
}

// IsVerified checks if the task completion was verified
func (otc *OneTimeTaskCompletion) IsVerified() bool {
	return otc.VerificationData != nil && otc.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (otc *OneTimeTaskCompletion) GetVerificationMethod() string {
	if otc.VerificationData == nil {
		return ""
	}
	return otc.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (otc *OneTimeTaskCompletion) SetVerificationData(method, source string, customData map[string]interface{}) {
	otc.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}
