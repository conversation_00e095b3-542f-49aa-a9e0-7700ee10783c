package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// InfiniteAgentReferralTree Infinite Agent Referral Tree
// Used to record the referral tree structure of infinite agent users
type InfiniteAgentReferralTree struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Infinite agent information
	InfiniteAgentUserID uuid.UUID       `gorm:"type:uuid;not null;index" json:"infinite_agent_user_id"` // Infinite agent user ID
	CommissionRateN     decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"commission_rate_n"`   // Commission rate N

	// Tree basic information
	RootUserID   uuid.UUID `gorm:"type:uuid;not null;index" json:"root_user_id"` // Root user ID
	SnapshotDate time.Time `gorm:"not null;index" json:"snapshot_date"`          // Snapshot date

	// Tree structure information
	TotalNodes  int `gorm:"default:0" json:"total_nodes"`  // Total nodes in the tree
	MaxDepth    int `gorm:"default:0" json:"max_depth"`    // Maximum depth of the tree
	DirectCount int `gorm:"default:0" json:"direct_count"` // Direct referral count (L1)

	// Statistics
	ActiveUsers  int `gorm:"default:0" json:"active_users"`  // Number of active users
	TradingUsers int `gorm:"default:0" json:"trading_users"` // Number of trading users

	// Commission statistics
	TotalCommissionEarned decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_commission_earned"` // Total commission earned
	TotalVolumeUSD        decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_volume_usd"`        // Total trading volume

	// Status information
	Status      string `gorm:"type:varchar(20);default:'ACTIVE'" json:"status"` // Status: ACTIVE, INACTIVE
	Description string `gorm:"type:text" json:"description"`                    // Description

	// Relationships
	InfiniteAgentUser   User                    `gorm:"foreignKey:InfiniteAgentUserID;references:ID" json:"infinite_agent_user"`
	InfiniteAgentConfig InfiniteAgentConfig     `gorm:"foreignKey:InfiniteAgentUserID;references:UserID" json:"infinite_agent_config"`
	RootUser            User                    `gorm:"foreignKey:RootUserID;references:ID" json:"root_user"`
	TreeNodes           []InfiniteAgentTreeNode `gorm:"foreignKey:TreeID;references:ID" json:"tree_nodes"`
}

// TableName specifies the table name for InfiniteAgentReferralTree
func (InfiniteAgentReferralTree) TableName() string {
	return "infinite_agent_referral_trees"
}

// InfiniteAgentTreeNode Infinite Agent Referral Tree Node
// Used to record detailed information of each node in the infinite agent referral tree
type InfiniteAgentTreeNode struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Association to tree
	TreeID uint `gorm:"not null;index" json:"tree_id"`

	// Node information
	UserID       uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"` // User ID
	ParentUserID *uuid.UUID `gorm:"type:uuid;index" json:"parent_user_id"`   // Parent user ID
	ReferrerID   *uuid.UUID `gorm:"type:uuid;index" json:"referrer_id"`      // Referrer ID

	// Tree structure information
	Depth    int `gorm:"default:0" json:"depth"`    // Depth in the tree
	Level    int `gorm:"default:0" json:"level"`    // Level (L1, L2, L3...)
	Position int `gorm:"default:0" json:"position"` // Position among siblings

	// User status information
	IsActive     bool `gorm:"default:true" json:"is_active"`   // Whether active
	IsTrading    bool `gorm:"default:false" json:"is_trading"` // Whether trading user
	AgentLevelID uint `gorm:"default:1" json:"agent_level_id"` // Agent level

	// Commission information
	CommissionEarned decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"commission_earned"` // Commission generated by this user
	VolumeUSD        decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"volume_usd"`        // Trading volume of this user
	FeeVolumeUSD     decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"fee_volume_usd"`    // Fee volume generated by this user

	// Relationships
	Tree       InfiniteAgentReferralTree `gorm:"foreignKey:TreeID;references:ID" json:"tree"`
	User       User                      `gorm:"foreignKey:UserID;references:ID" json:"user"`
	ParentUser *User                     `gorm:"foreignKey:ParentUserID;references:ID" json:"parent_user"`
	Referrer   *User                     `gorm:"foreignKey:ReferrerID;references:ID" json:"referrer"`
	AgentLevel AgentLevel                `gorm:"foreignKey:AgentLevelID;references:ID" json:"agent_level"`
}

// TableName specifies the table name for InfiniteAgentTreeNode
func (InfiniteAgentTreeNode) TableName() string {
	return "infinite_agent_tree_nodes"
}
