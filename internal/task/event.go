package task

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type UserEvent string

const (
	UserNewWalletSubject UserEvent = "dex.user.wallet.new"
)

type HyperLiquidEvent string

const (
	HyperLiquidTransactionEventSubject HyperLiquidEvent = "hyperliquid.tx.event"
)

type RewardEvent string

const (
	RewardSubmitClaimEventSubject RewardEvent = "reward.claim.execute"
	RewardClaimResultEventSubject RewardEvent = "reward.claim.result"
)

type UserWalletInfo struct {
	ID              string          `json:"id"`
	Chain           model.ChainType `json:"chain"`
	WalletAddress   string          `json:"walletAddress"`
	WalletAccountID uuid.UUID       `json:"walletAccountId"`
	WalletID        uuid.UUID       `json:"walletId"`
	CreatedAt       string          `json:"createdAt"`
}

type UserNewWalletEvent struct {
	UserID  uuid.UUID        `json:"userId"`
	Wallets []UserWalletInfo `json:"wallets"`
}

type HyperLiquidTransactionEvent struct {
	Cloid         string           `json:"cloid" binding:"required"`
	UserID        uuid.UUID        `json:"user_id" binding:"required"`
	WalletAddress *string          `json:"wallet_address"`
	Side          *string          `json:"side"`
	OrderType     *string          `json:"type"`   // Market/Limit/Trigger
	Symbol        *string          `json:"symbol"` // "BTC-USDT-PERP"
	IsBuy         *bool            `json:"is_buy"`
	Leverage      *int             `json:"leverage"`
	Margin        *decimal.Decimal `json:"margin"`
	IsMarket      *bool            `json:"is_market"`
	TriggerPx     *string          `json:"trigger_px"`
	Tpsl          *string          `json:"tpsl"`
	Tif           *string          `json:"tif"`   // Time In Force: Alo/Ioc/Gtc
	Base          *string          `json:"base"`  // "BTC"
	Quote         *string          `json:"quote"` // "USDC"
	Size          *decimal.Decimal `json:"size"`
	Price         *decimal.Decimal `json:"price"`
	AvgPrice      *decimal.Decimal `json:"avg_price"`
	BuildFee      *decimal.Decimal `json:"build_fee"`
	TotalFee      *string          `json:"total_fee"`
	FeeBp         *int             `json:"fee_bp"`
	BuildAddress  *string          `json:"build_address"`
	Status        *string          `json:"status"`
	OID           int64            `json:"oid"`
	CreatedAt     *string          `json:"created_at"`
	TotalSz       *string          `json:"total_sz"`
	Hash          *string          `json:"hash"`
	Asset         *string          `json:"asset"`
	Coin          string           `json:"coin"`
	ReduceOnly    *bool            `json:"reduce_only"`
	Grouping      *string          `json:"grouping"`
	Operation     *string          `json:"operation"`
}

type HyperLiquidTransactionEventBatch struct {
	Items []HyperLiquidTransactionEvent `json:"items"`
}

type RewardClaimEvent struct {
	ID        uuid.UUID       `json:"id"`
	UserID    uuid.UUID       `json:"user_id"`
	Address   string          `json:"address"`
	Amount    decimal.Decimal `json:"amount"`
	Token     string          `json:"token"`
	ChainID   int             `json:"chain_id"`
	CreatedAt string          `json:"created_at"`
}

type RewardClaimResult string

const (
	RewardClaimResultSuccess RewardClaimResult = "success"
	RewardClaimResultFailed  RewardClaimResult = "failed"
)

type RewardClaimResultEvent struct {
	ID           uuid.UUID         `json:"id"`
	UserID       uuid.UUID         `json:"user_id"`
	Address      string            `json:"address"`
	Amount       decimal.Decimal   `json:"amount"`
	Token        string            `json:"token"`
	ChainID      int               `json:"chain_id"`
	CreatedAt    string            `json:"created_at"`
	Result       RewardClaimResult `json:"result"`
	ErrorMessage *string           `json:"error_message,omitempty"`
	ErrorCode    *string           `json:"error_code,omitempty"`
}
