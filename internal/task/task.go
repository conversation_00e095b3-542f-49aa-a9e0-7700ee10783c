package task

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/robfig/cron/v3"
)

type TaskScheduler struct {
	cron       *cron.Cron
	tasks      map[string]cron.EntryID
	cancelFunc context.CancelFunc
}

func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		cron:  cron.New(cron.WithSeconds()),
		tasks: make(map[string]cron.EntryID),
	}
}

func (s *TaskScheduler) SetCancelFunc(cancel context.CancelFunc) {
	s.cancelFunc = cancel
}

func (s *TaskScheduler) Register(id string, expr string, job func()) error {
	if _, exists := s.tasks[id]; exists {
		return fmt.Errorf("task ID %s already exists", id)
	}
	entryID, err := s.cron.AddFunc(expr, job)
	if err != nil {
		return fmt.Errorf("register task failed.: %w", err)
	}
	s.tasks[id] = entryID
	log.Printf("✅ register [%s] Success，Cron expression: %s\n", id, expr)
	return nil
}

func (s *TaskScheduler) Remove(id string) {
	if entryID, ok := s.tasks[id]; ok {
		s.cron.Remove(entryID)
		delete(s.tasks, id)
		log.Printf("🛑 task removed [%s]\n", id)
	} else {
		log.Printf("⚠️ task ID [%s] does not exist\n", id)
	}
}

func (s *TaskScheduler) Start() {
	s.cron.Start()
	log.Println("🚀 task scheduler has started")
}

func (s *TaskScheduler) Stop() {
	ctx := s.cron.Stop()
	<-ctx.Done()
	log.Println("🛑 the scheduler has gracefully closed")
}

func (s *TaskScheduler) RunWithSignal(ctx context.Context) {
	go s.Start()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		log.Println("🔔 Upon receiving the exit signal, close the task scheduler...")
		s.Stop()
		if s.cancelFunc != nil {
			s.cancelFunc()
		}
	case <-ctx.Done():
		log.Println("🛑 Context Cancel, close the scheduler...")
		s.Stop()
	}
}
