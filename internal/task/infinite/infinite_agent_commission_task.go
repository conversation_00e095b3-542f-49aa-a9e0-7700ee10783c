package infinite

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InfiniteAgentCommissionTask 无限代理收益计算任务
type InfiniteAgentCommissionTask struct{}

// NewInfiniteAgentCommissionTask 创建新的无限代理收益计算任务实例
func NewInfiniteAgentCommissionTask() *InfiniteAgentCommissionTask {
	return &InfiniteAgentCommissionTask{}
}

// CalculateInfiniteAgentCommissions 计算无限代理收益
// 每日0:05分执行，计算所有活跃无限代理的收益
func (t *InfiniteAgentCommissionTask) CalculateInfiniteAgentCommissions() {
	global.GVA_LOG.Info("开始执行无限代理收益计算任务")

	// 获取前一天时间作为计算日期
	calculationDate := time.Now().UTC().AddDate(0, 0, -1)
	calculationDateStr := calculationDate.Format("2006-01-02 15:04:05")

	global.GVA_LOG.Info("计算日期", zap.String("date", calculationDateStr))

	// 获取所有活跃的无限代理配置
	activeInfiniteAgents, err := t.GetActiveInfiniteAgentConfigs()
	if err != nil {
		global.GVA_LOG.Error("获取活跃无限代理失败", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("找到活跃无限代理", zap.Int("infinite_agent_count", len(activeInfiniteAgents)))

	processedCount := 0
	errorCount := 0

	for _, infiniteAgent := range activeInfiniteAgents {
		if err := t.ProcessInfiniteAgentCommission(infiniteAgent); err != nil {
			global.GVA_LOG.Error("处理无限代理收益计算失败",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("无限代理收益计算任务完成",
		zap.String("date", calculationDateStr),
		zap.Int("total_infinite_agents", len(activeInfiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// GetActiveInfiniteAgentConfigs 获取所有状态为ACTIVE的无限代理配置
func (t *InfiniteAgentCommissionTask) GetActiveInfiniteAgentConfigs() ([]model.InfiniteAgentConfig, error) {
	var infiniteAgents []model.InfiniteAgentConfig

	err := global.GVA_DB.Where("status = ?", "ACTIVE").Find(&infiniteAgents).Error
	if err != nil {
		return nil, fmt.Errorf("查询活跃无限代理失败: %w", err)
	}

	return infiniteAgents, nil
}

// ProcessInfiniteAgentCommission 处理单个无限代理的收益计算
func (t *InfiniteAgentCommissionTask) ProcessInfiniteAgentCommission(infiniteAgent model.InfiniteAgentConfig) error {
	global.GVA_LOG.Debug("开始处理无限代理收益计算",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()))

	// 计算无限代理树的收益
	commissionData, err := t.calculateInfiniteAgentTreeCommission(infiniteAgent.UserID)
	if err != nil {
		return fmt.Errorf("计算无限代理树收益失败: %w", err)
	}

	// 计算最终收益金额
	// 修复：确保净费用不为负值，如果为负值则设为0
	effectiveNetFeeUSD := commissionData.TotalNetFeeUSD
	if effectiveNetFeeUSD.IsNegative() {
		global.GVA_LOG.Warn("检测到负的净费用，将其设为0",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.String("original_net_fee_usd", commissionData.TotalNetFeeUSD.String()))
		effectiveNetFeeUSD = decimal.Zero
	}

	// 计算无限代理应得的佣金：净费用 * 佣金率
	infiniteAgentCommission := effectiveNetFeeUSD.Mul(infiniteAgent.CommissionRateN)

	// 最终收益 = 无限代理佣金 - 已支付的标准佣金
	// 如果结果小于0，说明已支付的佣金超过了应得佣金，设为0
	finalCommissionAmount := infiniteAgentCommission.Sub(commissionData.TotalStandardCommissionPaidUSD)
	if finalCommissionAmount.IsNegative() {
		global.GVA_LOG.Warn("最终收益为负值，已支付的佣金超过应得佣金，设为0",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.String("infinite_agent_commission", infiniteAgentCommission.String()),
			zap.String("total_standard_commission_paid_usd", commissionData.TotalStandardCommissionPaidUSD.String()),
			zap.String("calculated_final_amount", finalCommissionAmount.String()))
		finalCommissionAmount = decimal.Zero
	}

	// 更新无限代理配置的收益数据
	err = t.updateInfiniteAgentCommissionData(infiniteAgent.ID, commissionData, finalCommissionAmount)
	if err != nil {
		return fmt.Errorf("更新无限代理收益数据失败: %w", err)
	}

	global.GVA_LOG.Debug("无限代理收益计算完成",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.String("total_net_fee_usd", commissionData.TotalNetFeeUSD.String()),
		zap.String("effective_net_fee_usd", effectiveNetFeeUSD.String()),
		zap.String("infinite_agent_commission", infiniteAgentCommission.String()),
		zap.String("total_standard_commission_paid_usd", commissionData.TotalStandardCommissionPaidUSD.String()),
		zap.String("final_commission_amount_usd", finalCommissionAmount.String()))

	return nil
}

// InfiniteAgentCommissionData 无限代理收益数据结构
type InfiniteAgentCommissionData struct {
	TotalNetFeeUSD                 decimal.Decimal
	TotalStandardCommissionPaidUSD decimal.Decimal
}

// calculateInfiniteAgentTreeCommission 计算无限代理树的收益
func (t *InfiniteAgentCommissionTask) calculateInfiniteAgentTreeCommission(infiniteAgentUserID uuid.UUID) (*InfiniteAgentCommissionData, error) {
	global.GVA_LOG.Debug("开始计算无限代理树收益",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))

	// 查找无限代理用户所在的推荐树快照
	var snapshot model.ReferralTreeSnapshot
	err := global.GVA_DB.Where("infinite_agent_user_id = ? AND is_valid = true", infiniteAgentUserID).
		First(&snapshot).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Warn("未找到无限代理用户的推荐树快照",
				zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))
			return &InfiniteAgentCommissionData{
				TotalNetFeeUSD:                 decimal.Zero,
				TotalStandardCommissionPaidUSD: decimal.Zero,
			}, nil
		}
		return nil, fmt.Errorf("查询推荐树快照失败: %w", err)
	}

	global.GVA_LOG.Debug("找到推荐树快照",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.Uint("snapshot_id", snapshot.ID))

	// 获取该树下的所有节点用户ID
	var treeNodeUserIDs []uuid.UUID
	err = global.GVA_DB.Model(&model.ReferralTreeNode{}).
		Where("tree_snapshot_id = ?", snapshot.ID).
		Pluck("user_id", &treeNodeUserIDs).Error
	if err != nil {
		return nil, fmt.Errorf("查询推荐树节点用户ID失败: %w", err)
	}

	// 对用户ID进行去重，避免重复计算
	treeNodeUserIDs = t.removeDuplicateUserIDs(treeNodeUserIDs)

	if len(treeNodeUserIDs) == 0 {
		global.GVA_LOG.Warn("推荐树下没有节点用户",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Uint("snapshot_id", snapshot.ID))
		return &InfiniteAgentCommissionData{
			TotalNetFeeUSD:                 decimal.Zero,
			TotalStandardCommissionPaidUSD: decimal.Zero,
		}, nil
	}

	global.GVA_LOG.Debug("获取到树节点用户",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.Int("user_count", len(treeNodeUserIDs)))

	// 分别计算meme返佣和合约返佣的净值
	memeNetFeeUSD, err := t.calculateMemeNetFeeUSD(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("计算meme净费用失败: %w", err)
	}

	contractNetFeeUSD, err := t.calculateContractNetFeeUSD(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("计算合约净费用失败: %w", err)
	}

	// 将合约净值与meme净值相加 = total净值
	totalNetFeeUSD := memeNetFeeUSD.Add(contractNetFeeUSD)

	global.GVA_LOG.Debug("计算净费用完成",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.String("meme_net_fee_usd", memeNetFeeUSD.String()),
		zap.String("contract_net_fee_usd", contractNetFeeUSD.String()),
		zap.String("total_net_fee_usd", totalNetFeeUSD.String()))

	// 计算已支付的标准佣金总额
	totalStandardCommissionPaidUSD, err := t.calculateTotalStandardCommissionPaidUSD(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("计算已支付标准佣金总额失败: %w", err)
	}

	global.GVA_LOG.Debug("计算标准佣金完成",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.String("total_standard_commission_paid_usd", totalStandardCommissionPaidUSD.String()))

	return &InfiniteAgentCommissionData{
		TotalNetFeeUSD:                 totalNetFeeUSD,
		TotalStandardCommissionPaidUSD: totalStandardCommissionPaidUSD,
	}, nil
}

// calculateMemeNetFeeUSD 计算meme净费用
// meme总手续费查询activity_cashback表的CashbackAmountUSD字段，查看无限代理下的用户返佣
// 通过MemeCommissionLedger表 in treeNodeUserIDs, 得到总的 = 已返佣
// meme 净值 = total - 已返佣
func (t *InfiniteAgentCommissionTask) calculateMemeNetFeeUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	// 输入验证
	if err := t.validateUserIDs(userIDs); err != nil {
		global.GVA_LOG.Debug("用户ID验证失败", zap.Error(err))
		return decimal.Zero, err
	}

	global.GVA_LOG.Debug("开始计算meme净费用", zap.Int("user_count", len(userIDs)))

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 1. 计算meme总手续费 - 从activity_cashback表查询CashbackAmountUSD
	var memeTotalFeeResult struct {
		TotalMemeFee decimal.Decimal `json:"total_meme_fee"`
	}

	memeTotalFeeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(ac.cashback_amount_usd), 0) as total_meme_fee
		FROM activity_cashback ac
		WHERE ac.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(memeTotalFeeQuery, args...).Scan(&memeTotalFeeResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询meme总手续费失败",
			zap.Error(err),
			zap.String("query", memeTotalFeeQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询meme总手续费失败: %w", err)
	}

	// 2. 计算已返佣的meme佣金 - 从MemeCommissionLedger表查询
	var memePaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	memePaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(mcl.commission_amount), 0) as total_paid_commission
		FROM meme_commission_ledger mcl
		WHERE mcl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Raw(memePaidCommissionQuery, args...).Scan(&memePaidCommissionResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询已返佣meme佣金失败",
			zap.Error(err),
			zap.String("query", memePaidCommissionQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询已返佣meme佣金失败: %w", err)
	}

	// 3. 计算meme净值 = 总手续费 - 已返佣
	memeNetFee := memeTotalFeeResult.TotalMemeFee.Sub(memePaidCommissionResult.TotalPaidCommission)

	// 确保净值不为负值
	if memeNetFee.IsNegative() {
		global.GVA_LOG.Warn("meme净费用为负值，设为0",
			zap.String("total_meme_fee", memeTotalFeeResult.TotalMemeFee.String()),
			zap.String("total_paid_commission", memePaidCommissionResult.TotalPaidCommission.String()),
			zap.String("calculated_net_fee", memeNetFee.String()))
		memeNetFee = decimal.Zero
	}

	global.GVA_LOG.Debug("meme净费用计算完成",
		zap.String("total_meme_fee", memeTotalFeeResult.TotalMemeFee.String()),
		zap.String("total_paid_commission", memePaidCommissionResult.TotalPaidCommission.String()),
		zap.String("meme_net_fee", memeNetFee.String()))

	return memeNetFee, nil
}

// calculateContractNetFeeUSD 计算合约净费用
// 合约返佣总费用要查HyperLiquidTransaction表的BuildFee = total
// 合约已经返佣查CommissionLedger表in treeNodeUserIDs, 得到总的 = 已返佣
// 合约 净值 = total - 已返佣
func (t *InfiniteAgentCommissionTask) calculateContractNetFeeUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	// 输入验证
	if err := t.validateUserIDs(userIDs); err != nil {
		global.GVA_LOG.Debug("用户ID验证失败", zap.Error(err))
		return decimal.Zero, err
	}

	global.GVA_LOG.Debug("开始计算合约净费用", zap.Int("user_count", len(userIDs)))

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 1. 计算合约总费用 - 从HyperLiquidTransaction表查询BuildFee
	var contractTotalFeeResult struct {
		TotalContractFee decimal.Decimal `json:"total_contract_fee"`
	}

	contractTotalFeeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(COALESCE(hlt.build_fee, 0)), 0) as total_contract_fee
		FROM hyperliquid_transactions hlt
		WHERE hlt.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(contractTotalFeeQuery, args...).Scan(&contractTotalFeeResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询合约总费用失败",
			zap.Error(err),
			zap.String("query", contractTotalFeeQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询合约总费用失败: %w", err)
	}

	// 2. 计算已返佣的合约佣金 - 从CommissionLedger表查询
	var contractPaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	contractPaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(cl.commission_amount), 0) as total_paid_commission
		FROM commission_ledger cl
		WHERE cl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Raw(contractPaidCommissionQuery, args...).Scan(&contractPaidCommissionResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询已返佣合约佣金失败",
			zap.Error(err),
			zap.String("query", contractPaidCommissionQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询已返佣合约佣金失败: %w", err)
	}

	// 3. 计算合约净值 = 总费用 - 已返佣
	contractNetFee := contractTotalFeeResult.TotalContractFee.Sub(contractPaidCommissionResult.TotalPaidCommission)

	// 确保净值不为负值
	if contractNetFee.IsNegative() {
		global.GVA_LOG.Warn("合约净费用为负值，设为0",
			zap.String("total_contract_fee", contractTotalFeeResult.TotalContractFee.String()),
			zap.String("total_paid_commission", contractPaidCommissionResult.TotalPaidCommission.String()),
			zap.String("calculated_net_fee", contractNetFee.String()))
		contractNetFee = decimal.Zero
	}

	global.GVA_LOG.Debug("合约净费用计算完成",
		zap.String("total_contract_fee", contractTotalFeeResult.TotalContractFee.String()),
		zap.String("total_paid_commission", contractPaidCommissionResult.TotalPaidCommission.String()),
		zap.String("contract_net_fee", contractNetFee.String()))

	return contractNetFee, nil
}

// calculateTotalNetFeeUSD 计算总净费用（保留原方法作为备用）
func (t *InfiniteAgentCommissionTask) calculateTotalNetFeeUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalNetFee decimal.Decimal `json:"total_net_fee"`
	}

	// 计算每个用户的净费用：TotalPerpsFees - (totalPerpsFeesPaid + totalPerpsFeesUnPaid)
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 修复：计算实际可用的净费用
	// 净费用应该是：总费用 - 已支付费用 - 未支付费用
	// 如果结果为负值，说明费用已经被完全支付，净费用为0
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(
			GREATEST(
				rs.total_perps_fees - rs.total_perps_fees_paid - rs.total_perps_fees_un_paid,
				0
			)
		), 0) as total_net_fee
		FROM referral_snapshots rs
		WHERE rs.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(query, args...).Scan(&result).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("查询总净费用失败: %w", err)
	}

	return result.TotalNetFee, nil
}

// calculateTotalStandardCommissionPaidUSD 计算已支付的标准佣金总额
// 包括meme佣金和合约佣金的已支付总额
func (t *InfiniteAgentCommissionTask) calculateTotalStandardCommissionPaidUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	// 输入验证
	if err := t.validateUserIDs(userIDs); err != nil {
		global.GVA_LOG.Debug("用户ID验证失败", zap.Error(err))
		return decimal.Zero, err
	}

	global.GVA_LOG.Debug("开始计算已支付标准佣金总额", zap.Int("user_count", len(userIDs)))

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 1. 计算已支付的meme标准佣金 - 从MemeCommissionLedger表查询
	var memePaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	memePaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(mcl.commission_amount), 0) as total_paid_commission
		FROM meme_commission_ledger mcl
		WHERE mcl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(memePaidCommissionQuery, args...).Scan(&memePaidCommissionResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询已支付meme标准佣金失败",
			zap.Error(err),
			zap.String("query", memePaidCommissionQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询已支付meme标准佣金失败: %w", err)
	}

	// 2. 计算已支付的合约标准佣金 - 从CommissionLedger表查询
	var contractPaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	contractPaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(cl.commission_amount), 0) as total_paid_commission
		FROM commission_ledger cl
		WHERE cl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Raw(contractPaidCommissionQuery, args...).Scan(&contractPaidCommissionResult).Error
	if err != nil {
		global.GVA_LOG.Error("查询已支付合约标准佣金失败",
			zap.Error(err),
			zap.String("query", contractPaidCommissionQuery),
			zap.Int("user_count", len(userIDs)))
		return decimal.Zero, fmt.Errorf("查询已支付合约标准佣金失败: %w", err)
	}

	// 3. 计算总已支付标准佣金 = meme已支付佣金 + 合约已支付佣金
	totalPaidCommission := memePaidCommissionResult.TotalPaidCommission.Add(contractPaidCommissionResult.TotalPaidCommission)

	global.GVA_LOG.Debug("已支付标准佣金计算完成",
		zap.String("meme_paid_commission", memePaidCommissionResult.TotalPaidCommission.String()),
		zap.String("contract_paid_commission", contractPaidCommissionResult.TotalPaidCommission.String()),
		zap.String("total_paid_commission", totalPaidCommission.String()))

	return totalPaidCommission, nil
}

// updateInfiniteAgentCommissionData 更新无限代理配置的收益数据
func (t *InfiniteAgentCommissionTask) updateInfiniteAgentCommissionData(
	configID uuid.UUID,
	commissionData *InfiniteAgentCommissionData,
	finalCommissionAmount decimal.Decimal,
) error {
	// 计算最终佣金金额
	finalCommission := finalCommissionAmount

	// 更新数据库
	updates := map[string]interface{}{
		"total_net_fee_usd":                  commissionData.TotalNetFeeUSD,
		"total_standard_commission_paid_usd": commissionData.TotalStandardCommissionPaidUSD,
		"final_commission_amount_usd":        finalCommission,
		"updated_at":                         time.Now().UTC(),
	}

	err := global.GVA_DB.Model(&model.InfiniteAgentConfig{}).
		Where("id = ?", configID).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新无限代理收益数据失败: %w", err)
	}

	return nil
}

// validateUserIDs 验证用户ID列表的有效性
func (t *InfiniteAgentCommissionTask) validateUserIDs(userIDs []uuid.UUID) error {
	if len(userIDs) == 0 {
		return fmt.Errorf("用户ID列表不能为空")
	}

	for i, userID := range userIDs {
		if userID == uuid.Nil {
			return fmt.Errorf("第%d个用户ID为空", i+1)
		}
	}

	return nil
}

// removeDuplicateUserIDs 去除重复的用户ID
func (t *InfiniteAgentCommissionTask) removeDuplicateUserIDs(userIDs []uuid.UUID) []uuid.UUID {
	if len(userIDs) == 0 {
		return userIDs
	}

	// 使用map来记录已存在的用户ID
	seen := make(map[uuid.UUID]bool)
	var uniqueUserIDs []uuid.UUID

	for _, userID := range userIDs {
		if !seen[userID] {
			seen[userID] = true
			uniqueUserIDs = append(uniqueUserIDs, userID)
		}
	}

	global.GVA_LOG.Debug("用户ID去重完成",
		zap.Int("original_count", len(userIDs)),
		zap.Int("unique_count", len(uniqueUserIDs)))

	return uniqueUserIDs
}
