package infinite

import (
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestInfiniteAgentCommissionTask_NewInfiniteAgentCommissionTask(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()
	assert.NotNil(t, task)
}

func TestInfiniteAgentCommissionTask_GetActiveInfiniteAgentConfigs(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	configs, err := task.GetActiveInfiniteAgentConfigs()
	assert.NoError(t, err)
	assert.NotNil(t, configs)
}

func TestInfiniteAgentCommissionTask_ProcessInfiniteAgentCommission(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 创建测试用的无限代理配置
	testConfig := model.InfiniteAgentConfig{
		ID:              uuid.New(),
		UserID:          uuid.New(),
		CommissionRateN: decimal.NewFromFloat(0.05), // 5% 佣金率
		Status:          "ACTIVE",
	}

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	err := task.ProcessInfiniteAgentCommission(testConfig)
	assert.NoError(t, err)
}

func TestInfiniteAgentCommissionTask_CalculateTotalNetFeeUSD(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 创建测试用户ID
	testUserIDs := []uuid.UUID{
		uuid.New(),
		uuid.New(),
	}

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	totalNetFee, err := task.calculateTotalNetFeeUSD(testUserIDs)
	assert.NoError(t, err)
	assert.NotNil(t, totalNetFee)
}

func TestInfiniteAgentCommissionTask_CalculateTotalStandardCommissionPaidUSD(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 创建测试用户ID
	testUserIDs := []uuid.UUID{
		uuid.New(),
		uuid.New(),
		uuid.New(),
	}

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	totalCommission, err := task.calculateTotalStandardCommissionPaidUSD(testUserIDs)
	assert.NoError(t, err)
	assert.NotNil(t, totalCommission)
}

func TestInfiniteAgentCommissionTask_UpdateInfiniteAgentCommissionData(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 创建测试数据
	configID := uuid.New()
	commissionData := &InfiniteAgentCommissionData{
		TotalNetFeeUSD:                 decimal.NewFromFloat(1000.0),
		TotalStandardCommissionPaidUSD: decimal.NewFromFloat(100.0),
	}
	finalCommissionAmount := decimal.NewFromFloat(50.0)

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	err := task.updateInfiniteAgentCommissionData(configID, commissionData, finalCommissionAmount)
	assert.NoError(t, err)
}

func TestInfiniteAgentCommissionTask_CalculateInfiniteAgentTreeCommission(t *testing.T) {
	task := NewInfiniteAgentCommissionTask()

	// 创建测试无限代理用户ID
	testInfiniteAgentUserID := uuid.New()

	// 这个测试需要数据库连接，在实际环境中运行
	// 暂时跳过
	t.Skip("需要数据库连接，跳过测试")

	commissionData, err := task.calculateInfiniteAgentTreeCommission(testInfiniteAgentUserID)
	assert.NoError(t, err)
	assert.NotNil(t, commissionData)
}

// 测试数据结构
func TestInfiniteAgentCommissionData_Structure(t *testing.T) {
	commissionData := &InfiniteAgentCommissionData{
		TotalNetFeeUSD:                 decimal.NewFromFloat(1000.0),
		TotalStandardCommissionPaidUSD: decimal.NewFromFloat(100.0),
	}

	assert.NotNil(t, commissionData)
	assert.Equal(t, decimal.NewFromFloat(1000.0), commissionData.TotalNetFeeUSD)
	assert.Equal(t, decimal.NewFromFloat(100.0), commissionData.TotalStandardCommissionPaidUSD)
}

// 测试佣金计算逻辑
func TestCommissionCalculationLogic(t *testing.T) {
	// 模拟佣金计算逻辑
	totalNetFeeUSD := decimal.NewFromFloat(1000.0)                // 总净费用 $1000
	commissionRateN := decimal.NewFromFloat(0.05)                 // 佣金率 5%
	totalStandardCommissionPaidUSD := decimal.NewFromFloat(100.0) // 已支付标准佣金 $100

	// 计算最终佣金金额：总净费用 * 佣金率 - 已支付标准佣金
	expectedCommission := totalNetFeeUSD.Mul(commissionRateN).Sub(totalStandardCommissionPaidUSD)
	// 期望结果：1000 * 0.05 - 100 = 50 - 100 = -50

	// 使用 String() 比较来避免精度问题
	assert.Equal(t, "-50", expectedCommission.String())

	// 测试边界情况
	zeroNetFee := decimal.Zero
	zeroCommission := zeroNetFee.Mul(commissionRateN).Sub(totalStandardCommissionPaidUSD)
	assert.Equal(t, "-100", zeroCommission.String())
}
