package task

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral"
	"go.uber.org/zap"
)

func ConsumeUserSyncInfoEvent(msg *nats.Msg) error {
	logger := global.GVA_LOG

	var event UserNewWalletEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		logger.Error("failed to unmarshal: %v", zap.Error(err))
		return fmt.Errorf("failed to unmarshal: %v", err)
	}

	ctx := context.Background()
	agentReferralService := agent_referral.NewAgentReferralService()
	// userService := service.NewUserService()
	for _, wallet := range event.Wallets {
		_, err := agentReferralService.CreateUserWallet(ctx, event.UserID, wallet.Chain, wallet.WalletAddress, &wallet.WalletAccountID, &wallet.WalletID)
		if err != nil {
			logger.Error("failed to update user wallet",
				zap.String("user_id", event.UserID.String()),
				zap.Error(err),
			)
			continue
		}
	}

	logger.Info("Received UserSyncInfoEvent",
		zap.String("user_id", event.UserID.String()),
	)

	return nil
}
