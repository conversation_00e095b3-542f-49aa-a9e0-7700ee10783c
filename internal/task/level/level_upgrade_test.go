package level

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

func TestLevelUpgradeTask_NewLevelUpgradeTask(t *testing.T) {
	task := NewLevelUpgradeTask()
	assert.NotNil(t, task)
}

func TestLevelUpgradeTask_DetermineTargetLevel(t *testing.T) {
	task := NewLevelUpgradeTask()
	fixtures := test.NewTestFixtures()

	// Create test levels
	levels := []model.AgentLevel{
		*fixtures.CreateTestAgentLevelWithID(1, "Lv1"), // Meme: 1000, Contract: 5000
		*fixtures.CreateTestAgentLevelWithID(2, "Lv2"), // Meme: 2000, Contract: 10000
		*fixtures.CreateTestAgentLevelWithID(3, "Lv3"), // Meme: 3000, Contract: 15000
	}

	tests := []struct {
		name           string
		memeVolume     decimal.Decimal
		contractVolume decimal.Decimal
		expectedLevel  uint
	}{
		{
			name:           "meets level 1 with meme volume",
			memeVolume:     decimal.NewFromFloat(1500.0),
			contractVolume: decimal.Zero,
			expectedLevel:  1,
		},
		{
			name:           "meets level 2 with contract volume",
			memeVolume:     decimal.Zero,
			contractVolume: decimal.NewFromFloat(12000.0),
			expectedLevel:  2,
		},
		{
			name:           "meets level 3 with both volumes",
			memeVolume:     decimal.NewFromFloat(3500.0),
			contractVolume: decimal.NewFromFloat(16000.0),
			expectedLevel:  3,
		},
		{
			name:           "does not meet any level",
			memeVolume:     decimal.NewFromFloat(500.0),
			contractVolume: decimal.NewFromFloat(2000.0),
			expectedLevel:  1, // Default to lowest level
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := task.determineTargetLevel(tt.memeVolume, tt.contractVolume, levels)
			assert.Equal(t, tt.expectedLevel, result.ID)
		})
	}
}

func TestLevelUpgradeTask_MeetsLevelRequirements(t *testing.T) {
	task := NewLevelUpgradeTask()
	fixtures := test.NewTestFixtures()

	level := fixtures.CreateTestAgentLevel() // Meme: 1000, Contract: 5000

	tests := []struct {
		name           string
		memeVolume     decimal.Decimal
		contractVolume decimal.Decimal
		expected       bool
	}{
		{
			name:           "meets meme threshold",
			memeVolume:     decimal.NewFromFloat(1500.0),
			contractVolume: decimal.Zero,
			expected:       true,
		},
		{
			name:           "meets contract threshold",
			memeVolume:     decimal.Zero,
			contractVolume: decimal.NewFromFloat(6000.0),
			expected:       true,
		},
		{
			name:           "meets both thresholds",
			memeVolume:     decimal.NewFromFloat(1500.0),
			contractVolume: decimal.NewFromFloat(6000.0),
			expected:       true,
		},
		{
			name:           "does not meet any threshold",
			memeVolume:     decimal.NewFromFloat(500.0),
			contractVolume: decimal.NewFromFloat(2000.0),
			expected:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := task.meetsLevelRequirements(tt.memeVolume, tt.contractVolume, *level)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestLevelUpgradeTask_ProcessUserLevelChange(t *testing.T) {
	task := NewLevelUpgradeTask()
	fixtures := test.NewTestFixtures()

	// Create test user
	userID := uuid.New()
	user := model.User{
		ID:           userID,
		AgentLevelID: 1, // Low level
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create test level
	level := fixtures.CreateTestAgentLevel()
	user.AgentLevel = *level

	t.Run("user meets upgrade requirements", func(t *testing.T) {
		// Mock the calculateUserVolume method by creating a test task
		// This is a simplified test that focuses on the logic flow

		// Test with high volumes that should trigger upgrade
		memeVolume := decimal.NewFromFloat(2000.0)     // Above threshold
		contractVolume := decimal.NewFromFloat(6000.0) // Above threshold

		// Test the level determination logic
		levels := []model.AgentLevel{*level}
		targetLevel := task.determineTargetLevel(memeVolume, contractVolume, levels)

		// Should upgrade to the level that meets requirements
		assert.True(t, targetLevel.ID >= user.AgentLevelID)
	})

	t.Run("user does not meet upgrade requirements", func(t *testing.T) {
		// Test with low volumes that should not trigger upgrade
		memeVolume := decimal.NewFromFloat(500.0)      // Below threshold
		contractVolume := decimal.NewFromFloat(2000.0) // Below threshold

		// Test the level determination logic
		levels := []model.AgentLevel{*level}
		targetLevel := task.determineTargetLevel(memeVolume, contractVolume, levels)

		// Should stay at current level or lower
		assert.True(t, targetLevel.ID <= user.AgentLevelID)
	})
}

func TestLevelUpgradeTask_AggregateTransactionData(t *testing.T) {
	task := NewLevelUpgradeTask()

	// This test verifies the method can be called without panicking
	t.Run("aggregate transaction data", func(t *testing.T) {
		// Note: This would require database setup for full testing
		// For now, we just test that the method exists and can be called
		assert.NotNil(t, task.aggregateTransactionData)
	})
}

func TestLevelUpgradeTask_AggregateAllTransactions(t *testing.T) {
	task := NewLevelUpgradeTask()

	// This test verifies the method can be called without panicking
	t.Run("aggregate all transactions", func(t *testing.T) {
		// Note: This would require database setup for full testing
		// For now, we just test that the method exists and can be called
		date := time.Now().UTC().AddDate(0, 0, -1)
		assert.NotNil(t, task.aggregateAllTransactions)
		assert.NotNil(t, date)
	})
}

func TestLevelUpgradeTask_GracePeriodLogic(t *testing.T) {
	task := NewLevelUpgradeTask()
	fixtures := test.NewTestFixtures()

	// Create test user
	userID := uuid.New()
	user := model.User{
		ID:           userID,
		AgentLevelID: 3, // High level
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create test levels
	levels := []model.AgentLevel{
		*fixtures.CreateTestAgentLevelWithID(1, "Lv1"), // Meme: 1000, Contract: 5000
		*fixtures.CreateTestAgentLevelWithID(2, "Lv2"), // Meme: 2000, Contract: 10000
		*fixtures.CreateTestAgentLevelWithID(3, "Lv3"), // Meme: 3000, Contract: 15000
	}

	t.Run("grace period calculation logic", func(t *testing.T) {
		// Test grace period calculation logic without database access
		now := time.Now().UTC()

		// Test day 15 (observation phase)
		graceStart15 := now.AddDate(0, 0, -15)
		daysSinceStart15 := int(now.Sub(graceStart15).Hours() / 24)
		assert.Equal(t, 15, daysSinceStart15)
		assert.True(t, daysSinceStart15 <= 30) // Should be in observation phase

		// Test day 45 (warning phase)
		graceStart45 := now.AddDate(0, 0, -45)
		daysSinceStart45 := int(now.Sub(graceStart45).Hours() / 24)
		assert.Equal(t, 45, daysSinceStart45)
		assert.True(t, daysSinceStart45 > 30 && daysSinceStart45 <= 60) // Should be in warning phase

		// Test day 70 (downgrade phase)
		graceStart70 := now.AddDate(0, 0, -70)
		daysSinceStart70 := int(now.Sub(graceStart70).Hours() / 24)
		assert.Equal(t, 70, daysSinceStart70)
		assert.True(t, daysSinceStart70 > 60) // Should be in downgrade phase
	})

	t.Run("level determination for downgrade", func(t *testing.T) {
		// Test that low volumes result in lower target level
		lowMemeVolume := decimal.NewFromFloat(500.0)
		lowContractVolume := decimal.NewFromFloat(2000.0)

		targetLevel := task.determineTargetLevel(lowMemeVolume, lowContractVolume, levels)

		// Should target level 1 (lowest) since volumes are below all thresholds
		assert.Equal(t, uint(1), targetLevel.ID)
		assert.True(t, targetLevel.ID < user.AgentLevelID) // Should be lower than current level
	})
}

func TestLevelUpgradeTask_VolumeCalculationLogic(t *testing.T) {

	t.Run("decimal operations", func(t *testing.T) {
		// Test decimal operations used in volume calculations
		memeVolume := decimal.NewFromFloat(1000.0)
		contractVolume := decimal.NewFromFloat(2000.0)

		// Test addition
		totalVolume := memeVolume.Add(contractVolume)
		expectedTotal := decimal.NewFromFloat(3000.0)
		assert.Equal(t, expectedTotal, totalVolume)

		// Test comparison
		assert.True(t, memeVolume.LessThan(contractVolume))
		assert.True(t, contractVolume.GreaterThan(memeVolume))
		assert.True(t, totalVolume.GreaterThanOrEqual(expectedTotal))
	})

	t.Run("threshold comparisons", func(t *testing.T) {
		// Test threshold comparison logic
		userVolume := decimal.NewFromFloat(1500.0)
		threshold := decimal.NewFromFloat(1000.0)

		// Should meet threshold
		assert.True(t, userVolume.GreaterThanOrEqual(threshold))

		// Should not meet higher threshold
		higherThreshold := decimal.NewFromFloat(2000.0)
		assert.False(t, userVolume.GreaterThanOrEqual(higherThreshold))
	})
}
