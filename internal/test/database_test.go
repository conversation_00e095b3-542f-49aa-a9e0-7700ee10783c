package test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

func TestDatabaseSetup(t *testing.T) {
	// Setup test database
	SetupTestConfig()
	db := SetupTestDB()
	defer CleanupTestConfig()

	// Verify database connection
	assert.NotNil(t, db)
	assert.NotNil(t, global.GVA_DB)

	// Check if tables exist
	tables := []string{
		"users",
		"task_categories", 
		"tier_benefits",
		"activity_tasks",
		"user_tier_info",
		"user_task_progress",
		"task_completion_history",
		"activity_cashback_claims",
	}

	for _, table := range tables {
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", table).Scan(&count).Error
		assert.NoError(t, err, "Failed to check table %s", table)
		assert.Equal(t, int64(1), count, "Table %s should exist", table)
	}
}
