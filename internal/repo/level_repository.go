package repo

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type LevelRepository struct{}

func NewLevelRepository() LevelRepo {
	return &LevelRepository{}
}

// GetAgentLevels retrieves all agent levels
func (r *LevelRepository) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	var levels []model.AgentLevel
	err := global.GVA_DB.WithContext(ctx).Find(&levels).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}
	return levels, nil
}

// GetAgentLevelByID retrieves a specific agent level by ID
func (r *LevelRepository) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	var level model.AgentLevel
	err := global.GVA_DB.WithContext(ctx).Where("id = ?", id).First(&level).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level by ID %d: %w", id, err)
	}
	return &level, nil
}

// UpdateAgentLevel updates an agent level's commission rates and meme fee rebate
func (r *LevelRepository) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	err := global.GVA_DB.WithContext(ctx).Save(level).Error
	if err != nil {
		return fmt.Errorf("failed to update agent level: %w", err)
	}
	return nil
}
