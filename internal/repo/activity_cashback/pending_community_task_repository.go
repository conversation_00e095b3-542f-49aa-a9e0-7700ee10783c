package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// PendingCommunityTaskRepositoryInterface defines the interface for pending community task repository
type PendingCommunityTaskRepositoryInterface interface {
	Create(ctx context.Context, pendingTask *model.PendingCommunityTask) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.PendingCommunityTask, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error)
	GetPendingTasks(ctx context.Context) ([]*model.PendingCommunityTask, error)
	GetReadyForCompletion(ctx context.Context) ([]*model.PendingCommunityTask, error)
	Update(ctx context.Context, pendingTask *model.PendingCommunityTask) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteExpired(ctx context.Context, expiredBefore time.Time) error
	HasPendingTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error)
}

// PendingCommunityTaskRepository implements the pending community task repository
type PendingCommunityTaskRepository struct {
	db *gorm.DB
}

// NewPendingCommunityTaskRepository creates a new pending community task repository
func NewPendingCommunityTaskRepository() PendingCommunityTaskRepositoryInterface {
	return &PendingCommunityTaskRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new pending community task
func (r *PendingCommunityTaskRepository) Create(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	// Set completion time to 2 minutes from now
	completionTime := pendingTask.ClickedAt.Add(2 * time.Minute)
	pendingTask.CompletionTime = &completionTime
	
	return r.db.WithContext(ctx).Create(pendingTask).Error
}

// GetByID gets a pending community task by ID
func (r *PendingCommunityTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.PendingCommunityTask, error) {
	var pendingTask model.PendingCommunityTask
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		First(&pendingTask, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &pendingTask, nil
}

// GetByUserAndTask gets a pending community task by user ID and task ID
func (r *PendingCommunityTaskRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error) {
	var pendingTask model.PendingCommunityTask
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		First(&pendingTask, "user_id = ? AND task_id = ? AND status = ?", userID, taskID, model.PendingCommunityTaskStatusPending).Error
	if err != nil {
		return nil, err
	}
	return &pendingTask, nil
}

// GetPendingTasks gets all pending community tasks
func (r *PendingCommunityTaskRepository) GetPendingTasks(ctx context.Context) ([]*model.PendingCommunityTask, error) {
	var pendingTasks []*model.PendingCommunityTask
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Where("status = ?", model.PendingCommunityTaskStatusPending).
		Find(&pendingTasks).Error
	return pendingTasks, err
}

// GetReadyForCompletion gets all pending tasks that are ready for completion (2 minutes have passed)
func (r *PendingCommunityTaskRepository) GetReadyForCompletion(ctx context.Context) ([]*model.PendingCommunityTask, error) {
	var pendingTasks []*model.PendingCommunityTask
	now := time.Now()
	
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Where("status = ? AND completion_time <= ?", model.PendingCommunityTaskStatusPending, now).
		Find(&pendingTasks).Error
	return pendingTasks, err
}

// Update updates a pending community task
func (r *PendingCommunityTaskRepository) Update(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	return r.db.WithContext(ctx).Save(pendingTask).Error
}

// Delete deletes a pending community task by ID
func (r *PendingCommunityTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.PendingCommunityTask{}, "id = ?", id).Error
}

// DeleteExpired deletes expired pending tasks
func (r *PendingCommunityTaskRepository) DeleteExpired(ctx context.Context, expiredBefore time.Time) error {
	return r.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", model.PendingCommunityTaskStatusPending, expiredBefore).
		Delete(&model.PendingCommunityTask{}).Error
}

// HasPendingTask checks if a user has a pending task for the given task ID
func (r *PendingCommunityTaskRepository) HasPendingTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.PendingCommunityTask{}).
		Where("user_id = ? AND task_id = ? AND status = ?", userID, taskID, model.PendingCommunityTaskStatusPending).
		Count(&count).Error
	return count > 0, err
}
