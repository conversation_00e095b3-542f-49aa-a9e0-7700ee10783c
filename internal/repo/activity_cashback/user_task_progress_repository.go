package activity_cashback

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// UserTaskProgressRepository implements UserTaskProgressRepositoryInterface
type UserTaskProgressRepository struct {
	db *gorm.DB
}

// NewUserTaskProgressRepository creates a new UserTaskProgressRepository
func NewUserTaskProgressRepository() UserTaskProgressRepositoryInterface {
	return &UserTaskProgressRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new user task progress
func (r *UserTaskProgressRepository) Create(ctx context.Context, progress *model.UserTaskProgress) error {
	return r.db.WithContext(ctx).Create(progress).Error
}

// Update updates an existing user task progress
func (r *UserTaskProgressRepository) Update(ctx context.Context, progress *model.UserTaskProgress) error {
	return r.db.WithContext(ctx).Save(progress).Error
}

// Delete deletes a user task progress
func (r *UserTaskProgressRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.UserTaskProgress{}, "id = ?", id).Error
}

// GetByID retrieves user task progress by ID
func (r *UserTaskProgressRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.UserTaskProgress, error) {
	var progress model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		First(&progress, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// GetByUserAndTask retrieves user task progress by user ID and task ID
func (r *UserTaskProgressRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	var progress model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		First(&progress, "user_id = ? AND task_id = ?", userID, taskID).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// GetByUserID retrieves all task progress for a user
func (r *UserTaskProgressRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Find(&progress).Error
	return progress, err
}

// GetByTaskID retrieves all user progress for a specific task
func (r *UserTaskProgressRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("updated_at DESC").
		Find(&progress).Error
	return progress, err
}

// GetCompletedTasks retrieves completed tasks for a user
func (r *UserTaskProgressRepository) GetCompletedTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND status IN (?)", userID, []model.TaskStatus{model.TaskStatusCompleted, model.TaskStatusClaimed}).
		Order("last_completed_at DESC").
		Find(&progress).Error
	return progress, err
}

// GetClaimableTasks retrieves tasks that can be claimed by a user
func (r *UserTaskProgressRepository) GetClaimableTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND status = ?", userID, model.TaskStatusCompleted).
		Order("last_completed_at DESC").
		Find(&progress).Error
	return progress, err
}

// GetTasksNeedingReset retrieves tasks that need to be reset based on reset period
func (r *UserTaskProgressRepository) GetTasksNeedingReset(ctx context.Context, resetPeriod model.ResetPeriod) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress

	// Build query based on reset period
	query := r.db.WithContext(ctx).
		Preload("Task").
		Joins("JOIN activity_tasks ON user_task_progress.task_id = activity_tasks.id").
		Where("activity_tasks.reset_period = ?", resetPeriod)

	// Add time-based conditions based on reset period
	switch resetPeriod {
	case model.ResetDaily:
		query = query.Where("user_task_progress.last_reset_at < DATE_TRUNC('day', NOW()) OR user_task_progress.last_reset_at IS NULL")
	case model.ResetWeekly:
		query = query.Where("user_task_progress.last_reset_at < DATE_TRUNC('week', NOW()) OR user_task_progress.last_reset_at IS NULL")
	case model.ResetMonthly:
		query = query.Where("user_task_progress.last_reset_at < DATE_TRUNC('month', NOW()) OR user_task_progress.last_reset_at IS NULL")
	}

	err := query.Find(&progress).Error
	return progress, err
}

// GetUserTasksByCategory retrieves user tasks by category
func (r *UserTaskProgressRepository) GetUserTasksByCategory(ctx context.Context, userID uuid.UUID, categoryID uint) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Joins("JOIN activity_tasks ON user_task_progress.task_id = activity_tasks.id").
		Where("user_task_progress.user_id = ? AND activity_tasks.category_id = ?", userID, categoryID).
		Order("user_task_progress.updated_at DESC").
		Find(&progress).Error
	return progress, err
}

// GetUserTasksByStatus retrieves user tasks by status
func (r *UserTaskProgressRepository) GetUserTasksByStatus(ctx context.Context, userID uuid.UUID, status model.TaskStatus) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND status = ?", userID, status).
		Order("updated_at DESC").
		Find(&progress).Error
	return progress, err
}

// BulkUpdateStatus updates status for multiple task progress records
func (r *UserTaskProgressRepository) BulkUpdateStatus(ctx context.Context, progressIDs []uuid.UUID, status model.TaskStatus) error {
	return r.db.WithContext(ctx).
		Model(&model.UserTaskProgress{}).
		Where("id IN (?)", progressIDs).
		Update("status", status).Error
}

// GetStreakTasks retrieves tasks with streak counts for a user
func (r *UserTaskProgressRepository) GetStreakTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND streak_count > 0", userID).
		Order("streak_count DESC").
		Find(&progress).Error
	return progress, err
}
