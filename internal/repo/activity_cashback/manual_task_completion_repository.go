package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ManualTaskCompletionRepository implements ManualTaskCompletionRepositoryInterface
type ManualTaskCompletionRepository struct {
	db *gorm.DB
}

// NewManualTaskCompletionRepository creates a new ManualTaskCompletionRepository
func NewManualTaskCompletionRepository() ManualTaskCompletionRepositoryInterface {
	return &ManualTaskCompletionRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new manual task completion record
func (r *ManualTaskCompletionRepository) Create(ctx context.Context, completion *model.ManualTaskCompletion) error {
	return r.db.WithContext(ctx).Create(completion).Error
}

// Update updates a manual task completion record
func (r *ManualTaskCompletionRepository) Update(ctx context.Context, completion *model.ManualTaskCompletion) error {
	return r.db.WithContext(ctx).Save(completion).Error
}

// GetByID retrieves a manual task completion by ID
func (r *ManualTaskCompletionRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ManualTaskCompletion, error) {
	var completion model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		Preload("Approver").
		Where("id = ?", id).
		First(&completion).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	
	return &completion, nil
}

// GetByUserID retrieves manual task completions by user ID with pagination
func (r *ManualTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Preload("Approver").
		Where("user_id = ?", userID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByTaskID retrieves manual task completions by task ID with pagination
func (r *ManualTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Approver").
		Where("task_id = ?", taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndTask retrieves manual task completions by user and task with pagination
func (r *ManualTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Preload("Approver").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByStatus retrieves manual task completions by status with pagination
func (r *ManualTaskCompletionRepository) GetByStatus(ctx context.Context, status model.ManualTaskCompletionStatus, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		Preload("Approver").
		Where("status = ?", status).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByApprover retrieves manual task completions by approver with pagination
func (r *ManualTaskCompletionRepository) GetByApprover(ctx context.Context, approverID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		Where("approved_by = ?", approverID).
		Order("approval_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *ManualTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.ManualTaskCompletion{}).
		Select("activity_tasks.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks ON manual_task_completions.task_id = activity_tasks.id").
		Where("manual_task_completions.user_id = ? AND manual_task_completions.completion_date BETWEEN ? AND ? AND manual_task_completions.status = ?", userID, startDate, endDate, model.ManualTaskStatusApproved).
		Group("activity_tasks.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *ManualTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ManualTaskCompletion{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ? AND status = ?", taskID, startDate, endDate, model.ManualTaskStatusApproved).
		Count(&count).Error
	return int(count), err
}

// GetPendingApprovals retrieves manual task completions pending approval with pagination
func (r *ManualTaskCompletionRepository) GetPendingApprovals(ctx context.Context, limit, offset int) ([]model.ManualTaskCompletion, error) {
	var completions []model.ManualTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Task").
		Preload("Task.Category").
		Where("status = ?", model.ManualTaskStatusPending).
		Order("completion_date ASC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// Approve approves a manual task completion
func (r *ManualTaskCompletionRepository) Approve(ctx context.Context, id, approverID uuid.UUID, notes string) error {
	completion, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if completion == nil {
		return gorm.ErrRecordNotFound
	}

	completion.Approve(approverID, notes)
	return r.Update(ctx, completion)
}

// Reject rejects a manual task completion
func (r *ManualTaskCompletionRepository) Reject(ctx context.Context, id, approverID uuid.UUID, notes string) error {
	completion, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if completion == nil {
		return gorm.ErrRecordNotFound
	}

	completion.Reject(approverID, notes)
	return r.Update(ctx, completion)
}
