package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// OneTimeTaskCompletionRepository implements OneTimeTaskCompletionRepositoryInterface
type OneTimeTaskCompletionRepository struct {
	db *gorm.DB
}

// NewOneTimeTaskCompletionRepository creates a new OneTimeTaskCompletionRepository
func NewOneTimeTaskCompletionRepository() OneTimeTaskCompletionRepositoryInterface {
	return &OneTimeTaskCompletionRepository{
		db: global.GVA_DB,
	}
}

// <PERSON>reate creates a new one-time task completion record
func (r *OneTimeTaskCompletionRepository) Create(ctx context.Context, completion *model.OneTimeTaskCompletion) error {
	return r.db.WithContext(ctx).Create(completion).Error
}

// GetByUserID retrieves one-time task completions by user ID with pagination
func (r *OneTimeTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.OneTimeTaskCompletion, error) {
	var completions []model.OneTimeTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByTaskID retrieves one-time task completions by task ID with pagination
func (r *OneTimeTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.OneTimeTaskCompletion, error) {
	var completions []model.OneTimeTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndTask retrieves one-time task completion by user and task (should be unique)
func (r *OneTimeTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.OneTimeTaskCompletion, error) {
	var completion model.OneTimeTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		First(&completion).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	
	return &completion, nil
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *OneTimeTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.OneTimeTaskCompletion{}).
		Select("activity_tasks.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks ON one_time_task_completions.task_id = activity_tasks.id").
		Where("one_time_task_completions.user_id = ? AND one_time_task_completions.completion_date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("activity_tasks.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *OneTimeTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.OneTimeTaskCompletion{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ?", taskID, startDate, endDate).
		Count(&count).Error
	return int(count), err
}

// HasUserCompletedTask checks if user has completed a specific one-time task
func (r *OneTimeTaskCompletionRepository) HasUserCompletedTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.OneTimeTaskCompletion{}).
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Count(&count).Error
	
	return count > 0, err
}
