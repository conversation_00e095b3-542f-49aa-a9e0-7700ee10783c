package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// UnlimitedTaskCompletionRepository implements UnlimitedTaskCompletionRepositoryInterface
type UnlimitedTaskCompletionRepository struct {
	db *gorm.DB
}

// NewUnlimitedTaskCompletionRepository creates a new UnlimitedTaskCompletionRepository
func NewUnlimitedTaskCompletionRepository() UnlimitedTaskCompletionRepositoryInterface {
	return &UnlimitedTaskCompletionRepository{
		db: global.GVA_DB,
	}
}

// <PERSON><PERSON> creates a new unlimited task completion record
func (r *UnlimitedTaskCompletionRepository) Create(ctx context.Context, completion *model.UnlimitedTaskCompletion) error {
	// If sequence number is not set, it will be auto-generated by the database
	return r.db.WithContext(ctx).Create(completion).Error
}

// GetByUserID retrieves unlimited task completions by user ID with pagination
func (r *UnlimitedTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error) {
	var completions []model.UnlimitedTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByTaskID retrieves unlimited task completions by task ID with pagination
func (r *UnlimitedTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error) {
	var completions []model.UnlimitedTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndTask retrieves unlimited task completions by user and task with pagination
func (r *UnlimitedTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error) {
	var completions []model.UnlimitedTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("sequence_number DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *UnlimitedTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.UnlimitedTaskCompletion{}).
		Select("activity_tasks.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks ON unlimited_task_completions.task_id = activity_tasks.id").
		Where("unlimited_task_completions.user_id = ? AND unlimited_task_completions.completion_date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("activity_tasks.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *UnlimitedTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.UnlimitedTaskCompletion{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ?", taskID, startDate, endDate).
		Count(&count).Error
	return int(count), err
}

// GetUserTaskCompletionCount retrieves the total number of completions for a user and task
func (r *UnlimitedTaskCompletionRepository) GetUserTaskCompletionCount(ctx context.Context, userID, taskID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.UnlimitedTaskCompletion{}).
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Count(&count).Error
	return count, err
}

// GetLastCompletionSequence retrieves the last sequence number for a user and task
func (r *UnlimitedTaskCompletionRepository) GetLastCompletionSequence(ctx context.Context, userID, taskID uuid.UUID) (int64, error) {
	var completion model.UnlimitedTaskCompletion
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("sequence_number DESC").
		First(&completion).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, err
	}
	
	return completion.SequenceNumber, nil
}
