package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TaskCategoryRepositoryInterface defines the interface for task category operations
type TaskCategoryRepositoryInterface interface {
	Create(ctx context.Context, category *model.TaskCategory) error
	Update(ctx context.Context, category *model.TaskCategory) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.TaskCategory, error)
	GetByName(ctx context.Context, name model.TaskCategoryName) (*model.TaskCategory, error)
	GetAll(ctx context.Context) ([]model.TaskCategory, error)
	GetActive(ctx context.Context) ([]model.TaskCategory, error)
}

// ActivityTaskRepositoryInterface defines the interface for activity task operations
type ActivityTaskRepositoryInterface interface {
	Create(ctx context.Context, task *model.ActivityTask) error
	Update(ctx context.Context, task *model.ActivityTask) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error)
	GetByCategoryID(ctx context.Context, categoryID uint) ([]model.ActivityTask, error)
	GetByTaskType(ctx context.Context, taskType model.TaskType) ([]model.ActivityTask, error)
	GetActive(ctx context.Context) ([]model.ActivityTask, error)
	GetAvailable(ctx context.Context, now time.Time) ([]model.ActivityTask, error)
	GetAll(ctx context.Context) ([]model.ActivityTask, error)
	GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error)
	GetDailyTasks(ctx context.Context) ([]model.ActivityTask, error)
	GetCommunityTasks(ctx context.Context) ([]model.ActivityTask, error)
	GetTradingTasks(ctx context.Context) ([]model.ActivityTask, error)
}

// UserTaskProgressRepositoryInterface defines the interface for user task progress operations
type UserTaskProgressRepositoryInterface interface {
	Create(ctx context.Context, progress *model.UserTaskProgress) error
	Update(ctx context.Context, progress *model.UserTaskProgress) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.UserTaskProgress, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID) ([]model.UserTaskProgress, error)
	GetCompletedTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
	GetClaimableTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
	GetTasksNeedingReset(ctx context.Context, resetPeriod model.ResetPeriod) ([]model.UserTaskProgress, error)
	GetUserTasksByCategory(ctx context.Context, userID uuid.UUID, categoryID uint) ([]model.UserTaskProgress, error)
	GetUserTasksByStatus(ctx context.Context, userID uuid.UUID, status model.TaskStatus) ([]model.UserTaskProgress, error)
	BulkUpdateStatus(ctx context.Context, progressIDs []uuid.UUID, status model.TaskStatus) error
	GetStreakTasks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
}

// UserTierInfoRepositoryInterface defines the interface for user tier info operations
type UserTierInfoRepositoryInterface interface {
	Create(ctx context.Context, tierInfo *model.UserTierInfo) error
	Update(ctx context.Context, tierInfo *model.UserTierInfo) error
	Delete(ctx context.Context, userID uuid.UUID) error
	GetByUserID(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error)
	GetByTier(ctx context.Context, tier int) ([]model.UserTierInfo, error)
	GetUsersNeedingMonthlyReset(ctx context.Context) ([]model.UserTierInfo, error)
	GetUsersEligibleForUpgrade(ctx context.Context, minPoints int) ([]model.UserTierInfo, error)
	GetTopUsersByPoints(ctx context.Context, limit int) ([]model.UserTierInfo, error)
	GetUserRankByPoints(ctx context.Context, userID uuid.UUID) (int, error)
	BulkUpdateMonthlyStats(ctx context.Context, userIDs []uuid.UUID) error
}

// TierBenefitRepositoryInterface defines the interface for tier benefit operations
type TierBenefitRepositoryInterface interface {
	Create(ctx context.Context, benefit *model.TierBenefit) error
	Update(ctx context.Context, benefit *model.TierBenefit) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.TierBenefit, error)
	GetByTierLevel(ctx context.Context, tierLevel int) (*model.TierBenefit, error)
	GetAll(ctx context.Context) ([]model.TierBenefit, error)
	GetActive(ctx context.Context) ([]model.TierBenefit, error)
	GetNextTier(ctx context.Context, currentTier int) (*model.TierBenefit, error)
	GetTierByPoints(ctx context.Context, points int) (*model.TierBenefit, error)
}

// TaskCompletionRepositoryFactoryInterface defines the interface for the task completion repository factory
type TaskCompletionRepositoryFactoryInterface interface {
	CreateTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error
	GetRepositoryByFrequency(frequency model.TaskFrequency) interface{}
	GetUnifiedRepository() TaskCompletionRepositoryInterface
}

// DailyTaskCompletionRepositoryInterface defines the interface for daily task completion operations
type DailyTaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion *model.DailyTaskCompletion) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error)
	GetByUserAndDate(ctx context.Context, userID uuid.UUID, date time.Time) ([]model.DailyTaskCompletion, error)
	GetByTaskAndDate(ctx context.Context, taskID uuid.UUID, date time.Time) ([]model.DailyTaskCompletion, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	GetDailyCompletionStats(ctx context.Context, date time.Time) (map[uuid.UUID]int, error)
	HasUserCompletedTaskToday(ctx context.Context, userID, taskID uuid.UUID) (bool, error)
	CreatePartitionIfNotExists(ctx context.Context, date time.Time) error
}

// OneTimeTaskCompletionRepositoryInterface defines the interface for one-time task completion operations
type OneTimeTaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion *model.OneTimeTaskCompletion) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.OneTimeTaskCompletion, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.OneTimeTaskCompletion, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID) (*model.OneTimeTaskCompletion, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	HasUserCompletedTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error)
}

// UnlimitedTaskCompletionRepositoryInterface defines the interface for unlimited task completion operations
type UnlimitedTaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion *model.UnlimitedTaskCompletion) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.UnlimitedTaskCompletion, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	GetUserTaskCompletionCount(ctx context.Context, userID, taskID uuid.UUID) (int64, error)
	GetLastCompletionSequence(ctx context.Context, userID, taskID uuid.UUID) (int64, error)
}

// ProgressiveTaskCompletionRepositoryInterface defines the interface for progressive task completion operations
type ProgressiveTaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion *model.ProgressiveTaskCompletion) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error)
	GetByUserTaskAndLevel(ctx context.Context, userID, taskID uuid.UUID, level int) (*model.ProgressiveTaskCompletion, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	GetUserTaskMaxLevel(ctx context.Context, userID, taskID uuid.UUID) (int, error)
	GetUserTaskTotalProgress(ctx context.Context, userID, taskID uuid.UUID) (int, error)
}

// ManualTaskCompletionRepositoryInterface defines the interface for manual task completion operations
type ManualTaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion *model.ManualTaskCompletion) error
	Update(ctx context.Context, completion *model.ManualTaskCompletion) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.ManualTaskCompletion, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error)
	GetByStatus(ctx context.Context, status model.ManualTaskCompletionStatus, limit, offset int) ([]model.ManualTaskCompletion, error)
	GetByApprover(ctx context.Context, approverID uuid.UUID, limit, offset int) ([]model.ManualTaskCompletion, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	GetPendingApprovals(ctx context.Context, limit, offset int) ([]model.ManualTaskCompletion, error)
	Approve(ctx context.Context, id, approverID uuid.UUID, notes string) error
	Reject(ctx context.Context, id, approverID uuid.UUID, notes string) error
}

// ActivityCashbackClaimRepositoryInterface defines the interface for activity cashback claim operations
type ActivityCashbackClaimRepositoryInterface interface {
	Create(ctx context.Context, claim *model.ActivityCashbackClaim) error
	Update(ctx context.Context, claim *model.ActivityCashbackClaim) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashbackClaim, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error)
	GetPendingClaims(ctx context.Context) ([]model.ActivityCashbackClaim, error)
	GetClaimsByStatus(ctx context.Context, status model.ClaimStatus) ([]model.ActivityCashbackClaim, error)
	GetClaimsByType(ctx context.Context, claimType model.ClaimType) ([]model.ActivityCashbackClaim, error)
	GetUserClaimHistory(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]model.ActivityCashbackClaim, error)
	GetTotalClaimedAmount(ctx context.Context, userID uuid.UUID) (float64, error)
	BulkUpdateStatus(ctx context.Context, claimIDs []uuid.UUID, status model.ClaimStatus) error
}
