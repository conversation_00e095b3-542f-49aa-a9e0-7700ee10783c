package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ProgressiveTaskCompletionRepository implements ProgressiveTaskCompletionRepositoryInterface
type ProgressiveTaskCompletionRepository struct {
	db *gorm.DB
}

// NewProgressiveTaskCompletionRepository creates a new ProgressiveTaskCompletionRepository
func NewProgressiveTaskCompletionRepository() ProgressiveTaskCompletionRepositoryInterface {
	return &ProgressiveTaskCompletionRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new progressive task completion record
func (r *ProgressiveTaskCompletionRepository) Create(ctx context.Context, completion *model.ProgressiveTaskCompletion) error {
	return r.db.WithContext(ctx).Create(completion).Error
}

// GetByUserID retrieves progressive task completions by user ID with pagination
func (r *ProgressiveTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error) {
	var completions []model.ProgressiveTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByTaskID retrieves progressive task completions by task ID with pagination
func (r *ProgressiveTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error) {
	var completions []model.ProgressiveTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndTask retrieves progressive task completions by user and task with pagination
func (r *ProgressiveTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.ProgressiveTaskCompletion, error) {
	var completions []model.ProgressiveTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("level_completed DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserTaskAndLevel retrieves progressive task completion by user, task and level (should be unique)
func (r *ProgressiveTaskCompletionRepository) GetByUserTaskAndLevel(ctx context.Context, userID, taskID uuid.UUID, level int) (*model.ProgressiveTaskCompletion, error) {
	var completion model.ProgressiveTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ? AND level_completed = ?", userID, taskID, level).
		First(&completion).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	
	return &completion, nil
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *ProgressiveTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.ProgressiveTaskCompletion{}).
		Select("activity_tasks.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks ON progressive_task_completions.task_id = activity_tasks.id").
		Where("progressive_task_completions.user_id = ? AND progressive_task_completions.completion_date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("activity_tasks.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *ProgressiveTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ProgressiveTaskCompletion{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ?", taskID, startDate, endDate).
		Count(&count).Error
	return int(count), err
}

// GetUserTaskMaxLevel retrieves the maximum level completed by a user for a specific task
func (r *ProgressiveTaskCompletionRepository) GetUserTaskMaxLevel(ctx context.Context, userID, taskID uuid.UUID) (int, error) {
	var maxLevel int
	err := r.db.WithContext(ctx).
		Model(&model.ProgressiveTaskCompletion{}).
		Select("COALESCE(MAX(level_completed), 0)").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Scan(&maxLevel).Error
	
	return maxLevel, err
}

// GetUserTaskTotalProgress retrieves the total progress for a user and task
func (r *ProgressiveTaskCompletionRepository) GetUserTaskTotalProgress(ctx context.Context, userID, taskID uuid.UUID) (int, error) {
	var completion model.ProgressiveTaskCompletion
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("level_completed DESC").
		First(&completion).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, err
	}
	
	return completion.TotalProgress, nil
}
