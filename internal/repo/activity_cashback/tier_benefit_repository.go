package activity_cashback

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// TierBenefitRepository implements TierBenefitRepositoryInterface
type TierBenefitRepository struct {
	db *gorm.DB
}

// NewTierBenefitRepository creates a new TierBenefitRepository
func NewTierBenefitRepository() TierBenefitRepositoryInterface {
	return &TierBenefitRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new tier benefit
func (r *TierBenefitRepository) Create(ctx context.Context, benefit *model.TierBenefit) error {
	return r.db.WithContext(ctx).Create(benefit).Error
}

// Update updates an existing tier benefit
func (r *TierBenefitRepository) Update(ctx context.Context, benefit *model.TierBenefit) error {
	return r.db.WithContext(ctx).Save(benefit).Error
}

// Delete soft deletes a tier benefit
func (r *TierBenefitRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&model.TierBenefit{}).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// GetByID retrieves a tier benefit by ID
func (r *TierBenefitRepository) GetByID(ctx context.Context, id uint) (*model.TierBenefit, error) {
	var benefit model.TierBenefit
	err := r.db.WithContext(ctx).First(&benefit, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &benefit, nil
}

// GetByTierLevel retrieves a tier benefit by tier level
func (r *TierBenefitRepository) GetByTierLevel(ctx context.Context, tierLevel int) (*model.TierBenefit, error) {
	var benefit model.TierBenefit
	err := r.db.WithContext(ctx).
		Where("tier_level = ? AND is_active = ?", tierLevel, true).
		First(&benefit).Error
	if err != nil {
		return nil, err
	}
	return &benefit, nil
}

// GetAll retrieves all tier benefits
func (r *TierBenefitRepository) GetAll(ctx context.Context) ([]model.TierBenefit, error) {
	var benefits []model.TierBenefit
	err := r.db.WithContext(ctx).
		Order("tier_level ASC").
		Find(&benefits).Error
	return benefits, err
}

// GetActive retrieves all active tier benefits
func (r *TierBenefitRepository) GetActive(ctx context.Context) ([]model.TierBenefit, error) {
	var benefits []model.TierBenefit
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("tier_level ASC").
		Find(&benefits).Error
	return benefits, err
}

// GetNextTier retrieves the next tier benefit for a given current tier
func (r *TierBenefitRepository) GetNextTier(ctx context.Context, currentTier int) (*model.TierBenefit, error) {
	var benefit model.TierBenefit
	err := r.db.WithContext(ctx).
		Where("tier_level > ? AND is_active = ?", currentTier, true).
		Order("tier_level ASC").
		First(&benefit).Error
	if err != nil {
		return nil, err
	}
	return &benefit, nil
}

// GetTierByPoints retrieves the appropriate tier for a given points amount
func (r *TierBenefitRepository) GetTierByPoints(ctx context.Context, points int) (*model.TierBenefit, error) {
	var benefit model.TierBenefit
	err := r.db.WithContext(ctx).
		Where("min_points <= ? AND is_active = ?", points, true).
		Order("tier_level DESC").
		First(&benefit).Error
	if err != nil {
		return nil, err
	}
	return &benefit, nil
}
