package activity_cashback

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// TaskCategoryRepository implements TaskCategoryRepositoryInterface
type TaskCategoryRepository struct {
	db *gorm.DB
}

// NewTaskCategoryRepository creates a new TaskCategoryRepository
func NewTaskCategoryRepository() TaskCategoryRepositoryInterface {
	return &TaskCategoryRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new task category
func (r *TaskCategoryRepository) Create(ctx context.Context, category *model.TaskCategory) error {
	return r.db.WithContext(ctx).Create(category).Error
}

// Update updates an existing task category
func (r *TaskCategoryRepository) Update(ctx context.Context, category *model.TaskCategory) error {
	return r.db.WithContext(ctx).Save(category).Error
}

// Delete soft deletes a task category
func (r *TaskCategoryRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&model.TaskCategory{}).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// GetByID retrieves a task category by ID
func (r *TaskCategoryRepository) GetByID(ctx context.Context, id uint) (*model.TaskCategory, error) {
	var category model.TaskCategory
	err := r.db.WithContext(ctx).
		Preload("Tasks").
		First(&category, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetByName retrieves a task category by name
func (r *TaskCategoryRepository) GetByName(ctx context.Context, name model.TaskCategoryName) (*model.TaskCategory, error) {
	var category model.TaskCategory
	err := r.db.WithContext(ctx).
		Preload("Tasks").
		First(&category, "name = ?", string(name)).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetAll retrieves all task categories
func (r *TaskCategoryRepository) GetAll(ctx context.Context) ([]model.TaskCategory, error) {
	var categories []model.TaskCategory
	err := r.db.WithContext(ctx).
		Preload("Tasks").
		Order("sort_order ASC, created_at ASC").
		Find(&categories).Error
	return categories, err
}

// GetActive retrieves all active task categories
func (r *TaskCategoryRepository) GetActive(ctx context.Context) ([]model.TaskCategory, error) {
	var categories []model.TaskCategory
	err := r.db.WithContext(ctx).
		Preload("Tasks", "is_active = ?", true).
		Where("is_active = ?", true).
		Order("sort_order ASC, created_at ASC").
		Find(&categories).Error
	return categories, err
}
