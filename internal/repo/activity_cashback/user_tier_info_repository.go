package activity_cashback

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// UserTierInfoRepository implements UserTierInfoRepositoryInterface
type UserTierInfoRepository struct {
	db *gorm.DB
}

// NewUserTierInfoRepository creates a new UserTierInfoRepository
func NewUserTierInfoRepository() UserTierInfoRepositoryInterface {
	return &UserTierInfoRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new user tier info
func (r *UserTierInfoRepository) Create(ctx context.Context, tierInfo *model.UserTierInfo) error {
	return r.db.WithContext(ctx).Create(tierInfo).Error
}

// Update updates an existing user tier info
func (r *UserTierInfoRepository) Update(ctx context.Context, tierInfo *model.UserTierInfo) error {
	return r.db.WithContext(ctx).Save(tierInfo).Error
}

// Delete deletes a user tier info
func (r *UserTierInfoRepository) Delete(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.UserTierInfo{}, "user_id = ?", userID).Error
}

// GetByUserID retrieves user tier info by user ID
func (r *UserTierInfoRepository) GetByUserID(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	var tierInfo model.UserTierInfo
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("TierBenefit").
		First(&tierInfo, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &tierInfo, nil
}

// GetByTier retrieves all users in a specific tier
func (r *UserTierInfoRepository) GetByTier(ctx context.Context, tier int) ([]model.UserTierInfo, error) {
	var tierInfos []model.UserTierInfo
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("TierBenefit").
		Where("current_tier = ?", tier).
		Order("total_points DESC").
		Find(&tierInfos).Error
	return tierInfos, err
}

// GetUsersNeedingMonthlyReset retrieves users whose monthly stats need to be reset
func (r *UserTierInfoRepository) GetUsersNeedingMonthlyReset(ctx context.Context) ([]model.UserTierInfo, error) {
	var tierInfos []model.UserTierInfo
	err := r.db.WithContext(ctx).
		Where("monthly_reset_at < DATE_TRUNC('month', NOW()) OR monthly_reset_at IS NULL").
		Find(&tierInfos).Error
	return tierInfos, err
}

// GetUsersEligibleForUpgrade retrieves users eligible for tier upgrade
func (r *UserTierInfoRepository) GetUsersEligibleForUpgrade(ctx context.Context, minPoints int) ([]model.UserTierInfo, error) {
	var tierInfos []model.UserTierInfo
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("TierBenefit").
		Where("total_points >= ?", minPoints).
		Order("total_points DESC").
		Find(&tierInfos).Error
	return tierInfos, err
}

// GetTopUsersByPoints retrieves top users by total points
func (r *UserTierInfoRepository) GetTopUsersByPoints(ctx context.Context, limit int) ([]model.UserTierInfo, error) {
	var tierInfos []model.UserTierInfo
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("TierBenefit").
		Order("total_points DESC").
		Limit(limit).
		Find(&tierInfos).Error
	return tierInfos, err
}

// GetUserRankByPoints retrieves a user's rank based on total points
func (r *UserTierInfoRepository) GetUserRankByPoints(ctx context.Context, userID uuid.UUID) (int, error) {
	var userTierInfo model.UserTierInfo
	err := r.db.WithContext(ctx).First(&userTierInfo, "user_id = ?", userID).Error
	if err != nil {
		return 0, err
	}

	var rank int64
	err = r.db.WithContext(ctx).
		Model(&model.UserTierInfo{}).
		Where("total_points > ?", userTierInfo.TotalPoints).
		Count(&rank).Error
	if err != nil {
		return 0, err
	}

	return int(rank) + 1, nil // +1 because rank starts from 1
}

// BulkUpdateMonthlyStats resets monthly stats for multiple users
func (r *UserTierInfoRepository) BulkUpdateMonthlyStats(ctx context.Context, userIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&model.UserTierInfo{}).
		Where("user_id IN (?)", userIDs).
		Updates(map[string]interface{}{
			"points_this_month":      0,
			"active_days_this_month": 0,
			"monthly_reset_at":       gorm.Expr("NOW()"),
			"updated_at":             gorm.Expr("NOW()"),
		}).Error
}
