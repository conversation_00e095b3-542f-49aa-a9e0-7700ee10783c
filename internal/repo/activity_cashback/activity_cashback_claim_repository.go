package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityCashbackClaimRepository implements ActivityCashbackClaimRepositoryInterface
type ActivityCashbackClaimRepository struct {
	db *gorm.DB
}

// NewActivityCashbackClaimRepository creates a new ActivityCashbackClaimRepository
func NewActivityCashbackClaimRepository() ActivityCashbackClaimRepositoryInterface {
	return &ActivityCashbackClaimRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new activity cashback claim
func (r *ActivityCashbackClaimRepository) Create(ctx context.Context, claim *model.ActivityCashbackClaim) error {
	return r.db.WithContext(ctx).Create(claim).Error
}

// Update updates an existing activity cashback claim
func (r *ActivityCashbackClaimRepository) Update(ctx context.Context, claim *model.ActivityCashbackClaim) error {
	return r.db.WithContext(ctx).Save(claim).Error
}

// GetByID retrieves an activity cashback claim by ID
func (r *ActivityCashbackClaimRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashbackClaim, error) {
	var claim model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&claim, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &claim, nil
}

// GetByUserID retrieves activity cashback claims by user ID with pagination
func (r *ActivityCashbackClaimRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("claimed_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&claims).Error
	return claims, err
}

// GetPendingClaims retrieves all pending claims
func (r *ActivityCashbackClaimRepository) GetPendingClaims(ctx context.Context) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("status = ?", model.ClaimStatusPending).
		Order("claimed_at ASC").
		Find(&claims).Error
	return claims, err
}

// GetClaimsByStatus retrieves claims by status
func (r *ActivityCashbackClaimRepository) GetClaimsByStatus(ctx context.Context, status model.ClaimStatus) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("status = ?", status).
		Order("claimed_at DESC").
		Find(&claims).Error
	return claims, err
}

// GetClaimsByType retrieves claims by type
func (r *ActivityCashbackClaimRepository) GetClaimsByType(ctx context.Context, claimType model.ClaimType) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("claim_type = ?", claimType).
		Order("claimed_at DESC").
		Find(&claims).Error
	return claims, err
}

// GetUserClaimHistory retrieves user claim history within a date range
func (r *ActivityCashbackClaimRepository) GetUserClaimHistory(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND claimed_at BETWEEN ? AND ?", userID, startDate, endDate).
		Order("claimed_at DESC").
		Find(&claims).Error
	return claims, err
}

// GetTotalClaimedAmount retrieves the total claimed amount for a user
func (r *ActivityCashbackClaimRepository) GetTotalClaimedAmount(ctx context.Context, userID uuid.UUID) (float64, error) {
	var result struct {
		TotalAmount float64 `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.ActivityCashbackClaim{}).
		Select("COALESCE(SUM(total_amount_usd), 0) as total_amount").
		Where("user_id = ? AND status = ?", userID, model.ClaimStatusCompleted).
		Scan(&result).Error

	return result.TotalAmount, err
}

// BulkUpdateStatus updates status for multiple claims
func (r *ActivityCashbackClaimRepository) BulkUpdateStatus(ctx context.Context, claimIDs []uuid.UUID, status model.ClaimStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == model.ClaimStatusProcessing {
		// No additional fields needed for processing
	} else if status == model.ClaimStatusCompleted {
		updates["processed_at"] = time.Now()
	}

	return r.db.WithContext(ctx).
		Model(&model.ActivityCashbackClaim{}).
		Where("id IN (?)", claimIDs).
		Updates(updates).Error
}
