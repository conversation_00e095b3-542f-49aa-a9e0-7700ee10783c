package activity_cashback

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestTaskCompletionRepositoryFactory(t *testing.T) {
	factory := NewTaskCompletionRepositoryFactory()

	// Test factory creation
	assert.NotNil(t, factory)

	// Test GetRepositoryByFrequency
	t.Run("GetRepositoryByFrequency", func(t *testing.T) {
		dailyRepo := factory.GetRepositoryByFrequency(model.FrequencyDaily)
		assert.NotNil(t, dailyRepo)

		oneTimeRepo := factory.GetRepositoryByFrequency(model.FrequencyOneTime)
		assert.NotNil(t, oneTimeRepo)

		unlimitedRepo := factory.GetRepositoryByFrequency(model.FrequencyUnlimited)
		assert.NotNil(t, unlimitedRepo)

		progressiveRepo := factory.GetRepositoryByFrequency(model.FrequencyProgressive)
		assert.NotNil(t, progressiveRepo)

		manualRepo := factory.GetRepositoryByFrequency(model.FrequencyManual)
		assert.NotNil(t, manualRepo)

		unknownRepo := factory.GetRepositoryByFrequency("UNKNOWN")
		assert.Nil(t, unknownRepo)
	})

	// Test GetUnifiedRepository
	t.Run("GetUnifiedRepository", func(t *testing.T) {
		unifiedRepo := factory.GetUnifiedRepository()
		assert.NotNil(t, unifiedRepo)
	})
}

func TestUnifiedTaskCompletionRepository(t *testing.T) {
	unifiedRepo := NewUnifiedTaskCompletionRepository()

	// Test repository creation
	assert.NotNil(t, unifiedRepo)

	// Note: These tests would require database setup and mock data
	// For now, we'll test the interface compliance
	t.Run("Interface Compliance", func(t *testing.T) {
		// Test that unifiedRepo implements TaskCompletionRepositoryInterface
		var _ TaskCompletionRepositoryInterface = unifiedRepo
	})
}

func TestTaskCompletionInterface(t *testing.T) {
	userID := uuid.New()
	taskID := uuid.New()
	pointsAwarded := 100
	now := time.Now()

	t.Run("DailyTaskCompletion Interface", func(t *testing.T) {
		completion := &model.DailyTaskCompletion{
			ID:             uuid.New(),
			UserID:         userID,
			TaskID:         taskID,
			PointsAwarded:  pointsAwarded,
			CompletionTime: now,
			CreatedAt:      now,
		}

		// Test interface methods
		assert.Equal(t, completion.ID, completion.GetID())
		assert.Equal(t, userID, completion.GetUserID())
		assert.Equal(t, taskID, completion.GetTaskID())
		assert.Equal(t, pointsAwarded, completion.GetPointsAwarded())
		assert.Equal(t, now, completion.GetCompletionDate())
		assert.Equal(t, now, completion.GetCreatedAt())

		// Test verification methods
		assert.False(t, completion.IsVerified())
		completion.SetVerificationData("auto", "system", map[string]interface{}{"test": "data"})
		assert.True(t, completion.IsVerified())
		assert.Equal(t, "auto", completion.GetVerificationMethod())

		// Test conversion
		// history := completion.ToTaskCompletionHistory()
		// assert.NotNil(t, history)
		// assert.Equal(t, completion.ID, history.ID)
		// assert.Equal(t, completion.UserID, history.UserID)
		// assert.Equal(t, completion.TaskID, history.TaskID)
	})

	t.Run("OneTimeTaskCompletion Interface", func(t *testing.T) {
		completion := &model.OneTimeTaskCompletion{
			ID:             uuid.New(),
			UserID:         userID,
			TaskID:         taskID,
			PointsAwarded:  pointsAwarded,
			CompletionDate: now,
			CreatedAt:      now,
		}

		// Test interface compliance
		var _ model.TaskCompletionInterface = completion

		// Test interface methods
		assert.Equal(t, completion.ID, completion.GetID())
		assert.Equal(t, userID, completion.GetUserID())
		assert.Equal(t, taskID, completion.GetTaskID())
		assert.Equal(t, pointsAwarded, completion.GetPointsAwarded())
		assert.Equal(t, now, completion.GetCompletionDate())
		assert.Equal(t, now, completion.GetCreatedAt())
	})

	t.Run("UnlimitedTaskCompletion Interface", func(t *testing.T) {
		completion := &model.UnlimitedTaskCompletion{
			ID:             uuid.New(),
			UserID:         userID,
			TaskID:         taskID,
			SequenceNumber: 1,
			PointsAwarded:  pointsAwarded,
			CompletionDate: now,
			CreatedAt:      now,
		}

		// Test interface compliance
		var _ model.TaskCompletionInterface = completion

		// Test interface methods
		assert.Equal(t, completion.ID, completion.GetID())
		assert.Equal(t, userID, completion.GetUserID())
		assert.Equal(t, taskID, completion.GetTaskID())
		assert.Equal(t, pointsAwarded, completion.GetPointsAwarded())
		assert.Equal(t, now, completion.GetCompletionDate())
		assert.Equal(t, now, completion.GetCreatedAt())
	})

	t.Run("ProgressiveTaskCompletion Interface", func(t *testing.T) {
		completion := &model.ProgressiveTaskCompletion{
			ID:             uuid.New(),
			UserID:         userID,
			TaskID:         taskID,
			LevelCompleted: 1,
			TotalProgress:  10,
			PointsAwarded:  pointsAwarded,
			CompletionDate: now,
			CreatedAt:      now,
		}

		// Test interface compliance
		var _ model.TaskCompletionInterface = completion

		// Test interface methods
		assert.Equal(t, completion.ID, completion.GetID())
		assert.Equal(t, userID, completion.GetUserID())
		assert.Equal(t, taskID, completion.GetTaskID())
		assert.Equal(t, pointsAwarded, completion.GetPointsAwarded())
		assert.Equal(t, now, completion.GetCompletionDate())
		assert.Equal(t, now, completion.GetCreatedAt())

		// Test milestone data
		completion.SetMilestoneData(1, "Level 1", 10, 10, nil, map[string]interface{}{"bonus": true})
		assert.NotNil(t, completion.MilestoneData)
		assert.Equal(t, 1, completion.MilestoneData.Level)
		assert.Equal(t, "Level 1", completion.MilestoneData.LevelName)
	})

	t.Run("ManualTaskCompletion Interface", func(t *testing.T) {
		completion := &model.ManualTaskCompletion{
			ID:             uuid.New(),
			UserID:         userID,
			TaskID:         taskID,
			PointsAwarded:  pointsAwarded,
			CompletionDate: now,
			Status:         model.ManualTaskStatusPending,
			CreatedAt:      now,
			UpdatedAt:      now,
		}

		// Test interface compliance
		var _ model.TaskCompletionInterface = completion

		// Test interface methods
		assert.Equal(t, completion.ID, completion.GetID())
		assert.Equal(t, userID, completion.GetUserID())
		assert.Equal(t, taskID, completion.GetTaskID())
		assert.Equal(t, pointsAwarded, completion.GetPointsAwarded())
		assert.Equal(t, now, completion.GetCompletionDate())
		assert.Equal(t, now, completion.GetCreatedAt())

		// Test status methods
		assert.True(t, completion.IsPending())
		assert.False(t, completion.IsApproved())
		assert.False(t, completion.IsRejected())

		// Test approval
		approverID := uuid.New()
		completion.Approve(approverID, "Approved for testing")
		assert.True(t, completion.IsApproved())
		assert.False(t, completion.IsPending())
		assert.Equal(t, &approverID, completion.ApprovedBy)
		assert.NotNil(t, completion.ApprovalDate)
		assert.Equal(t, "Approved for testing", *completion.ApprovalNotes)

		// Test rejection
		completion.Status = model.ManualTaskStatusPending // Reset
		completion.Reject(approverID, "Rejected for testing")
		assert.True(t, completion.IsRejected())
		assert.False(t, completion.IsPending())
		assert.False(t, completion.IsApproved())
	})
}

func TestTaskCompletionConversions(t *testing.T) {
	// userID := uuid.New()
	// taskID := uuid.New()
	// pointsAwarded := 100
	// now := time.Now()

	// Create a TaskCompletionHistory
	// history := &model.TaskCompletionHistory{
	// 	ID:             uuid.New(),
	// 	UserID:         userID,
	// 	TaskID:         taskID,
	// 	PointsAwarded:  pointsAwarded,
	// 	CompletionDate: now,
	// 	CreatedAt:      now,
	// }

	// t.Run("DailyTaskCompletion Conversion", func(t *testing.T) {
	// 	daily := &model.DailyTaskCompletion{}
	// 	daily.FromTaskCompletionHistory(history)

	// 	assert.Equal(t, history.ID, daily.ID)
	// 	assert.Equal(t, history.UserID, daily.UserID)
	// 	assert.Equal(t, history.TaskID, daily.TaskID)
	// 	assert.Equal(t, history.PointsAwarded, daily.PointsAwarded)
	// 	assert.Equal(t, history.CompletionDate, daily.CompletionTime)

	// 	// Test back conversion
	// 	convertedHistory := daily.ToTaskCompletionHistory()
	// 	assert.Equal(t, daily.ID, convertedHistory.ID)
	// 	assert.Equal(t, daily.UserID, convertedHistory.UserID)
	// 	assert.Equal(t, daily.TaskID, convertedHistory.TaskID)
	// })

	// t.Run("OneTimeTaskCompletion Conversion", func(t *testing.T) {
	// 	oneTime := &model.OneTimeTaskCompletion{}
	// 	oneTime.FromTaskCompletionHistory(history)

	// 	assert.Equal(t, history.ID, oneTime.ID)
	// 	assert.Equal(t, history.UserID, oneTime.UserID)
	// 	assert.Equal(t, history.TaskID, oneTime.TaskID)
	// 	assert.Equal(t, history.PointsAwarded, oneTime.PointsAwarded)
	// 	assert.Equal(t, history.CompletionDate, oneTime.CompletionDate)

	// 	// Test back conversion
	// 	convertedHistory := oneTime.ToTaskCompletionHistory()
	// 	assert.Equal(t, oneTime.ID, convertedHistory.ID)
	// 	assert.Equal(t, oneTime.UserID, convertedHistory.UserID)
	// 	assert.Equal(t, oneTime.TaskID, convertedHistory.TaskID)
	// })
}
