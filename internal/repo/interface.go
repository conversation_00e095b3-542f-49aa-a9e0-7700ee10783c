package repo

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type (
	InvitationRepo interface {
		Create(ctx context.Context, user *model.User) error
		Update(ctx context.Context, user *model.User) error
		CreateWallet(ctx context.Context, wallet *model.UserWallet) error
		GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
		WithTransaction(ctx context.Context, fn func(ctx context.Context) (*model.User, error)) (*model.User, error)
		GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
		GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error)
		HasDirectReferral(ctx context.Context, userID uuid.UUID) (bool, error)
		IsInUpline(ctx context.Context, referrerID, userID uuid.UUID) (bool, error)
		GetAllReferrals(ctx context.Context, tx *gorm.DB, userID uuid.UUID) ([]model.Referral, error)
		GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
		GetByInvitationCode(ctx context.Context, code string) (*model.User, error)
		CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error
	}

	LevelRepo interface {
		GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error)
		GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error)
		UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error
	}
)
