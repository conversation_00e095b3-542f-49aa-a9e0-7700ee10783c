package repo

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// AffiliateRepositoryInterface defines the interface for affiliate repository operations
type AffiliateRepositoryInterface interface {
	// Affiliate Transaction operations
	CreateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error
	GetAffiliateTransactionByOrderID(ctx context.Context, orderID uuid.UUID) (*model.AffiliateTransaction, error)
	GetAffiliateTransactionByTxHash(ctx context.Context, txHash string) (*model.AffiliateTransaction, error)
	UpdateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error
	GetAffiliateTransactionsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error)
	GetAffiliateTransactionsByReferrerID(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error)
	GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error)
	MarkCommissionAsPaid(ctx context.Context, transactionIDs []uint) error

	// SOL Price operations
	UpsertSolPriceSnapshot(ctx context.Context, snapshot *model.SolPriceSnapshot) error
	GetLatestSolPrice(ctx context.Context) (*model.SolPriceSnapshot, error)
	GetSolPriceHistory(ctx context.Context, from, to time.Time) ([]model.SolPriceSnapshot, error)

	// Statistics operations
	GetUserTotalVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetReferrerTotalCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error)
	GetReferrerUnpaidCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error)
}

// AffiliateRepository implements the affiliate repository interface
type AffiliateRepository struct {
	db *gorm.DB
}

// NewAffiliateRepository creates a new affiliate repository
func NewAffiliateRepository() AffiliateRepositoryInterface {
	return &AffiliateRepository{
		db: global.GVA_DB,
	}
}

// CreateAffiliateTransaction creates a new affiliate transaction
func (r *AffiliateRepository) CreateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	return r.db.WithContext(ctx).Create(tx).Error
}

// GetAffiliateTransactionByOrderID retrieves an affiliate transaction by order ID
func (r *AffiliateRepository) GetAffiliateTransactionByOrderID(ctx context.Context, orderID uuid.UUID) (*model.AffiliateTransaction, error) {
	var tx model.AffiliateTransaction
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("order_id = ?", orderID).
		First(&tx).Error
	if err != nil {
		return nil, err
	}
	return &tx, nil
}

// GetAffiliateTransactionByTxHash retrieves an affiliate transaction by transaction hash
func (r *AffiliateRepository) GetAffiliateTransactionByTxHash(ctx context.Context, txHash string) (*model.AffiliateTransaction, error) {
	var tx model.AffiliateTransaction
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("tx_hash = ?", txHash).
		First(&tx).Error
	if err != nil {
		return nil, err
	}
	return &tx, nil
}

// UpdateAffiliateTransaction updates an existing affiliate transaction
func (r *AffiliateRepository) UpdateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	return r.db.WithContext(ctx).Save(tx).Error
}

// GetAffiliateTransactionsByUserID retrieves affiliate transactions for a specific user
func (r *AffiliateRepository) GetAffiliateTransactionsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	var transactions []model.AffiliateTransaction
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&transactions).Error
	return transactions, err
}

// GetAffiliateTransactionsByReferrerID retrieves affiliate transactions for a specific referrer
func (r *AffiliateRepository) GetAffiliateTransactionsByReferrerID(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	var transactions []model.AffiliateTransaction
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("referrer_id = ?", referrerID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&transactions).Error
	return transactions, err
}

// GetUnpaidCommissions retrieves unpaid commissions for a referrer
func (r *AffiliateRepository) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	var transactions []model.AffiliateTransaction
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("referrer_id = ? AND commission_paid = false AND commission_amount > 0", referrerID).
		Order("created_at ASC").
		Find(&transactions).Error
	return transactions, err
}

// MarkCommissionAsPaid marks commissions as paid for given transaction IDs
func (r *AffiliateRepository) MarkCommissionAsPaid(ctx context.Context, transactionIDs []uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Where("id IN ?", transactionIDs).
		Updates(map[string]interface{}{
			"commission_paid":    true,
			"commission_paid_at": now,
		}).Error
}

// UpsertSolPriceSnapshot creates or updates a SOL price snapshot
// Uses ON CONFLICT to ensure only one record per timestamp (millisecond precision)
func (r *AffiliateRepository) UpsertSolPriceSnapshot(ctx context.Context, snapshot *model.SolPriceSnapshot) error {
	// Use raw SQL for upsert to handle ON CONFLICT properly
	query := `
		INSERT INTO sol_price_snapshots (price, timestamp, updated_at)
		VALUES (?, ?, ?)
		ON CONFLICT (timestamp)
		DO UPDATE SET
			price = EXCLUDED.price,
			updated_at = EXCLUDED.updated_at
	`

	now := time.Now()
	snapshot.UpdatedAt = now

	return r.db.WithContext(ctx).Exec(query,
		snapshot.Price,
		snapshot.Timestamp,
		now,
	).Error
}

// GetLatestSolPrice retrieves the latest SOL price
func (r *AffiliateRepository) GetLatestSolPrice(ctx context.Context) (*model.SolPriceSnapshot, error) {
	var snapshot model.SolPriceSnapshot
	err := r.db.WithContext(ctx).
		Order("timestamp DESC").
		First(&snapshot).Error
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

// GetSolPriceHistory retrieves SOL price history within a time range
func (r *AffiliateRepository) GetSolPriceHistory(ctx context.Context, from, to time.Time) ([]model.SolPriceSnapshot, error) {
	var snapshots []model.SolPriceSnapshot
	err := r.db.WithContext(ctx).
		Where("timestamp BETWEEN ? AND ?", from, to).
		Order("timestamp ASC").
		Find(&snapshots).Error
	return snapshots, err
}

// GetUserTotalVolume calculates the total trading volume for a user
func (r *AffiliateRepository) GetUserTotalVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalVolume decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(volume_usd), 0) as total_volume").
		Where("user_id = ? AND status = ?", userID, model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalVolume, nil
}

// GetReferrerTotalCommission calculates the total commission earned by a referrer
func (r *AffiliateRepository) GetReferrerTotalCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalCommission decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_commission").
		Where("referrer_id = ? AND status = ?", referrerID, model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalCommission, nil
}

// GetReferrerUnpaidCommission calculates the unpaid commission for a referrer
func (r *AffiliateRepository) GetReferrerUnpaidCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		UnpaidCommission decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(commission_amount), 0) as unpaid_commission").
		Where("referrer_id = ? AND commission_paid = false AND status = ?", referrerID, model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.UnpaidCommission, nil
}
