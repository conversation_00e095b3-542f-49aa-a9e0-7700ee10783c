package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// AffiliateTransactionRepositoryInterface defines the interface for affiliate transaction operations
type AffiliateTransactionRepositoryInterface interface {
	GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error)
	GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error)
	GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error)
}

type AffiliateTransactionRepository struct {
	db *gorm.DB
}

func NewAffiliateTransactionRepository() AffiliateTransactionRepositoryInterface {
	return &AffiliateTransactionRepository{
		db: global.GVA_DB,
	}
}

// GetTotalVolumeByUserIDs calculates the total volume from affiliate transactions for given user IDs
func (r *AffiliateTransactionRepository) GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(volume_usd), 0) as total_volume").
		Where("user_id IN ?", userIDs).
		Where("status = ?", model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total volume: %w", err)
	}

	return result.TotalVolume, nil
}

// GetTransactingUserIDs gets the list of user IDs who have made transactions
func (r *AffiliateTransactionRepository) GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error) {
	if len(userIDs) == 0 {
		return []uuid.UUID{}, nil
	}

	var transactingUserIDs []uuid.UUID

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Distinct("user_id").
		Where("user_id IN ?", userIDs).
		Where("status = ?", model.StatusCompleted).
		Pluck("user_id", &transactingUserIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get transacting user IDs: %w", err)
	}

	return transactingUserIDs, nil
}

// GetVolumeByUserIDsAndPeriod calculates the total volume from affiliate transactions for given user IDs within a time period
func (r *AffiliateTransactionRepository) GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	err := r.db.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(volume_usd), 0) as total_volume").
		Where("user_id IN ?", userIDs).
		Where("status = ?", model.StatusCompleted).
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total volume by period: %w", err)
	}

	return result.TotalVolume, nil
}
