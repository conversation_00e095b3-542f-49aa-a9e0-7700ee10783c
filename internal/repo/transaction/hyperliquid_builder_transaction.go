package transaction

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// HyperLiquidBuilderTransactionRepository defines the interface for interacting with HyperLiquid transactions
type HyperLiquidBuilderTransactionRepositoryInterface interface {
	Create(ctx context.Context, tx *model.HyperLiquidBuilderTransaction) error
	BulkCreate(ctx context.Context, txs []model.HyperLiquidBuilderTransaction) error
}

type HyperLiquidBuilderTransactionRepository struct {
	db *gorm.DB
}

func NewHyperLiquidBuilderTransactionRepository() HyperLiquidBuilderTransactionRepositoryInterface {
	return &HyperLiquidBuilderTransactionRepository{
		db: global.GVA_DB,
	}
}

func (r *HyperLiquidBuilderTransactionRepository) Create(ctx context.Context, tx *model.HyperLiquidBuilderTransaction) error {
	transaction := r.db.WithContext(ctx).Model(&model.HyperLiquidBuilderTransaction{}).Create(tx)

	if transaction.Error != nil {
		return transaction.Error
	}

	return nil
}

func (r *HyperLiquidBuilderTransactionRepository) BulkCreate(ctx context.Context, txs []model.HyperLiquidBuilderTransaction) error {
	if len(txs) == 0 {
		return nil
	}

	// Use the database instance with context for bulk creation
	if err := r.db.WithContext(ctx).Model(&model.HyperLiquidBuilderTransaction{}).
		Clauses(clause.OnConflict{DoNothing: true}).
		CreateInBatches(txs, 100).Error; err != nil {
		return err
	}

	return nil
}
