package transaction

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// HyperLiquidBuilderTransactionRepository defines the interface for interacting with HyperLiquid transactions
type BuilderCrawlerHistoryInterface interface {
	Create(ctx context.Context, history *model.BuilderCrawlerHistory) error
	GetLastHistory(ctx context.Context, builderAddress string) (*model.BuilderCrawlerHistory, error)
}

type BuilderCrawlerHistoryRepository struct {
	db *gorm.DB
}

func NewBuilderCrawlerHistoryRepository() BuilderCrawlerHistoryInterface {
	return &BuilderCrawlerHistoryRepository{
		db: global.GVA_DB,
	}
}

func (r *BuilderCrawlerHistoryRepository) Create(ctx context.Context, history *model.BuilderCrawlerHistory) error {
	result := r.db.WithContext(ctx).Model(&model.BuilderCrawlerHistory{}).Create(history)

	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (r *BuilderCrawlerHistoryRepository) GetLastHistory(ctx context.Context, builderAddress string) (*model.BuilderCrawlerHistory, error) {
	var history model.BuilderCrawlerHistory
	result := r.db.WithContext(ctx).Model(&model.BuilderCrawlerHistory{}).
		Where("builder_address = ?", builderAddress).
		Order("date DESC").First(&history)

	if result.Error != nil {
		return nil, result.Error
	}

	return &history, nil
}
