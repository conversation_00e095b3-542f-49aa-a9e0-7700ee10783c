package middleware

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// ActivityCashbackMiddleware provides middleware functions for activity cashback system
type ActivityCashbackMiddleware struct {
	systemInitializer *activity_cashback.SystemInitializer
}

// NewActivityCashbackMiddleware creates a new ActivityCashbackMiddleware
func NewActivityCashbackMiddleware() *ActivityCashbackMiddleware {
	return &ActivityCashbackMiddleware{
		systemInitializer: activity_cashback.GetGlobalSystemInitializer(),
	}
}

// TrackUserActivity tracks user activity for task completion
func (m *ActivityCashbackMiddleware) TrackUserActivity() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Continue with the request first
		c.Next()

		// Track activity after request completion (async)
		go func() {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			userID := c.GetString("userId")
			if userID == "" {
				return // No user ID, skip tracking
			}

			// Determine activity type based on request path and method
			activityType := m.determineActivityType(c)
			if activityType == "" {
				return // No trackable activity
			}

			// Prepare activity data
			activityData := map[string]interface{}{
				"timestamp":   time.Now(),
				"method":      c.Request.Method,
				"path":        c.Request.URL.Path,
				"status_code": c.Writer.Status(),
				"user_agent":  c.Request.UserAgent(),
				"ip_address":  c.ClientIP(),
			}

			// Add specific data based on activity type
			m.enrichActivityData(c, activityType, activityData)

			// Process the activity
			if err := activity_cashback.ProcessGlobalEvent(ctx, activityType, userID, activityData); err != nil {
				global.GVA_LOG.Error("Failed to process activity event",
					zap.Error(err),
					zap.String("user_id", userID),
					zap.String("activity_type", activityType))
			}
		}()
	}
}

// determineActivityType determines the activity type based on the request
func (m *ActivityCashbackMiddleware) determineActivityType(c *gin.Context) string {
	path := c.Request.URL.Path
	method := c.Request.Method

	// Login activity
	if method == "POST" && (path == "/api/auth/login" || path == "/api/user/login") {
		return "user_login"
	}

	// Trading activities
	if method == "POST" && (path == "/api/trade" || path == "/api/trading/order") {
		return "trade_completed"
	}

	// Market check activity
	if method == "GET" && (path == "/api/market" || path == "/api/market/data") {
		return "market_check"
	}

	// GraphQL activities
	if method == "POST" && path == "/graphql" {
		return m.determineGraphQLActivityType(c)
	}

	return ""
}

// determineGraphQLActivityType determines activity type for GraphQL requests
func (m *ActivityCashbackMiddleware) determineGraphQLActivityType(c *gin.Context) string {
	// This would need to parse the GraphQL query to determine the operation
	// For now, we'll return empty string as GraphQL activities are handled differently
	return ""
}

// enrichActivityData adds specific data based on activity type
func (m *ActivityCashbackMiddleware) enrichActivityData(c *gin.Context, activityType string, data map[string]interface{}) {
	switch activityType {
	case "trade_completed":
		m.enrichTradingData(c, data)
	case "user_login":
		m.enrichLoginData(c, data)
	case "market_check":
		m.enrichMarketData(c, data)
	}
}

// enrichTradingData enriches data for trading activities
func (m *ActivityCashbackMiddleware) enrichTradingData(c *gin.Context, data map[string]interface{}) {
	// Try to extract trading data from request body or response
	// This would depend on your trading API structure

	// Example: Extract from request body
	if c.Request.Body != nil {
		var requestBody map[string]interface{}
		if err := c.ShouldBindJSON(&requestBody); err == nil {
			if volume, ok := requestBody["volume"].(float64); ok {
				data["volume"] = volume
			}
			if tradeType, ok := requestBody["type"].(string); ok {
				data["trade_type"] = tradeType
			}
			if symbol, ok := requestBody["symbol"].(string); ok {
				data["symbol"] = symbol
			}
		}
	}
}

// enrichLoginData enriches data for login activities
func (m *ActivityCashbackMiddleware) enrichLoginData(c *gin.Context, data map[string]interface{}) {
	data["login_method"] = "web"
	data["device_type"] = m.getDeviceType(c.Request.UserAgent())
}

// enrichMarketData enriches data for market check activities
func (m *ActivityCashbackMiddleware) enrichMarketData(c *gin.Context, data map[string]interface{}) {
	// Add query parameters if any
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			data[key] = values[0]
		}
	}
}

// getDeviceType determines device type from user agent
func (m *ActivityCashbackMiddleware) getDeviceType(userAgent string) string {
	// Simple device detection logic
	if userAgent == "" {
		return "unknown"
	}

	// This is a simplified version - you might want to use a proper user agent parser
	if len(userAgent) > 100 {
		return "desktop"
	}
	return "mobile"
}

// HealthCheck provides a health check endpoint for activity cashback system
func (m *ActivityCashbackMiddleware) HealthCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if m.systemInitializer == nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "unavailable",
				"reason": "system_not_initialized",
			})
			return
		}

		health := m.systemInitializer.GetHealthCheck(ctx)

		statusCode := http.StatusOK
		if status, ok := health["status"].(string); ok && status != "healthy" {
			statusCode = http.StatusServiceUnavailable
		}

		c.JSON(statusCode, health)
	}
}

// SystemStatus provides a system status endpoint
func (m *ActivityCashbackMiddleware) SystemStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if m.systemInitializer == nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "system_not_initialized",
			})
			return
		}

		status := m.systemInitializer.GetSystemStatus(ctx)
		c.JSON(http.StatusOK, status)
	}
}

// AdminForceReset provides an admin endpoint to force task reset
func (m *ActivityCashbackMiddleware) AdminForceReset() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check admin permissions (implement your admin auth logic here)
		if !m.isAdmin(c) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "admin access required",
			})
			return
		}

		resetType := c.Param("type")
		if resetType == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "reset type required (daily/weekly/monthly)",
			})
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := m.systemInitializer.ForceTaskReset(ctx, resetType); err != nil {
			global.GVA_LOG.Error("Failed to force task reset", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "failed to reset tasks",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "task reset completed",
			"type":    resetType,
		})
	}
}

// AdminRecalculateTiers provides an admin endpoint to recalculate all tiers
func (m *ActivityCashbackMiddleware) AdminRecalculateTiers() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check admin permissions
		if !m.isAdmin(c) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "admin access required",
			})
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
		defer cancel()

		if err := m.systemInitializer.RecalculateAllTiers(ctx); err != nil {
			global.GVA_LOG.Error("Failed to recalculate tiers", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "failed to recalculate tiers",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "tier recalculation completed",
		})
	}
}

// ProcessWebhook processes webhooks from external systems
func (m *ActivityCashbackMiddleware) ProcessWebhook() gin.HandlerFunc {
	return func(c *gin.Context) {
		var webhookData struct {
			EventType string                 `json:"event_type"`
			UserID    string                 `json:"user_id"`
			Data      map[string]interface{} `json:"data"`
		}

		if err := c.ShouldBindJSON(&webhookData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "invalid webhook data",
			})
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := activity_cashback.ProcessGlobalEvent(ctx, webhookData.EventType, webhookData.UserID, webhookData.Data); err != nil {
			global.GVA_LOG.Error("Failed to process webhook event", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "failed to process event",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "event processed successfully",
		})
	}
}

// isAdmin checks if the user has admin permissions using API key
func (m *ActivityCashbackMiddleware) isAdmin(c *gin.Context) bool {
	apiKey := c.GetHeader("x-api-key")
	if apiKey == "" {
		// Try alternative header names
		apiKey = c.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = c.GetHeader("Authorization")
			if strings.HasPrefix(apiKey, "Bearer ") {
				apiKey = strings.TrimPrefix(apiKey, "Bearer ")
			}
		}
	}

	// Validate API key against configured internal API key
	return apiKey != "" && apiKey == global.GVA_CONFIG.Admin.InternalAPIKey
}

// RegisterRoutes registers activity cashback routes
func (m *ActivityCashbackMiddleware) RegisterRoutes(router *gin.Engine) {
	activityGroup := router.Group("/api/activity-cashback")
	{
		activityGroup.GET("/health", m.HealthCheck())
		activityGroup.GET("/status", m.SystemStatus())
		activityGroup.POST("/webhook", m.ProcessWebhook())

		// Admin routes
		adminGroup := activityGroup.Group("/admin")
		{
			adminGroup.POST("/reset/:type", m.AdminForceReset())
			adminGroup.POST("/recalculate-tiers", m.AdminRecalculateTiers())
		}
	}
}
