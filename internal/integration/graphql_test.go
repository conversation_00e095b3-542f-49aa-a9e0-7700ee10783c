//go:build integration
// +build integration

package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MockInvitationService is a mock implementation of service.InvitationI
type MockInvitationService struct {
	mock.Mock
}

func (m *MockInvitationService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockInvitationService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	args := m.Called(ctx, userID, chain, name, walletAddress, walletID, walletAccountID, walletType, invitationCode, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	args := m.Called(ctx, referrerID, userID)
	return args.Error(0)
}

func (m *MockInvitationService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	args := m.Called(ctx, invitationCode)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationService) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationService) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockInvitationService) GetMemeTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockInvitationService) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

// MockLevelService is a mock implementation of service.LevelI
type MockLevelService struct {
	mock.Mock
}

func (m *MockLevelService) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error) {
	args := m.Called(ctx, levelID, directRate, indirectRate, extendedRate, memeFeeRebate)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

// GraphQLRequest represents a GraphQL request
type GraphQLRequest struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

// GraphQLResponse represents a GraphQL response
type GraphQLResponse struct {
	Data   interface{} `json:"data"`
	Errors []struct {
		Message string        `json:"message"`
		Path    []interface{} `json:"path"`
	} `json:"errors,omitempty"`
}

// TestGraphQLIntegration tests the GraphQL API integration
func TestGraphQLIntegration(t *testing.T) {
	t.Skip("Skipping GraphQL integration tests due to complex resolver setup")
}

// executeGraphQLRequest executes a GraphQL request and returns the response
func executeGraphQLRequest(t *testing.T, srv http.Handler, req GraphQLRequest) GraphQLResponse {
	// Marshal request to JSON
	reqBody, err := json.Marshal(req)
	require.NoError(t, err)

	// Create HTTP request
	httpReq := httptest.NewRequest("POST", "/graphql", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Execute request
	srv.ServeHTTP(w, httpReq)

	// Parse response
	var resp GraphQLResponse
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	require.NoError(t, err)

	return resp
}

// executeGraphQLRequestWithAuth executes a GraphQL request with authentication
func executeGraphQLRequestWithAuth(t *testing.T, srv http.Handler, req GraphQLRequest, userID uuid.UUID) GraphQLResponse {
	// Marshal request to JSON
	reqBody, err := json.Marshal(req)
	require.NoError(t, err)

	// Create HTTP request
	httpReq := httptest.NewRequest("POST", "/graphql", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	// Add user ID to context (simulating authentication middleware)
	ctx := context.WithValue(httpReq.Context(), "userId", userID.String())
	httpReq = httpReq.WithContext(ctx)

	// Create response recorder
	w := httptest.NewRecorder()

	// Execute request
	srv.ServeHTTP(w, httpReq)

	// Parse response
	var resp GraphQLResponse
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	require.NoError(t, err)

	return resp
}
