package global

import (
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/songzhibin97/gkit/cache/local_cache"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

var (
	GVA_DB                  *gorm.DB
	GVA_DBList              map[string]*gorm.DB
	GVA_REDIS               redis.UniversalClient
	GVA_REDISList           map[string]redis.UniversalClient
	GVA_CONFIG              config.Server
	GVA_VP                  *viper.Viper
	GVA_LOG                 *zap.Logger
	GVA_Concurrency_Control = &singleflight.Group{}
	GVA_ROUTERS             gin.RoutesInfo
	GVA_ACTIVE_DBNAME       *string
	BlackCache              local_cache.Cache
	lock                    sync.RWMutex
	// GVA_NATS                interface{} // Will be set to *nats.NATSClient - Temporarily disabled, only using nats-meme for now
	GVA_NATS_MEME *nats.NATSClient // Will be set to *nats.NATSClient
	GVA_NATS_DEX  *nats.NATSClient // Will be set to *nats.NATSClient
	// GVA_NATS_DEX            interface{} // Will be set to *nats.NATSClient - Temporarily disabled
	GVA_SENSITIVE_CONFIG *config.AgentSystemSensitiveConfig
)
