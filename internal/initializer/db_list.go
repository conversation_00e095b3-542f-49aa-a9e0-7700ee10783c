package initializer

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gorm.io/gorm"
)

const sys = "system"

func DBList() {
	dbMap := make(map[string]*gorm.DB)
	for _, info := range global.GVA_CONFIG.DBList {
		if info.Disable {
			continue
		}
		switch info.Type {
		case "pgsql":
			db := GormPgSqlByConfig(config.Pgsql{GeneralDB: info.GeneralDB})
			if db != nil {
				dbMap[info.AliasName] = db
			}
		default:
			continue
		}
	}
	if sysDB, ok := dbMap[sys]; ok && sysDB != nil {
		global.GVA_DB = sysDB
	}
	global.GVA_DBList = dbMap
}
