package initializer

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "pgsql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	default:
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	}
}

func gormConfig(prefix string, singular bool) *gorm.Config {
	config := &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger:                                   logger.Default.LogMode(logger.Silent),
	}
	_default := schema.NamingStrategy{
		TablePrefix:   prefix,
		SingularTable: singular,
	}
	config.NamingStrategy = _default
	return config
}
