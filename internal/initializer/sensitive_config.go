package initializer

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
)

type HyperLiquidConfig map[string]config.HyperLiquidSystemConfig

func LoadSensitiveConfig() *config.AgentSystemSensitiveConfig {
	var sensitiveConfig config.AgentSystemSensitiveConfig
	stage := os.Getenv("STAGE")

	fileHyperLiquid, err := os.Open("sensitive_configs/hyperliquid.json")
	if err != nil {
		panic(fmt.Errorf("failed to open config file: %w", err))
	}
	defer fileHyperLiquid.Close()
	var hyperLiquidConfig HyperLiquidConfig
	if err := json.NewDecoder(fileHyperLiquid).Decode(&hyperLiquidConfig); err != nil {
		panic(fmt.E<PERSON><PERSON>("failed to decode config file: %w", err))
	}
	stageHyperLiquidCfg, ok := hyperLiquidConfig[stage]

	if !ok {
		panic(fmt.<PERSON><PERSON><PERSON>("stage %q not found in config", stage))
	}
	sensitiveConfig.HyperLiquid = stageHyperLiquidCfg

	return &sensitiveConfig
}
