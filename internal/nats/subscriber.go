package nats

import "github.com/nats-io/nats.go"

type Subscriber interface {
	Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error)

	SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error)

	AddStream(cfg *nats.StreamConfig) (*nats.StreamInfo, error)

	// Consumer management methods
	ConsumerInfo(stream, consumer string) (*nats.ConsumerInfo, error)
	DeleteConsumer(stream, consumer string) error
}
