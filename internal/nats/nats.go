package nats

import (
	"crypto/tls"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
)

type RawMsg struct {
	Msg nats.Msg
}

type NATSClient struct {
	conn *nats.Conn
	js   nats.JetStreamContext
}

func InitNatsJetStream(natConfig config.Nats) *NATSClient {
	var tlsConfig *tls.Config
	if natConfig.UseTLS {
		tlsConfig = &tls.Config{
			MinVersion: tls.VersionTLS12,
		}
	}

	natsOptions := []nats.Option{
		nats.Timeout(10 * time.Second),
		nats.MaxReconnects(-1), // Unlimited reconnects
		nats.ReconnectWait(2 * time.Second),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			log.Printf("NATS disconnected: %v", err)
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			log.Printf("NATS reconnected to %s", nc.ConnectedUrl())
		}),
		nats.ClosedHandler(func(nc *nats.Conn) {
			log.Fatalf("NATS connection closed permanently. Exiting: %v", nc.LastError())
		}),
	}
	if tlsConfig != nil {
		natsOptions = append(natsOptions, nats.Secure(tlsConfig))
	}

	if natConfig.Token != "" {
		natsOptions = append(natsOptions, nats.Token(natConfig.Token))
	} else {
		natsOptions = append(natsOptions, nats.UserInfo(natConfig.User, natConfig.Pass))
	}

	nc, err := nats.Connect(natConfig.URL, natsOptions...)

	if err != nil {
		log.Fatalf("Cannot connect to NATS: %v", err)
	}

	js, err := nc.JetStream()
	if err != nil {
		log.Fatalf("Cannot create JetStream: %v", err)
	}
	log.Println("Connect to NATS JetStream successful!")

	return &NATSClient{
		conn: nc,
		js:   js,
	}
}

func (n *NATSClient) Close() {
	if n.conn != nil && n.conn.Status() == nats.CONNECTED {
		log.Println("Closing NATS connection...")
		n.conn.Close()
		log.Println("NATS connection closed.")
	}
}

func (n *NATSClient) Publish(subject string, data []byte) error {
	return n.conn.Publish(subject, data)
}

func (n *NATSClient) PublishJS(subject string, data []byte, opts ...nats.PubOpt) (*nats.PubAck, error) {
	return n.js.Publish(subject, data, opts...)
}

func (n *NATSClient) Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error) {
	return n.conn.Subscribe(subject, handler)
}

func (n *NATSClient) SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error) {
	return n.js.Subscribe(subject, handler, opts...)
}

func (n *NATSClient) SubscribeQueue(subject, queue string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error) {
	return n.js.QueueSubscribe(subject, queue, handler, opts...)
}

func (n *NATSClient) PullSubscribe(subject, queue string, opts ...nats.SubOpt) (*nats.Subscription, error) {
	return n.js.PullSubscribe(subject, queue, opts...)
}

func (n *NATSClient) AddStream(cfg *nats.StreamConfig) (*nats.StreamInfo, error) {
	info, err := n.js.StreamInfo(cfg.Name)
	if info != nil {
		for _, subject := range info.Config.Subjects {
			if cfg.Subjects[0] == subject {
				log.Printf("Stream '%s' already exists with subject %s\n", cfg.Name, subject)
				return info, nil
			}
		}
	}

	info, err = n.js.AddStream(cfg)
	if err != nil {
		if errors.Is(err, nats.ErrStreamNameAlreadyInUse) {
			log.Printf("Stream '%s' already exists.", cfg.Name)
			return n.js.StreamInfo(cfg.Name) // Return existing stream info
		}
		return nil, fmt.Errorf("failed to add stream '%s': %w", cfg.Name, err)
	}

	log.Printf("Stream '%s' added/updated successfully.", cfg.Name)
	return info, nil
}

func (n *NATSClient) PublisherAddStream(streamName string, subjects []string) {
	_, err := n.AddStream(&nats.StreamConfig{
		Name:     streamName,
		Subjects: subjects,
		Storage:  nats.FileStorage,
	})
	if err != nil {
		log.Printf("Error adding stream '%s': %v", streamName, err)
	}
}

// ConsumerInfo returns information about a consumer
func (n *NATSClient) ConsumerInfo(stream, consumer string) (*nats.ConsumerInfo, error) {
	return n.js.ConsumerInfo(stream, consumer)
}

// DeleteConsumer deletes a consumer from a stream
func (n *NATSClient) DeleteConsumer(stream, consumer string) error {
	return n.js.DeleteConsumer(stream, consumer)
}

func (n *NATSClient) EnsureReadonlyStreamExists(streamName string) error {
	_, err := n.js.StreamInfo(streamName)
	if err != nil {
		return fmt.Errorf("failed to get stream: %s %v", streamName, err)
	}

	return nil
}

func (n *NATSClient) EnsureStreamConsumers(streamName string, queueName string) error {
	info, err := n.js.ConsumerInfo(streamName, queueName)

	if err != nil || info == nil {
		_, err = n.js.AddConsumer(streamName, &nats.ConsumerConfig{
			Durable:    queueName,
			MaxDeliver: 100,
			AckPolicy:  nats.AckExplicitPolicy,
			BackOff: []time.Duration{
				500 * time.Millisecond,
				1 * time.Second,
				5 * time.Second,
				15 * time.Second,
			},
		})

		return err
	}

	config := info.Config
	if config.AckWait.Seconds() <= 1 {
		config.AckWait = 30 * time.Second
		config.BackOff = []time.Duration{
			500 * time.Millisecond,
			1 * time.Second,
			5 * time.Second,
			15 * time.Second,
		}

		_, err := n.js.UpdateConsumer(streamName, &config)
		if err != nil {
			return err
		}
	}

	return nil
}
