package utils

import (
	"regexp"
	"strings"
)

// IsValidEmail validates email format using regex
func IsValidEmail(email string) bool {
	if email == "" {
		return true // Empty email is considered valid (will be stored as empty string)
	}

	// Basic email regex pattern
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(strings.TrimSpace(email))
}

// NormalizeEmail normalizes email by trimming whitespace and converting to lowercase
func NormalizeEmail(email string) string {
	if email == "" {
		return ""
	}
	return strings.ToLower(strings.TrimSpace(email))
}

// StringToPointer converts string to *string, returns nil for empty strings
func StringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// PointerToString converts *string to string, returns empty string for nil
func PointerToString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// IsValidInvitationCode validates invitation code format
// - Must be 5-15 characters long (counting Unicode runes, not bytes)
// - Cannot contain whitespace characters (spaces, tabs, newlines, etc.)
// - Allows alphanumeric characters, Unicode characters (including Chinese), and common symbols
func IsValidInvitationCode(code string) bool {
	// Check length using rune count for proper Unicode support
	runes := []rune(code)
	if len(runes) < 5 || len(runes) > 15 {
		return false
	}

	// Check for whitespace characters (spaces, tabs, newlines, etc.)
	// This is the main restriction - no whitespace allowed
	if regexp.MustCompile(`\s`).MatchString(code) {
		return false
	}

	// Allow most characters except whitespace and control characters
	// This includes Unicode characters (Chinese, Japanese, Korean, etc.)
	// and common symbols like @, #, $, %, etc.
	for _, r := range code {
		// Reject control characters and whitespace
		if r < 32 || r == 127 {
			return false
		}
	}

	return true
}

// DetectChainTypeFromAddress detects the chain type based on wallet address format
// This is a basic implementation that can be enhanced with more sophisticated detection logic
func DetectChainTypeFromAddress(address string) string {
	if address == "" {
		return ""
	}

	// Remove any whitespace and convert to lowercase for consistent comparison
	addr := strings.ToLower(strings.TrimSpace(address))

	// EVM/ARB addresses: 0x followed by 40 hex characters (42 total)
	if len(addr) == 42 && strings.HasPrefix(addr, "0x") {
		// Check if it's a valid hex string
		if regexp.MustCompile(`^0x[0-9a-f]{40}$`).MatchString(addr) {
			// For now, default to EVM. In a real implementation, you might want to:
			// - Check against known ARB addresses
			// - Use chain ID from transaction data
			// - Query blockchain APIs
			return "EVM"
		}
	}

	// Solana addresses: Base58 encoded, typically 32-44 characters
	// Solana addresses don't have a specific prefix like 0x
	if len(addr) >= 32 && len(addr) <= 44 {
		// Check if it's a valid Base58 string (only alphanumeric characters, no 0, O, I, l)
		if regexp.MustCompile(`^[1-9A-HJ-NP-Za-km-z]+$`).MatchString(addr) {
			return "SOLANA"
		}
	}

	// TRON addresses: T followed by 33 characters (34 total)
	if len(addr) == 34 && strings.HasPrefix(addr, "t") {
		// Check if it's a valid Base58 string
		if regexp.MustCompile(`^t[1-9A-HJ-NP-Za-km-z]{33}$`).MatchString(addr) {
			return "TRON"
		}
	}

	// If no pattern matches, return empty string
	return ""
}
