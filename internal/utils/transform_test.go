package utils

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// Test structs for transformation tests
type SourceStruct struct {
	Name  string `json:"name"`
	Age   int    `json:"age"`
	Email string `json:"email"`
}

type TargetStruct struct {
	Name  string `json:"name"`
	Age   int    `json:"age"`
	Email string `json:"email"`
}

type PartialTargetStruct struct {
	Name string `json:"name"`
	Age  int    `json:"age"`
}

func TestTranslate(t *testing.T) {
	// Setup test configuration to initialize global logger
	test.SetupTestConfig()
	defer test.CleanupTestConfig()
	tests := []struct {
		name     string
		source   interface{}
		expected interface{}
	}{
		{
			name: "struct to struct with same fields",
			source: SourceStruct{
				Name:  "<PERSON>",
				Age:   30,
				Email: "<EMAIL>",
			},
			expected: &TargetStruct{
				Name:  "<PERSON>",
				Age:   30,
				Email: "<EMAIL>",
			},
		},
		{
			name: "struct to struct with partial fields",
			source: SourceStruct{
				Name:  "<PERSON>",
				Age:   25,
				Email: "<EMAIL>",
			},
			expected: &PartialTargetStruct{
				Name: "<PERSON> Doe",
				Age:  25,
			},
		},
		{
			name:     "nil source",
			source:   nil,
			expected: &TargetStruct{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch expected := tt.expected.(type) {
			case *TargetStruct:
				result := Translate[TargetStruct](tt.source)
				assert.NotNil(t, result)
				if tt.source != nil {
					assert.Equal(t, expected.Name, result.Name)
					assert.Equal(t, expected.Age, result.Age)
					assert.Equal(t, expected.Email, result.Email)
				}
			case *PartialTargetStruct:
				result := Translate[PartialTargetStruct](tt.source)
				assert.NotNil(t, result)
				if tt.source != nil {
					assert.Equal(t, expected.Name, result.Name)
					assert.Equal(t, expected.Age, result.Age)
				}
			}
		})
	}
}

func TestTranslateByJSON(t *testing.T) {
	// Setup test configuration to initialize global logger
	test.SetupTestConfig()
	defer test.CleanupTestConfig()
	tests := []struct {
		name     string
		source   interface{}
		expected interface{}
	}{
		{
			name: "struct to struct via JSON",
			source: SourceStruct{
				Name:  "John Doe",
				Age:   30,
				Email: "<EMAIL>",
			},
			expected: &TargetStruct{
				Name:  "John Doe",
				Age:   30,
				Email: "<EMAIL>",
			},
		},
		{
			name: "map to struct via JSON",
			source: map[string]interface{}{
				"name":  "Jane Doe",
				"age":   25,
				"email": "<EMAIL>",
			},
			expected: &TargetStruct{
				Name:  "Jane Doe",
				Age:   25,
				Email: "<EMAIL>",
			},
		},
		{
			name:     "nil source",
			source:   nil,
			expected: &TargetStruct{}, // TranslateByJSON returns empty struct for nil input
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TranslateByJSON[TargetStruct](tt.source)

			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				expected := tt.expected.(*TargetStruct)
				assert.NotNil(t, result)
				assert.Equal(t, expected.Name, result.Name)
				assert.Equal(t, expected.Age, result.Age)
				assert.Equal(t, expected.Email, result.Email)
			}
		})
	}
}

func TestJsonDecode(t *testing.T) {
	tests := []struct {
		name     string
		data     interface{}
		expected TargetStruct
		wantErr  bool
	}{
		{
			name: "valid struct data",
			data: SourceStruct{
				Name:  "John Doe",
				Age:   30,
				Email: "<EMAIL>",
			},
			expected: TargetStruct{
				Name:  "John Doe",
				Age:   30,
				Email: "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "valid map data",
			data: map[string]interface{}{
				"name":  "Jane Doe",
				"age":   25,
				"email": "<EMAIL>",
			},
			expected: TargetStruct{
				Name:  "Jane Doe",
				Age:   25,
				Email: "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name:     "nil data",
			data:     nil,
			expected: TargetStruct{},
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result TargetStruct
			err := JsonDecode(tt.data, &result)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected.Name, result.Name)
				assert.Equal(t, tt.expected.Age, result.Age)
				assert.Equal(t, tt.expected.Email, result.Email)
			}
		})
	}
}

func TestParseStringArray(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "empty array",
			input:    "[]",
			expected: []string{},
		},
		{
			name:     "single element with single quotes",
			input:    "['test']",
			expected: []string{"test"},
		},
		{
			name:     "single element with double quotes",
			input:    `["test"]`,
			expected: []string{"test"},
		},
		{
			name:     "multiple elements with single quotes",
			input:    "['apple', 'banana', 'cherry']",
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "multiple elements with double quotes",
			input:    `["apple", "banana", "cherry"]`,
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "mixed quotes",
			input:    `['apple', "banana", 'cherry']`,
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "elements with spaces",
			input:    "[ 'apple' , 'banana' , 'cherry' ]",
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "elements without quotes",
			input:    "[apple, banana, cherry]",
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "empty elements should be filtered",
			input:    "[apple, , banana, , cherry]",
			expected: []string{"apple", "banana", "cherry"},
		},
		{
			name:     "single element without brackets",
			input:    "test",
			expected: []string{"test"},
		},
		{
			name:     "empty string",
			input:    "",
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseStringArray(tt.input)
			assert.Equal(t, tt.expected, result, "ParseStringArray(%q) = %v, want %v", tt.input, result, tt.expected)
		})
	}
}

func TestParseStringArrayEdgeCases(t *testing.T) {
	t.Run("array with special characters", func(t *testing.T) {
		input := `["<EMAIL>", "<EMAIL>", "special#chars$123"]`
		expected := []string{"<EMAIL>", "<EMAIL>", "special#chars$123"}
		result := ParseStringArray(input)
		assert.Equal(t, expected, result)
	})

	t.Run("array with numbers as strings", func(t *testing.T) {
		input := `["123", "456", "789"]`
		expected := []string{"123", "456", "789"}
		result := ParseStringArray(input)
		assert.Equal(t, expected, result)
	})

	t.Run("array with unicode characters", func(t *testing.T) {
		input := `["测试", "тест", "テスト"]`
		expected := []string{"测试", "тест", "テスト"}
		result := ParseStringArray(input)
		assert.Equal(t, expected, result)
	})
}

// Timestamp conversion tests
func TestTimeToTimestamp(t *testing.T) {
	// Test with nil
	result := TimeToTimestamp(nil)
	assert.Nil(t, result)

	// Test with valid time
	testTime := time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)
	result = TimeToTimestamp(&testTime)
	assert.NotNil(t, result)

	expectedTimestamp := testTime.UnixMilli()
	assert.Equal(t, expectedTimestamp, *result)
}

func TestTimestampToTime(t *testing.T) {
	// Test with nil
	result := TimestampToTime(nil)
	assert.Nil(t, result)

	// Test with valid timestamp
	timestamp := int64(1705315800000) // 2024-01-15 10:30:00 UTC in milliseconds
	result = TimestampToTime(&timestamp)
	assert.NotNil(t, result)

	expectedTime := time.UnixMilli(timestamp)
	assert.True(t, result.Equal(expectedTime))
}

func TestValidateTimestamp(t *testing.T) {
	// Test with nil
	assert.True(t, ValidateTimestamp(nil))

	// Test with valid timestamp (current time)
	now := time.Now().UnixMilli()
	assert.True(t, ValidateTimestamp(&now))

	// Test with timestamp too far in the past (before year 2000)
	oldTimestamp := time.Date(1999, 1, 1, 0, 0, 0, 0, time.UTC).UnixMilli()
	assert.False(t, ValidateTimestamp(&oldTimestamp))

	// Test with timestamp too far in the future (more than 10 years from now)
	futureTimestamp := time.Now().AddDate(11, 0, 0).UnixMilli()
	assert.False(t, ValidateTimestamp(&futureTimestamp))

	// Test with valid timestamp in the past (year 2020)
	validPastTimestamp := time.Date(2020, 6, 15, 12, 0, 0, 0, time.UTC).UnixMilli()
	assert.True(t, ValidateTimestamp(&validPastTimestamp))

	// Test with valid timestamp in the future (1 year from now)
	validFutureTimestamp := time.Now().AddDate(1, 0, 0).UnixMilli()
	assert.True(t, ValidateTimestamp(&validFutureTimestamp))
}

func TestRoundTripConversion(t *testing.T) {
	// Test round trip: Time -> Timestamp -> Time
	originalTime := time.Date(2024, 3, 15, 14, 30, 45, 123000000, time.UTC)

	// Convert to timestamp
	timestamp := TimeToTimestamp(&originalTime)
	assert.NotNil(t, timestamp)

	// Convert back to time
	convertedTime := TimestampToTime(timestamp)
	assert.NotNil(t, convertedTime)

	// Note: We lose nanosecond precision when converting to milliseconds
	// So we compare with millisecond precision
	expectedTime := time.UnixMilli(originalTime.UnixMilli())
	assert.True(t, convertedTime.Equal(expectedTime))
}
