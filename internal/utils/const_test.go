package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskConstants(t *testing.T) {
	t.Run("task constants have expected values", func(t *testing.T) {
		// Test that constants have the expected values
		assert.Equal(t, 0, TaskAgentReferralSnapshot)
		assert.Equal(t, 1, TaskLevelUpgrade)
	})

	t.Run("task constants are unique", func(t *testing.T) {
		// Ensure all task constants have unique values
		constants := []int{
			TaskAgentReferralSnapshot,
			TaskLevelUpgrade,
		}

		// Check for uniqueness
		seen := make(map[int]bool)
		for _, constant := range constants {
			assert.False(t, seen[constant], "Duplicate constant value found: %d", constant)
			seen[constant] = true
		}
	})

	t.Run("task constants are sequential starting from 0", func(t *testing.T) {
		// Verify that constants follow the iota pattern (0, 1, 2, ...)
		expectedValues := []struct {
			name     string
			constant int
			expected int
		}{
			{"TaskAgentReferralSnapshot", TaskAgentReferralSnapshot, 0},
			{"TaskLevelUpgrade", TaskLevelUpgrade, 1},
		}

		for _, test := range expectedValues {
			assert.Equal(t, test.expected, test.constant,
				"Constant %s should have value %d, got %d",
				test.name, test.expected, test.constant)
		}
	})
}

func TestTaskConstantsUsage(t *testing.T) {
	t.Run("constants can be used in switch statements", func(t *testing.T) {
		testCases := []struct {
			taskType     int
			expectedName string
		}{
			{TaskAgentReferralSnapshot, "AgentReferralSnapshot"},
			{TaskLevelUpgrade, "LevelUpgrade"},
		}

		for _, tc := range testCases {
			var taskName string
			switch tc.taskType {
			case TaskAgentReferralSnapshot:
				taskName = "AgentReferralSnapshot"
			case TaskLevelUpgrade:
				taskName = "LevelUpgrade"
			default:
				taskName = "Unknown"
			}

			assert.Equal(t, tc.expectedName, taskName,
				"Task type %d should map to %s", tc.taskType, tc.expectedName)
		}
	})

	t.Run("constants can be used in maps", func(t *testing.T) {
		taskNames := map[int]string{
			TaskAgentReferralSnapshot: "Agent Referral Snapshot Task",
			TaskLevelUpgrade:          "Level Upgrade Task",
		}

		assert.Equal(t, "Agent Referral Snapshot Task", taskNames[TaskAgentReferralSnapshot])
		assert.Equal(t, "Level Upgrade Task", taskNames[TaskLevelUpgrade])
	})

	t.Run("constants can be used in slices", func(t *testing.T) {
		allTasks := []int{
			TaskAgentReferralSnapshot,
			TaskLevelUpgrade,
		}

		assert.Len(t, allTasks, 2)
		assert.Contains(t, allTasks, TaskAgentReferralSnapshot)
		assert.Contains(t, allTasks, TaskLevelUpgrade)
	})
}

func TestTaskConstantsType(t *testing.T) {
	t.Run("constants are of int type", func(t *testing.T) {
		assert.IsType(t, 0, TaskAgentReferralSnapshot)
		assert.IsType(t, 0, TaskLevelUpgrade)
	})
}

// Benchmark tests for constant access (should be extremely fast)
func BenchmarkTaskConstants(b *testing.B) {
	b.Run("access_TaskAgentReferralSnapshot", func(b *testing.B) {
		var result int
		for i := 0; i < b.N; i++ {
			result = TaskAgentReferralSnapshot
		}
		_ = result // Prevent optimization
	})

	b.Run("access_TaskLevelUpgrade", func(b *testing.B) {
		var result int
		for i := 0; i < b.N; i++ {
			result = TaskLevelUpgrade
		}
		_ = result // Prevent optimization
	})

	b.Run("switch_on_constants", func(b *testing.B) {
		tasks := []int{TaskAgentReferralSnapshot, TaskLevelUpgrade}
		for i := 0; i < b.N; i++ {
			task := tasks[i%len(tasks)]
			switch task {
			case TaskAgentReferralSnapshot:
				// Do nothing
			case TaskLevelUpgrade:
				// Do nothing
			}
		}
	})
}

// Example usage of constants
func ExampleTaskAgentReferralSnapshot() {
	// Using constants in a function
	processTask := func(taskType int) string {
		switch taskType {
		case TaskAgentReferralSnapshot:
			return "Processing agent referral snapshot"
		case TaskLevelUpgrade:
			return "Processing level upgrade"
		default:
			return "Unknown task type"
		}
	}

	_ = processTask(TaskAgentReferralSnapshot) // "Processing agent referral snapshot"
	_ = processTask(TaskLevelUpgrade)          // "Processing level upgrade"
}
