package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		{
			name:     "valid email",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "valid email with subdomain",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "valid email with numbers",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "valid email with special characters",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "empty email should be valid",
			email:    "",
			expected: true,
		},
		{
			name:     "invalid email without @",
			email:    "testexample.com",
			expected: false,
		},
		{
			name:     "invalid email without domain",
			email:    "test@",
			expected: false,
		},
		{
			name:     "invalid email without local part",
			email:    "@example.com",
			expected: false,
		},
		{
			name:     "invalid email with spaces",
			email:    "test @example.com",
			expected: false,
		},
		{
			name:     "invalid email with multiple @",
			email:    "test@@example.com",
			expected: false,
		},
		{
			name:     "invalid email without TLD",
			email:    "test@example",
			expected: false,
		},
		{
			name:     "invalid email with short TLD",
			email:    "test@example.c",
			expected: false,
		},
		{
			name:     "email with whitespace should be trimmed and validated",
			email:    "  <EMAIL>  ",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidEmail(tt.email)
			assert.Equal(t, tt.expected, result, "IsValidEmail(%q) = %v, want %v", tt.email, result, tt.expected)
		})
	}
}

func TestNormalizeEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected string
	}{
		{
			name:     "normal email",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "email with uppercase",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "email with leading/trailing spaces",
			email:    "  <EMAIL>  ",
			expected: "<EMAIL>",
		},
		{
			name:     "email with mixed case and spaces",
			email:    "  <EMAIL>  ",
			expected: "<EMAIL>",
		},
		{
			name:     "empty email",
			email:    "",
			expected: "",
		},
		{
			name:     "email with only spaces",
			email:    "   ",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizeEmail(tt.email)
			assert.Equal(t, tt.expected, result, "NormalizeEmail(%q) = %q, want %q", tt.email, result, tt.expected)
		})
	}
}

func TestStringToPointer(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *string
	}{
		{
			name:     "non-empty string",
			input:    "test",
			expected: func() *string { s := "test"; return &s }(),
		},
		{
			name:     "empty string",
			input:    "",
			expected: nil,
		},
		{
			name:     "string with spaces",
			input:    "  test  ",
			expected: func() *string { s := "  test  "; return &s }(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringToPointer(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result, "StringToPointer(%q) should return nil", tt.input)
			} else {
				assert.NotNil(t, result, "StringToPointer(%q) should not return nil", tt.input)
				assert.Equal(t, *tt.expected, *result, "StringToPointer(%q) = %q, want %q", tt.input, *result, *tt.expected)
			}
		})
	}
}

func TestPointerToString(t *testing.T) {
	tests := []struct {
		name     string
		input    *string
		expected string
	}{
		{
			name:     "non-nil pointer",
			input:    func() *string { s := "test"; return &s }(),
			expected: "test",
		},
		{
			name:     "nil pointer",
			input:    nil,
			expected: "",
		},
		{
			name:     "pointer to empty string",
			input:    func() *string { s := ""; return &s }(),
			expected: "",
		},
		{
			name:     "pointer to string with spaces",
			input:    func() *string { s := "  test  "; return &s }(),
			expected: "  test  ",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := PointerToString(tt.input)
			assert.Equal(t, tt.expected, result, "PointerToString(%v) = %q, want %q", tt.input, result, tt.expected)
		})
	}
}

func TestIsValidInvitationCode(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected bool
	}{
		{
			name:     "valid code with letters and numbers",
			code:     "ABC123",
			expected: true,
		},
		{
			name:     "valid code with underscore",
			code:     "USER_123",
			expected: true,
		},
		{
			name:     "valid code with dash",
			code:     "USER-123",
			expected: true,
		},
		{
			name:     "valid code with dot",
			code:     "USER.123",
			expected: true,
		},
		{
			name:     "valid code minimum length",
			code:     "ABC12",
			expected: true,
		},
		{
			name:     "valid code maximum length",
			code:     "ABCDEFGHIJKLMNO",
			expected: true,
		},
		{
			name:     "invalid code with space",
			code:     "ABC 123",
			expected: false,
		},
		{
			name:     "invalid code with tab",
			code:     "ABC\t123",
			expected: false,
		},
		{
			name:     "invalid code with newline",
			code:     "ABC\n123",
			expected: false,
		},
		{
			name:     "invalid code with leading space",
			code:     " ABC123",
			expected: false,
		},
		{
			name:     "invalid code with trailing space",
			code:     "ABC123 ",
			expected: false,
		},
		{
			name:     "invalid code too short",
			code:     "ABC1",
			expected: false,
		},
		{
			name:     "invalid code too long",
			code:     "ABCDEFGHIJKLMNOP",
			expected: false,
		},
		{
			name:     "valid code with special characters",
			code:     "ABC@123",
			expected: true,
		},
		{
			name:     "valid code with hash",
			code:     "ABC#123",
			expected: true,
		},
		{
			name:     "valid code with Chinese characters",
			code:     "用户123",
			expected: true,
		},
		{
			name:     "valid code with mixed Chinese and English",
			code:     "USER用户",
			expected: true,
		},
		{
			name:     "valid code with emoji",
			code:     "USER🎉123",
			expected: true,
		},
		{
			name:     "valid code with various symbols",
			code:     "A$B%C&D",
			expected: true,
		},
		{
			name:     "empty code",
			code:     "",
			expected: false,
		},
		{
			name:     "code with only spaces",
			code:     "     ",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidInvitationCode(tt.code)
			assert.Equal(t, tt.expected, result, "IsValidInvitationCode(%q) = %v, want %v", tt.code, result, tt.expected)
		})
	}
}

// Test the round-trip conversion
func TestStringPointerRoundTrip(t *testing.T) {
	tests := []string{
		"test",
		"",
		"  spaces  ",
		"special@chars#123",
	}

	for _, original := range tests {
		t.Run("round_trip_"+original, func(t *testing.T) {
			// Convert to pointer and back
			ptr := StringToPointer(original)
			result := PointerToString(ptr)

			if original == "" {
				// Empty string should become nil pointer, then back to empty string
				assert.Equal(t, "", result)
			} else {
				// Non-empty strings should survive the round trip
				assert.Equal(t, original, result)
			}
		})
	}
}
