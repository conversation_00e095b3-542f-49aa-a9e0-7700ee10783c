package utils

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAPIError(t *testing.T) {
	t.Run("create APIError", func(t *testing.T) {
		err := &APIError{
			Status:    http.StatusBadRequest,
			ErrorCode: "TEST_ERROR",
			Message:   "This is a test error",
		}

		assert.Equal(t, http.StatusBadRequest, err.Status)
		assert.Equal(t, "TEST_ERROR", err.ErrorCode)
		assert.Equal(t, "This is a test error", err.Message)
	})

	t.Run("APIError implements error interface", func(t *testing.T) {
		err := &APIError{
			Status:    http.StatusInternalServerError,
			ErrorCode: "INTERNAL_ERROR",
			Message:   "Internal server error occurred",
		}

		// Test that APIError implements the error interface
		var e error = err
		assert.Equal(t, "Internal server error occurred", e.Error())
	})

	t.Run("APIError Error method", func(t *testing.T) {
		err := &APIError{
			Status:    http.StatusNotFound,
			ErrorCode: "NOT_FOUND",
			Message:   "Resource not found",
		}

		assert.Equal(t, "Resource not found", err.Error())
	})
}

func TestErrAccessTokenInvalid(t *testing.T) {
	t.Run("predefined error has correct values", func(t *testing.T) {
		err := ErrAccessTokenInvalid

		assert.Equal(t, http.StatusUnauthorized, err.Status)
		assert.Equal(t, "ErrAccessTokenInvalid", err.ErrorCode)
		assert.Equal(t, "Access token invalid", err.Message)
	})

	t.Run("predefined error implements error interface", func(t *testing.T) {
		var e error = ErrAccessTokenInvalid
		assert.Equal(t, "Access token invalid", e.Error())
	})

	t.Run("predefined error is pointer type", func(t *testing.T) {
		// Ensure it's a pointer so we can compare with == in code
		assert.IsType(t, &APIError{}, ErrAccessTokenInvalid)
	})
}

func TestAPIErrorComparison(t *testing.T) {
	t.Run("same error instances are equal", func(t *testing.T) {
		err1 := ErrAccessTokenInvalid
		err2 := ErrAccessTokenInvalid

		assert.Equal(t, err1, err2)
		assert.True(t, err1 == err2) // Pointer comparison
	})

	t.Run("different error instances with same values are not equal by pointer", func(t *testing.T) {
		err1 := &APIError{
			Status:    http.StatusUnauthorized,
			ErrorCode: "ErrAccessTokenInvalid",
			Message:   "Access token invalid",
		}
		err2 := &APIError{
			Status:    http.StatusUnauthorized,
			ErrorCode: "ErrAccessTokenInvalid",
			Message:   "Access token invalid",
		}

		// Values are equal but pointers are different
		assert.Equal(t, err1.Status, err2.Status)
		assert.Equal(t, err1.ErrorCode, err2.ErrorCode)
		assert.Equal(t, err1.Message, err2.Message)
		assert.False(t, err1 == err2) // Different pointers
	})
}

func TestAPIErrorWithDifferentStatusCodes(t *testing.T) {
	tests := []struct {
		name       string
		status     int
		errorCode  string
		message    string
		statusName string
	}{
		{
			name:       "Bad Request",
			status:     http.StatusBadRequest,
			errorCode:  "BAD_REQUEST",
			message:    "Bad request",
			statusName: "400",
		},
		{
			name:       "Unauthorized",
			status:     http.StatusUnauthorized,
			errorCode:  "UNAUTHORIZED",
			message:    "Unauthorized access",
			statusName: "401",
		},
		{
			name:       "Forbidden",
			status:     http.StatusForbidden,
			errorCode:  "FORBIDDEN",
			message:    "Access forbidden",
			statusName: "403",
		},
		{
			name:       "Not Found",
			status:     http.StatusNotFound,
			errorCode:  "NOT_FOUND",
			message:    "Resource not found",
			statusName: "404",
		},
		{
			name:       "Internal Server Error",
			status:     http.StatusInternalServerError,
			errorCode:  "INTERNAL_ERROR",
			message:    "Internal server error",
			statusName: "500",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := &APIError{
				Status:    tt.status,
				ErrorCode: tt.errorCode,
				Message:   tt.message,
			}

			assert.Equal(t, tt.status, err.Status)
			assert.Equal(t, tt.errorCode, err.ErrorCode)
			assert.Equal(t, tt.message, err.Message)
			assert.Equal(t, tt.message, err.Error())
		})
	}
}

func TestAPIErrorZeroValues(t *testing.T) {
	t.Run("zero value APIError", func(t *testing.T) {
		err := &APIError{}

		assert.Equal(t, 0, err.Status)
		assert.Equal(t, "", err.ErrorCode)
		assert.Equal(t, "", err.Message)
		assert.Equal(t, "", err.Error())
	})
}

// Example of how APIError might be used in practice
func ExampleAPIError() {
	// Create a custom API error
	err := &APIError{
		Status:    http.StatusBadRequest,
		ErrorCode: "INVALID_INPUT",
		Message:   "The provided input is invalid",
	}

	// Use as regular error
	if err != nil {
		// Handle error
		_ = err.Error() // "The provided input is invalid"
	}

	// Access specific fields
	_ = err.Status    // 400
	_ = err.ErrorCode // "INVALID_INPUT"
}
