package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
)

// JWTClaims 定义JWT的声明结构
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	jwt.RegisteredClaims
}

func GenerateJWTToken(userID uuid.UUID, email string, jwtConfig config.JWT) (string, error) {
	expiresTime, err := time.ParseDuration(jwtConfig.ExpiresTime)
	if err != nil {
		return "", fmt.Errorf("invalid expires time: %w", err)
	}

	claims := JWTClaims{
		UserID: userID,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiresTime)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    jwtConfig.Issuer,
			Subject:   userID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	tokenString, err := token.SignedString([]byte(jwtConfig.SigningKey))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

func ValidateJWTToken(tokenString string, jwtConfig config.JWT) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jwtConfig.SigningKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}

func ExtractUserIDFromToken(tokenString string, jwtConfig config.JWT) (uuid.UUID, error) {
	claims, err := ValidateJWTToken(tokenString, jwtConfig)
	if err != nil {
		return uuid.Nil, err
	}
	return claims.UserID, nil
}

func ExtractEmailFromToken(tokenString string, jwtConfig config.JWT) (string, error) {
	claims, err := ValidateJWTToken(tokenString, jwtConfig)
	if err != nil {
		return "", err
	}
	return claims.Email, nil
}
