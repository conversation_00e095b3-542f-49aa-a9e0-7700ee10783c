package utils

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPathExists(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "test_path_exists")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create a test file
	testFile := filepath.Join(tempDir, "test_file.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err)

	// Create a test subdirectory
	testSubDir := filepath.Join(tempDir, "test_subdir")
	err = os.Mkdir(testSubDir, 0755)
	require.NoError(t, err)

	tests := []struct {
		name        string
		path        string
		expectExist bool
		expectError bool
		errorMsg    string
	}{
		{
			name:        "existing directory",
			path:        tempDir,
			expectExist: true,
			expectError: false,
		},
		{
			name:        "existing subdirectory",
			path:        testSubDir,
			expectExist: true,
			expectError: false,
		},
		{
			name:        "existing file should return error",
			path:        testFile,
			expectExist: false,
			expectError: true,
			errorMsg:    "file with same name exists",
		},
		{
			name:        "non-existing path",
			path:        filepath.Join(tempDir, "non_existing"),
			expectExist: false,
			expectError: false,
		},
		{
			name:        "non-existing nested path",
			path:        filepath.Join(tempDir, "non_existing", "nested"),
			expectExist: false,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			exists, err := PathExists(tt.path)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				assert.False(t, exists)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectExist, exists)
			}
		})
	}
}

func TestPathExistsWithInvalidPath(t *testing.T) {
	// Test with a path that would cause os.Stat to return an error other than os.IsNotExist
	// This is platform-dependent, but we can test with a very long path name
	longPath := string(make([]byte, 1000)) // Very long path that might cause issues
	for i := range longPath {
		longPath = longPath[:i] + "a" + longPath[i+1:]
	}

	exists, err := PathExists(longPath)

	// The exact behavior depends on the OS, but we expect either:
	// 1. false, nil (path doesn't exist)
	// 2. false, error (some other error occurred)
	assert.False(t, exists)
	// We don't assert on error because it's platform-dependent
	_ = err // Suppress unused variable warning
}

func TestPathExistsEdgeCases(t *testing.T) {
	t.Run("empty path", func(t *testing.T) {
		exists, err := PathExists("")
		// Empty path behavior is platform-dependent
		// On most systems, it should return false with no error or an error
		assert.False(t, exists)
		_ = err // Suppress unused variable warning
	})

	t.Run("root path", func(t *testing.T) {
		// Test with root path (should exist on Unix-like systems)
		exists, err := PathExists("/")
		if err == nil {
			// If no error, root should exist and be a directory
			assert.True(t, exists)
		}
		// If there's an error, we don't assert anything as it's platform-dependent
		_ = err // Suppress unused variable warning
	})

	t.Run("current directory", func(t *testing.T) {
		exists, err := PathExists(".")
		assert.NoError(t, err)
		assert.True(t, exists)
	})

	t.Run("parent directory", func(t *testing.T) {
		exists, err := PathExists("..")
		assert.NoError(t, err)
		assert.True(t, exists)
	})
}

// Benchmark tests
func BenchmarkPathExists(b *testing.B) {
	// Create a temporary directory for benchmarking
	tempDir, err := os.MkdirTemp("", "bench_path_exists")
	if err != nil {
		b.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	b.Run("existing_directory", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			PathExists(tempDir)
		}
	})

	b.Run("non_existing_path", func(b *testing.B) {
		nonExistingPath := filepath.Join(tempDir, "non_existing")
		for i := 0; i < b.N; i++ {
			PathExists(nonExistingPath)
		}
	})
}
