package utils

import (
	"os"
	"path"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"

	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// GetWriteSyncer 获取zapcore.WriteSyncer
func GetWriteSyncer(level string) zapcore.WriteSyncer {
	fileWriter := getFileLogWriter(level)
	if global.GVA_CONFIG.Zap.LogInConsole {
		return zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(fileWriter))
	}
	return zapcore.AddSync(fileWriter)
}

// getFileLogWriter 获取文件日志写入器
func getFileLogWriter(level string) *lumberjack.Logger {
	return &lumberjack.Logger{
		Filename:   path.Join(global.GVA_CONFIG.Zap.Director, level+".log"),
		MaxSize:    10,
		MaxBackups: 200,
		MaxAge:     30,
		Compress:   true,
	}
}
