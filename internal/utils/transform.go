package utils

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/jinzhu/copier"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"

	"strings"
)

func Translate[T any](from any) *T {
	var t T
	err := copier.Copy(&t, from)

	if err != nil {
		global.GVA_LOG.Error(err.<PERSON>r())
	}
	return &t
}

func TranslateByJSON[T any](from any) *T {
	var t T

	b, err := json.Marshal(from)
	if err != nil {
		global.GVA_LOG.Error("Marshal error: " + err.<PERSON>rror())
		return nil
	}

	err = json.Unmarshal(b, &t)
	if err != nil {
		global.GVA_LOG.Error("Unmarshal error: " + err.Error())
		return nil
	}

	return &t
}

func JsonDecode[T any](data interface{}, v *T) error {
	marshalData, err := json.<PERSON>(data)
	if err != nil {
		return err
	}

	err = json.Unmarshal(marshalData, v)
	if err != nil {
		return err
	}

	return nil
}

func ParseStringArray(arrayString string) []string {
	if arrayString == "[]" {
		return []string{} // Return an empty slice for "[]"
	}

	arrayString = strings.Trim(arrayString, "[]")
	elements := strings.Split(arrayString, ",")
	result := make([]string, 0, len(elements)) // Pre-allocate for efficiency

	for _, element := range elements {
		trimmedElement := strings.TrimSpace(element)
		// Remove quotes, if any
		trimmedElement = strings.ReplaceAll(trimmedElement, "'", "")
		trimmedElement = strings.ReplaceAll(trimmedElement, "\"", "")
		if trimmedElement != "" { // added check to prevent empty strings from being added
			result = append(result, trimmedElement)
		}
	}
	return result
}

func StringToFloat(s string) float64 {
	s = strings.TrimSpace(s)
	if s == "" {
		return 0
	}

	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		global.GVA_LOG.Warn("StringToFloat error: " + err.Error())
		return 0
	}

	return f
}

func StringToBool(s string) bool {
	s = strings.TrimSpace(strings.ToLower(s))
	if s == "true" || s == "1" {
		return true
	}

	return false
}

func StringToInt64(s string) int64 {
	s = strings.TrimSpace(s)

	if s == "" {
		return int64(0)
	}

	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		global.GVA_LOG.Warn("StringToInt64 error: " + err.Error())
		return int64(0)
	}

	return i
}

// TimeToTimestamp converts time.Time to int64 timestamp (milliseconds)
func TimeToTimestamp(t *time.Time) *int64 {
	if t == nil {
		return nil
	}
	timestamp := t.UnixMilli()
	return &timestamp
}

// TimestampToTime converts int64 timestamp (milliseconds) to time.Time
func TimestampToTime(timestamp *int64) *time.Time {
	if timestamp == nil {
		return nil
	}
	t := time.UnixMilli(*timestamp)
	return &t
}

// ValidateTimestamp validates if the timestamp is reasonable (not too far in past/future)
func ValidateTimestamp(timestamp *int64) bool {
	if timestamp == nil {
		return true // nil is valid
	}

	// Convert to time for validation
	t := time.UnixMilli(*timestamp)
	now := time.Now()

	// Check if timestamp is not too far in the past (before year 2000)
	minTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)
	if t.Before(minTime) {
		return false
	}

	// Check if timestamp is not too far in the future (more than 10 years from now)
	maxTime := now.AddDate(10, 0, 0)
	if t.After(maxTime) {
		return false
	}

	return true
}
