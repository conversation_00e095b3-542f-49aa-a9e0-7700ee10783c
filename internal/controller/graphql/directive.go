package graphql

import (
	"context"
	"errors"

	"github.com/99designs/gqlgen/graphql"
	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/middlewares"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
	// Allow introspection queries to bypass authentication
	isIntrospection := ctx.Value("isIntrospection")
	if isIntrospection != nil && isIntrospection.(bool) {
		return next(ctx)
	}

	user := ctx.Value("userId")
	if user == nil {
		return nil, utils.ErrAccessTokenInvalid
	}
	return next(ctx)
}

// AdminAuthDirective validates admin API key authentication
func AdminAuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
	// Allow introspection queries to bypass authentication
	isIntrospection := ctx.Value("isIntrospection")
	if isIntrospection != nil && isIntrospection.(bool) {
		return next(ctx)
	}

	// Check if admin context is set (from API key middleware)
	isAdmin := ctx.Value("isAdmin")
	if isAdmin == nil || !isAdmin.(bool) {
		// Fallback: check for API key in gin context
		ginCtx := ctx.Value(middlewares.GinContextKey)
		if ginCtx != nil {
			if ginContext, ok := ginCtx.(*gin.Context); ok {
				apiKey := ginContext.GetHeader("x-api-key")
				if apiKey == "" {
					apiKey = ginContext.GetHeader("X-API-Key")
				}

				if !middlewares.ValidateApiKey(apiKey) {
					return nil, errors.New("admin access required: invalid or missing API key")
				}
			} else {
				return nil, errors.New("admin access required: invalid context")
			}
		} else {
			return nil, errors.New("admin access required: missing authentication")
		}
	}

	return next(ctx)
}
