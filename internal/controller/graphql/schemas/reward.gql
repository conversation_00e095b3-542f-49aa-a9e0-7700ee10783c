type InvitationRecord {
  address: String!
  transactionVolume: Float!
  invitedWithdrawal: Float!
  date: String!
}

input InvitationRecordRequest {
  page: Int!
  pageSize: Int!
}

type InvitationRecordResponse {
  success: Boolean!
  message: String!
  data: [InvitationRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
}

# WithdrawalRecord represents a single withdrawal record
type WithdrawalRecord {
  hash: String!
  withdrawalReward: String!
  date: String!
}

input WithdrawalRecordRequest {
  page: Int!
  pageSize: Int!
}

# WithdrawalRecordResponse represents the response for withdrawal records query
type WithdrawalRecordResponse {
  data: [WithdrawalRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
  success: Boolean!
  message: String
}
