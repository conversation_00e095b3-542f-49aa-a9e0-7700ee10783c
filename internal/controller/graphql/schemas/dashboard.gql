# Dashboard service schema for agent management
type ReferralSnapshotFull {
    userId: ID!
    directCount: Int!
    totalDownlineCount: Int!
    totalVolumeUsd: Float!
    totalRewardsDistributed: Float!
    tradingUserCount: Int!

    # Personal Trading Stats
    totalPerpsVolumeUsd: Float!
    totalPerpsFees: Float!
    totalPerpsFeesPaid: Float!
    totalPerpsFeesUnpaid: Float!
    totalPerpsTradesCount: Int!

    totalMemeVolumeUsd: Float!
    totalMemeFees: Float!
    totalMemeFeesPaid: Float!
    totalMemeFeesUnpaid: Float!
    totalMemeTradesCount: Int!

    # Personal Rewards Stats
    totalCommissionEarnedUsd: Float!
    claimedCommissionUsd: Float!
    unclaimedCommissionUsd: Float!
    totalCashbackEarnedUsd: Float!
    claimedCashbackUsd: Float!
    unclaimedCashbackUsd: Float!

    # Upline Relationships
    l1UplineId: ID
    l2UplineId: ID
    l3UplineId: ID

    # Relationships
    user: User!
    l1Upline: User
    l2Upline: User
    l3Upline: User
}
# User invitation statistics
type UserInvitationStats {
    directReferralCount: Int!
    indirectReferralCount: Int!
    extendedReferralCount: Int!
    totalDownlineCount: Int!
    tradingUserCount: Int!
    totalVolumeUsd: Float!
}

# User reward data
type UserRewardData {
    referralSnapshot: ReferralSnapshotFull!
    totalCommissionEarned: Float!
    totalCashbackEarned: Float!
}

# Transaction statistics
type TransactionStats {
    memeVolumeUsd: Float!
    contractVolumeUsd: Float!
    totalVolumeUsd: Float!
    period: Period!
}

# Platform transaction statistics
type PlatformTransactionStats {
    totalMemeVolume: Float!
    totalContractVolume: Float!
    totalVolume: Float!
    memeUserCount: Int!
    contractUserCount: Int!
    period: Period!
}

# Time period
type Period {
    startDate: String!
    endDate: String!
}

# Pagination information
type Pagination {
    page: Int!
    pageSize: Int!
    total: Int!
    totalPages: Int!
}

# Referral records response
type ReferralRecordsResponse {
    referrals: [Referral!]!
    pagination: Pagination!
}



input DSTransactionDataInput {
    userId: ID!
    startDate: String!
    endDate: String!
}

input UserReferralRecordsInput {
    userId: ID!
    page: Int!
    pageSize: Int!
}

input AllInfiniteAgentsInput {
    page: Int! = 1
    pageSize: Int! = 20
}

