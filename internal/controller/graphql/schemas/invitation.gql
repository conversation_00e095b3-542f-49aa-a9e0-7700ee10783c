type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  l1UplineId: ID
  l2UplineId: ID
  l3UplineId: ID
  user: User!
  l1Upline: User
  l2Upline: User
  l3Upline: User
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserInvitationCodeInput {
  invitationCode: String!
  email: String
  chain: String
  name: String
  walletAddress: String
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

type CreateUserResponse {
  user: User!
  success: Boolean!
  message: String!
}

input CreateUserWithReferralInput {
  invitationCode: String!
}

type User {
  id: ID!
  email: String
  invitationCode: String
  createdAt: String!
  updatedAt: String!
  deletedAt: String
  agentLevelId: Int!
  agentLevel: AgentLevel!
  levelGracePeriodStartedAt: String
  levelUpgradedAt: String
  firstTransactionAt: String
  referrals: [Referral!]!
  referralSnapshot: ReferralSnapshot
  referredUsers: [Referral!]!
}

type AgentLevel {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: String!
  user: User!
  referrer: User
}

# Invitation list schema

type InvitationListItem {
  userAddress: String!
  invitationTime: Time!
  transactionType: String!
  transactionAmount: Float!
  accumulatedCommission: Float!
  date: String! # "06-09"
}

input InvitationListRequest {
  transactionType: TransactionType! # MEME, CONTRACT, SPOT, ALL
  page: Int!
  pageSize: Int!
}

enum TransactionType {
  ALL
  MEME
  CONTRACT
  SPOT
}

type InvitationListResponse {
  data: [InvitationListItem!]!
  total: Int!
  page: Int!
  pageSize: Int!
  success: Boolean!
  message: String
}

enum WithdrawalStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

# Invitation summary schema

type InvitationSummary {
  invitedUserCount: Int!
  tradingUserCount: Int!
}

type InvitationSummaryResponse {
  data: InvitationSummary
  success: Boolean!
  message: String
}