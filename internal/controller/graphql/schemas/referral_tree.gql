# Referral Tree Snapshot and Node management schema

type ReferralTreeSnapshot {
  id: ID!
  createdAt: Time!
  rootUserId: ID!
  snapshotDate: Time!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  infiniteAgentUserId: ID
  hasInfiniteAgent: Boolean!
  description: String
  isValid: Boolean!
  rootUser: User!
  infiniteAgentUser: User
  infiniteAgentConfig: InfiniteAgentConfig
}

type ReferralTreeNode {
  id: ID!
  createdAt: Time!
  treeSnapshotID: ID!
  userID: ID!
  parentUserID: ID
  referrerID: ID
  depth: Int!
  level: Int!
  position: Int!
  isActive: Boolean!
  isTrading: Boolean!
  agentLevelID: Int!
  treeSnapshot: ReferralTreeSnapshot!
  user: User!
  parentUser: User
  referrer: User
  agentLevel: AgentLevel!
}

type ReferralTreeSnapshotsResponse {
  referralTreeSnapshots: [ReferralTreeSnapshot!]!
  success: Boolean!
  message: String!
}

type ReferralTreeSnapshotResponse {
  referralTreeSnapshot: ReferralTreeSnapshot
  referralTreeNodes: [ReferralTreeNode!]!
  success: Boolean!
  message: String!
}
