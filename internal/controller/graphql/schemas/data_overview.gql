# Data Overview schema

type DataOverviewCategory {
  all: Float!
  meme: Float!
  contract: Float!
}

type DataOverview {
  rebateAmount: DataOverviewCategory!
  transactionVolume: DataOverviewCategory!
  invitationCount: DataOverviewCategory!
  timestamp: Time!
  period: String!
}

enum DataOverviewType {
  ALL
  MEME
  CONTRACT
}

enum DataOverviewTimeRange {
  TODAY
  LAST_30_DAYS
  LAST_60_DAYS
  ALL_TIME
}

input DataOverviewInput {
  timeRange: DataOverviewTimeRange!
}

type DataOverviewResponse {
  data: [DataOverview!]!
  success: Boolean!
  message: String
}

type DataOverviewSummary {
  totalRebateAmount: DataOverviewCategory!
  totalTransactionVolume: DataOverviewCategory!
  totalInvitationCount: DataOverviewCategory!
  peakRebateAmount: DataOverviewCategory!
  peakTransactionVolume: DataOverviewCategory!
  peakInvitationCount: DataOverviewCategory!
  averageRebateAmount: DataOverviewCategory!
  averageTransactionVolume: DataOverviewCategory!
  averageInvitationCount: DataOverviewCategory!
}

type DataOverviewWithSummary {
  data: [DataOverview!]!
  summary: DataOverviewSummary!
  success: Boolean!
  message: String
}
