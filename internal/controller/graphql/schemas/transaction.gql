# Transaction data schema

type TransactionData {
  transactionAmountUsd: Float!
  claimedUsd: Float!
  pendingClaimUsd: Float!
  invitationCount: Int!
  transactingUserCount: Int!
  contractVolumeUsd: Float!
  memeVolumeUsd: Float!
}

enum TransactionDataType {
  ALL
  MEME
  CONTRACT
}

enum TimeRangeType {
  TODAY
  LAST_30_DAYS
  LAST_60_DAYS
  ALL_TIME
}

input TransactionDataInput {
  dataType: TransactionDataType!
  timeRange: TimeRangeType!
}

type TransactionDataResponse {
  transactionData: [TransactionData!]!
  success: Boolean!
  message: String
}
