package middlewares

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

type Claims struct {
	Sub string `json:"sub"`
	jwt.RegisteredClaims
}

func GqlJwtAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Check if this is an introspection query (already set by ApiKeyAuth middleware)
		if ctx.Request.Context().Value("isIntrospection") != nil {
			ctx.Next()
			return
		}

		consumerName := ctx.GetHeader("X-Consumer-Username")
		if consumerName != "xbit" {
			ctx.Next()
			return
		}

		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			ctx.Next()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		claims := &Claims{}
		_, _, err := new(jwt.Parser).ParseUnverified(tokenString, claims)

		if err != nil {
			ctx.Next()
			return
		}

		wrappedCtx := context.WithValue(ctx.Request.Context(), "userId", claims.Sub)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}
