package resolvers

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/dashboard"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

type DashboardResolver struct {
	service dashboard.DashboardI
}

func NewDashboardResolver() *DashboardResolver {
	return &DashboardResolver{
		service: dashboard.NewDashboardService(),
	}
}

// UserReferralSnapshotFull is the resolver for the userReferralSnapshot field.
func (r *DashboardResolver) UserReferralSnapshotFull(ctx context.Context, userID string) (*gql_model.ReferralSnapshotFull, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	snapshot, err := r.service.GetUserReferralSnapshot(ctx, userUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user referral snapshot: %w", err)
	}

	return ModelReferralSnapshotFullToGQL(snapshot), nil
}

// AllAgentLevels is the resolver for the allAgentLevels field.
func (r *DashboardResolver) AllAgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levels, err := r.service.GetAllAgentLevels(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all agent levels: %w", err)
	}

	var gqlLevels []*gql_model.AgentLevel
	for _, level := range levels {
		gqlLevel := ModelAgentLevelToGQL(&level)
		gqlLevels = append(gqlLevels, gqlLevel)
	}

	return gqlLevels, nil
}

// UserTransactionStats is the resolver for the userTransactionStats field.
func (r *DashboardResolver) UserTransactionStats(ctx context.Context, userID, startDate, endDate string) (*gql_model.TransactionStats, error) {
	// Parse startDate and endDate from strings to int64 (assuming they are in milliseconds)
	startDateInt, err := strconv.ParseInt(startDate, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid startDate: %w", err)
	}
	endDateInt, err := strconv.ParseInt(endDate, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid endDate: %w", err)
	}

	// Convert millisecond timestamps to time.Time
	startDateT := time.UnixMilli(startDateInt)
	endDateT := time.UnixMilli(endDateInt)
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	stats, err := r.service.GetTransactionStats(ctx, userUUID, startDateT, endDateT)
	if err != nil {
		return nil, fmt.Errorf("failed to get user transaction stats: %w", err)
	}

	return &gql_model.TransactionStats{
		MemeVolumeUsd:     stats.MemeVolumeUSD.InexactFloat64(),
		ContractVolumeUsd: stats.ContractVolumeUSD.InexactFloat64(),
		TotalVolumeUsd:    stats.TotalVolumeUSD.InexactFloat64(),
		Period: &gql_model.Period{
			StartDate: fmt.Sprintf("%d", stats.Period.StartDate.UnixMilli()),
			EndDate:   fmt.Sprintf("%d", stats.Period.EndDate.UnixMilli()),
		},
	}, nil
}

// UserReferralRecords is the resolver for the userReferralRecords field.
func (r *DashboardResolver) UserReferralRecords(ctx context.Context, userID string, page int, pageSize int) (*gql_model.ReferralRecordsResponse, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	records, err := r.service.GetUserReferralRecords(ctx, userUUID, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("failed to get user referral records: %w", err)
	}

	var gqlReferrals []*gql_model.Referral
	for _, referral := range records.Referrals {
		gqlReferral := ModelReferralToGQL(&referral)
		gqlReferrals = append(gqlReferrals, gqlReferral)
	}

	return &gql_model.ReferralRecordsResponse{
		Referrals: gqlReferrals,
		Pagination: &gql_model.Pagination{
			Page:       records.Pagination.Page,
			PageSize:   records.Pagination.PageSize,
			Total:      records.Pagination.Total,
			TotalPages: records.Pagination.TotalPages,
		},
	}, nil
}

// AllInfiniteAgents is the resolver for the allInfiniteAgents field.
func (r *DashboardResolver) AllInfiniteAgents(ctx context.Context, page int, pageSize int) ([]*gql_model.InfiniteAgentConfig, error) {
	agents, err := r.service.GetAllInfiniteAgents(ctx, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("failed to get all infinite agents: %w", err)
	}

	var gqlAgents []*gql_model.InfiniteAgentConfig
	for _, agent := range agents {
		gqlAgent := ModelInfiniteAgentConfigToGQL(agent)
		gqlAgents = append(gqlAgents, gqlAgent)
	}

	return gqlAgents, nil
}

// InfiniteAgentByUserId is the resolver for the infiniteAgentByUserId field.
func (r *DashboardResolver) InfiniteAgentByUserId(ctx context.Context, userID string) (*gql_model.InfiniteAgentConfig, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	agent, err := r.service.GetInfiniteAgentByUserID(ctx, userUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent by user ID: %w", err)
	}

	return ModelInfiniteAgentConfigToGQL(agent), nil
}

func ModelReferralSnapshotFullToGQL(snapshot *model.ReferralSnapshot) *gql_model.ReferralSnapshotFull {
	return utils.Translate[gql_model.ReferralSnapshotFull](&snapshot)
}

// ModelReferralToGQL converts model.Referral to gql_model.Referral
func ModelReferralToGQL(referral *model.Referral) *gql_model.Referral {
	if referral == nil {
		return nil
	}

	gqlReferral := &gql_model.Referral{
		ID:        int(referral.ID),
		UserID:    referral.UserID.String(),
		Depth:     referral.Depth,
		CreatedAt: referral.CreatedAt.Format(time.RFC3339),
		User:      ModelUserToGQL(&referral.User),
	}

	if referral.ReferrerID != nil {
		r := referral.ReferrerID.String()
		gqlReferral.ReferrerID = &r
		if referral.Referrer != nil {
			gqlReferral.Referrer = ModelUserToGQL(referral.Referrer)
		}
	}

	return gqlReferral
}
