package resolvers

import (
	"context"
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

type InvitationResolver struct {
	s                       service.InvitationI
	v                       *validator.Validate
	invitationListService   *invitation.InvitationListService
	invitationRecordService *invitation.InvitationRecordService
}

func NewInvitationResolver() *InvitationResolver {
	return &InvitationResolver{
		s:                       agent_referral.NewAgentReferralService(),
		v:                       validator.New(),
		invitationListService:   invitation.NewInvitationListService().(*invitation.InvitationListService),
		invitationRecordService: invitation.NewInvitationRecordService(),
	}
}

func (i *InvitationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	// Extract user ID from JWT token instead of accepting it as input
	userId := GetUserIDFromContext(ctx)
	if userId == uuid.Nil {
		return nil, fmt.Errorf("User ID not found in JWT token")
	}

	referrer, err := i.s.GetUserByInvitationCode(ctx, input.InvitationCode)
	if err != nil || referrer == nil {
		return nil, fmt.Errorf("Invalid invitation code")
	}

	err = i.s.CreateUserWithReferral(ctx, referrer.ID, userId.String())
	if err != nil {
		return nil, fmt.Errorf("Failed to create user association: %v", err)
	}

	// Fetch the user to return in the response (required by schema)
	user, err := i.s.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("Failed to fetch user after creating referral: %v", err)
	}

	return &gql_model.CreateUserResponse{
		User:    utils.Translate[gql_model.User](user),
		Success: true,
		Message: "User created and bound referral relationship successfully",
	}, nil
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (i *InvitationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	userId := GetUserIDFromContext(ctx)
	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return nil, fmt.Errorf("Invalid wallet ID: %w", err)
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return nil, fmt.Errorf("Invalid wallet account ID: %w", err)
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}
	chain := ""
	if input.Chain != nil {
		chain = *input.Chain
	}
	walletAddress := ""
	if input.WalletAddress != nil {
		walletAddress = *input.WalletAddress
	}
	email := ""
	if input.Email != nil {
		email = *input.Email
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	user, err := i.s.UpdateUserInvitationCode(ctx, userId,
		chain, name, walletAddress, walletID, walletAccountID,
		walletType, input.InvitationCode, email)
	if err != nil {
		return nil, fmt.Errorf("failed to update user invitation code: %w", err)
	}

	return &gql_model.CreateUserResponse{
		User:    utils.Translate[gql_model.User](user),
		Success: true,
		Message: "User created successfully",
	}, nil
}

// User is the resolver for the user field.
func (i *InvitationResolver) User(ctx context.Context) (*gql_model.User, error) {
	userId := GetUserIDFromContext(ctx)
	user, err := i.s.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return utils.Translate[gql_model.User](user), nil
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (i *InvitationResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	userId := GetUserIDFromContext(ctx)
	snapshot, err := i.s.GetReferralSnapshot(ctx, userId)
	if err != nil {
		return ModelReferralSnapshotToGQL(&model.ReferralSnapshot{}), nil
	}

	return ModelReferralSnapshotToGQL(snapshot), nil
}

// InvitationList handles the invitation list query
func (r *InvitationResolver) InvitationList(ctx context.Context, input gql_model.InvitationListRequest) (*gql_model.InvitationListResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "User not logged in"
		return &gql_model.InvitationListResponse{
			Success: false,
			Message: &message,
		}, nil
	}

	// Convert GraphQL input to DTO request
	request := &response.InvitationListRequest{
		TransactionType: string(input.TransactionType),
		Page:            input.Page,
		PageSize:        input.PageSize,
	}

	// Get invitation list using the dedicated service
	listResponse, err := r.invitationListService.GetInvitationList(ctx, userID, request)
	if err != nil {
		message := fmt.Sprintf("Failed to get invitation list: %v", err)
		return &gql_model.InvitationListResponse{
			Success: false,
			Message: &message,
		}, nil
	}

	// Convert DTO items to GraphQL items
	var gqlItems []*gql_model.InvitationListItem
	for _, item := range listResponse.Data {
		gqlItem := &gql_model.InvitationListItem{
			UserAddress:           item.UserAddress,
			InvitationTime:        item.InvitationTime,
			TransactionType:       item.TransactionType,
			TransactionAmount:     item.TransactionAmount,
			AccumulatedCommission: item.AccumulatedCommission,
			Date:                  item.Date,
		}
		gqlItems = append(gqlItems, gqlItem)
	}

	// Create GraphQL response
	message := listResponse.Message
	response := &gql_model.InvitationListResponse{
		Data:     gqlItems,
		Total:    listResponse.Total,
		Page:     listResponse.Page,
		PageSize: listResponse.PageSize,
		Success:  listResponse.Success,
		Message:  &message,
	}

	return response, nil
}

// InvitationSummary handles the invitation summary query
func (r *InvitationResolver) InvitationSummary(ctx context.Context) (*gql_model.InvitationSummaryResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "User is not logged in"
		return &gql_model.InvitationSummaryResponse{
			Success: false,
			Message: &message,
		}, nil
	}

	// Get invited user count using the agent referral service
	invitedUserCount, err := r.s.GetExtendedInvitedUserCount(ctx, userID)
	if err != nil {
		message := fmt.Sprintf("Failed to obtain invited user count: %v", err)
		return &gql_model.InvitationSummaryResponse{
			Success: false,
			Message: &message,
		}, nil
	}

	// Get trading user count using the agent referral service
	tradingUserCount, err := r.s.GetTradingUserCount(ctx, userID, nil, nil)
	if err != nil {
		message := fmt.Sprintf("Failed to obtain trading user count: %v", err)
		return &gql_model.InvitationSummaryResponse{
			Success: false,
			Message: &message,
		}, nil
	}

	// Convert to GraphQL response
	message := "Successfully obtained the invitation summary"
	response := &gql_model.InvitationSummaryResponse{
		Data: &gql_model.InvitationSummary{
			InvitedUserCount: invitedUserCount,
			TradingUserCount: tradingUserCount,
		},
		Success: true,
		Message: &message,
	}

	return response, nil
}

func GetUserIDFromContext(ctx context.Context) uuid.UUID {
	userIdStr, _ := ctx.Value("userId").(string)
	userId := uuid.Nil
	if userIdStr != "" {
		userId, _ = uuid.Parse(userIdStr)
	}
	return userId
}

func GetUserEmailFromContext(ctx context.Context) string {
	email, _ := ctx.Value("userEmail").(string)
	return email
}

func GQLWalletTypeToString(walletType gql_model.WalletType) string {
	switch walletType {
	case gql_model.WalletTypeEmbedded:
		return "embedded"
	case gql_model.WalletTypeManaged:
		return "managed"
	default:
		return ""
	}
}

func ModelReferralSnapshotToGQL(snapshot *model.ReferralSnapshot) *gql_model.ReferralSnapshot {
	if snapshot == nil {
		return nil
	}

	totalVolumeUsd, _ := snapshot.TotalVolumeUSD.Float64()
	totalRewardsDistributed, _ := snapshot.TotalRewardsDistributed.Float64()

	return &gql_model.ReferralSnapshot{
		UserID:                  snapshot.UserID.String(),
		DirectCount:             snapshot.DirectCount,
		TotalDownlineCount:      snapshot.TotalDownlineCount,
		TotalVolumeUsd:          totalVolumeUsd,
		TotalRewardsDistributed: totalRewardsDistributed,
		User:                    utils.Translate[gql_model.User](&snapshot.User),
	}
}
