package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/level"
)

type LevelResolver struct {
	s service.LevelI
}

func NewLevelResolver() *LevelResolver {
	return &LevelResolver{
		s: level.NewLevelService(),
	}
}

// AgentLevels is the resolver for the agentLevels field.
func (l *LevelResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levels, err := l.s.GetAgentLevels(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}

	var gqlLevels []*gql_model.AgentLevel
	for _, level := range levels {
		gqlLevel := ModelAgentLevelToGQL(&level)
		gqlLevels = append(gqlLevels, gqlLevel)
	}

	return gqlLevels, nil
}

// AgentLevel is the resolver for the agentLevel field.
func (l *LevelResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	level, err := l.s.GetAgentLevelByID(ctx, uint(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level: %w", err)
	}

	return ModelAgentLevelToGQL(level), nil
}

// UserLevelInfo is the resolver for the userLevelInfo field.
func (l *LevelResolver) UserLevelInfo(ctx context.Context) (*gql_model.UserLevelInfoResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.UserLevelInfoResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	userLevelInfo, err := l.s.GetUserLevelInfo(ctx, userID)
	if err != nil {
		return &gql_model.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get user level info: %v", err),
		}, nil
	}

	// Convert to GraphQL model
	gqlUserLevelInfo := &gql_model.UserLevelInfo{
		CurrentLevel:              ModelAgentLevelToGQL(userLevelInfo.Data.CurrentLevel),
		MemeVolume:                userLevelInfo.Data.MemeVolume,
		ContractVolume:            userLevelInfo.Data.ContractVolume,
		TotalVolume:               userLevelInfo.Data.TotalVolume,
	}

	return &gql_model.UserLevelInfoResponse{
		Success: userLevelInfo.Success,
		Message: userLevelInfo.Message,
		Data:    gqlUserLevelInfo,
	}, nil
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (l *LevelResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	// Validate levelId - only accept 6 and 7
	var levelID uint
	switch input.LevelID {
	case "6":
		levelID = 6
	case "7":
		levelID = 7
	default:
		return &gql_model.UpdateLevelCommissionResponse{
			Success: false,
			Message: "Invalid levelId: only values 6 and 7 are allowed",
			Level:   nil,
		}, nil
	}

	level, err := l.s.UpdateLevelCommission(ctx, levelID, input.DirectCommissionRate, input.IndirectCommissionRate, input.ExtendedCommissionRate, input.MemeFeeRebate)
	if err != nil {
		return &gql_model.UpdateLevelCommissionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update level commission: %v", err),
			Level:   nil,
		}, nil
	}

	return &gql_model.UpdateLevelCommissionResponse{
		Level:   ModelAgentLevelToGQL(level),
		Success: true,
		Message: "Level commission updated successfully",
	}, nil
}

// ModelAgentLevelToGQL converts model.AgentLevel to gql_model.AgentLevel
func ModelAgentLevelToGQL(level *model.AgentLevel) *gql_model.AgentLevel {
	if level == nil {
		return nil
	}

	memeVolumeThreshold, _ := level.MemeVolumeThreshold.Float64()
	contractVolumeThreshold, _ := level.ContractVolumeThreshold.Float64()
	memeFeeRate, _ := level.MemeFeeRate.Float64()
	takerFeeRate, _ := level.TakerFeeRate.Float64()
	makerFeeRate, _ := level.MakerFeeRate.Float64()
	directCommissionRate, _ := level.DirectCommissionRate.Float64()
	indirectCommissionRate, _ := level.IndirectCommissionRate.Float64()
	extendedCommissionRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	return &gql_model.AgentLevel{
		ID:                      int(level.ID),
		Name:                    level.Name,
		MemeVolumeThreshold:     memeVolumeThreshold,
		ContractVolumeThreshold: contractVolumeThreshold,
		MemeFeeRate:             memeFeeRate,
		TakerFeeRate:            takerFeeRate,
		MakerFeeRate:            makerFeeRate,
		DirectCommissionRate:    directCommissionRate,
		IndirectCommissionRate:  indirectCommissionRate,
		ExtendedCommissionRate:  extendedCommissionRate,
		MemeFeeRebate:           memeFeeRebate,
	}
}
