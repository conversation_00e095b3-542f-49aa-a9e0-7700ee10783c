package resolvers

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ReferralTreeResolver struct{}

func NewReferralTreeResolver() *ReferralTreeResolver {
	return &ReferralTreeResolver{}
}

// ReferralTreeSnapshots is the resolver for the referralTreeSnapshots field.
func (r *ReferralTreeResolver) ReferralTreeSnapshots(ctx context.Context) (*gql_model.ReferralTreeSnapshotsResponse, error) {
	var snapshots []model.ReferralTreeSnapshot

	err := global.GVA_DB.WithContext(ctx).
		Preload("RootUser").
		Preload("InfiniteAgentUser").
		Preload("InfiniteAgentConfig").
		Where("is_valid = ?", true).
		Find(&snapshots).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get referral tree snapshots", zap.Error(err))
		return &gql_model.ReferralTreeSnapshotsResponse{
			ReferralTreeSnapshots: []*gql_model.ReferralTreeSnapshot{},
			Success:               false,
			Message:               fmt.Sprintf("Failed to get referral tree snapshots: %v", err),
		}, nil
	}

	var gqlSnapshots []*gql_model.ReferralTreeSnapshot
	for _, snapshot := range snapshots {
		gqlSnapshot := ModelReferralTreeSnapshotToGQL(&snapshot)
		gqlSnapshots = append(gqlSnapshots, gqlSnapshot)
	}

	return &gql_model.ReferralTreeSnapshotsResponse{
		ReferralTreeSnapshots: gqlSnapshots,
		Success:               true,
		Message:               "Successfully retrieved referral tree snapshots",
	}, nil
}

// ReferralTreeSnapshot is the resolver for the referralTreeSnapshot field.
func (r *ReferralTreeResolver) ReferralTreeSnapshot(ctx context.Context, id string) (*gql_model.ReferralTreeSnapshotResponse, error) {
	var snapshot model.ReferralTreeSnapshot
	err := global.GVA_DB.WithContext(ctx).
		Preload("RootUser").
		Preload("InfiniteAgentUser").
		Preload("InfiniteAgentConfig").
		Where("id = ? AND is_valid = ?", id, true).
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &gql_model.ReferralTreeSnapshotResponse{
				ReferralTreeSnapshot: nil,
				ReferralTreeNodes:    []*gql_model.ReferralTreeNode{},
				Success:              false,
				Message:              "Referral tree snapshot not found",
			}, nil
		}
		global.GVA_LOG.Error("Failed to get referral tree snapshot", zap.String("id", id), zap.Error(err))
		return &gql_model.ReferralTreeSnapshotResponse{
			ReferralTreeSnapshot: nil,
			ReferralTreeNodes:    []*gql_model.ReferralTreeNode{},
			Success:              false,
			Message:              fmt.Sprintf("Failed to get referral tree snapshot: %v", err),
		}, nil
	}

	// Get tree nodes for this snapshot
	var nodes []model.ReferralTreeNode
	err = global.GVA_DB.WithContext(ctx).
		Preload("TreeSnapshot").
		Preload("User").
		Preload("ParentUser").
		Preload("Referrer").
		Preload("AgentLevel").
		Where("tree_snapshot_id = ?", snapshot.ID).
		Order("level, position").
		Find(&nodes).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get referral tree nodes", zap.Uint("snapshot_id", snapshot.ID), zap.Error(err))
		return &gql_model.ReferralTreeSnapshotResponse{
			ReferralTreeSnapshot: ModelReferralTreeSnapshotToGQL(&snapshot),
			ReferralTreeNodes:    []*gql_model.ReferralTreeNode{},
			Success:              false,
			Message:              fmt.Sprintf("Failed to get referral tree nodes: %v", err),
		}, nil
	}

	var gqlNodes []*gql_model.ReferralTreeNode
	for _, node := range nodes {
		gqlNode := ModelReferralTreeNodeToGQL(&node)
		gqlNodes = append(gqlNodes, gqlNode)
	}

	return &gql_model.ReferralTreeSnapshotResponse{
		ReferralTreeSnapshot: ModelReferralTreeSnapshotToGQL(&snapshot),
		ReferralTreeNodes:    gqlNodes,
		Success:              true,
		Message:              "Successfully retrieved referral tree snapshot and nodes",
	}, nil
}

// ModelReferralTreeSnapshotToGQL converts model.ReferralTreeSnapshot to gql_model.ReferralTreeSnapshot
func ModelReferralTreeSnapshotToGQL(snapshot *model.ReferralTreeSnapshot) *gql_model.ReferralTreeSnapshot {
	if snapshot == nil {
		return nil
	}

	gqlSnapshot := &gql_model.ReferralTreeSnapshot{
		ID:               fmt.Sprintf("%d", snapshot.ID),
		CreatedAt:        snapshot.CreatedAt,
		RootUserID:       snapshot.RootUserID.String(),
		SnapshotDate:     snapshot.SnapshotDate,
		TotalNodes:       snapshot.TotalNodes,
		MaxDepth:         snapshot.MaxDepth,
		DirectCount:      snapshot.DirectCount,
		ActiveUsers:      snapshot.ActiveUsers,
		TradingUsers:     snapshot.TradingUsers,
		HasInfiniteAgent: snapshot.HasInfiniteAgent,
		Description:      &snapshot.Description,
		IsValid:          snapshot.IsValid,
		RootUser:         utils.Translate[gql_model.User](&snapshot.RootUser),
	}

	if snapshot.InfiniteAgentUserID != nil {
		infiniteAgentUserID := snapshot.InfiniteAgentUserID.String()
		gqlSnapshot.InfiniteAgentUserID = &infiniteAgentUserID
	}

	if snapshot.InfiniteAgentUser != nil {
		gqlSnapshot.InfiniteAgentUser = utils.Translate[gql_model.User](snapshot.InfiniteAgentUser)
	}

	if snapshot.InfiniteAgentConfig != nil {
		gqlSnapshot.InfiniteAgentConfig = utils.Translate[gql_model.InfiniteAgentConfig](snapshot.InfiniteAgentConfig)
	}

	return gqlSnapshot
}

// ModelReferralTreeNodeToGQL converts model.ReferralTreeNode to gql_model.ReferralTreeNode
func ModelReferralTreeNodeToGQL(node *model.ReferralTreeNode) *gql_model.ReferralTreeNode {
	if node == nil {
		return nil
	}

	gqlNode := &gql_model.ReferralTreeNode{
		ID:             fmt.Sprintf("%d", node.ID),
		CreatedAt:      node.CreatedAt,
		TreeSnapshotID: fmt.Sprintf("%d", node.TreeSnapshotID),
		UserID:         node.UserID.String(),
		Depth:          node.Depth,
		Level:          node.Level,
		Position:       node.Position,
		IsActive:       node.IsActive,
		IsTrading:      node.IsTrading,
		AgentLevelID:   int(node.AgentLevelID),
		TreeSnapshot:   ModelReferralTreeSnapshotToGQL(&node.TreeSnapshot),
		User:           utils.Translate[gql_model.User](&node.User),
		AgentLevel:     utils.Translate[gql_model.AgentLevel](&node.AgentLevel),
	}

	if node.ParentUserID != nil {
		parentUserID := node.ParentUserID.String()
		gqlNode.ParentUserID = &parentUserID
	}

	if node.ReferrerID != nil {
		referrerID := node.ReferrerID.String()
		gqlNode.ReferrerID = &referrerID
	}

	if node.ParentUser != nil {
		gqlNode.ParentUser = utils.Translate[gql_model.User](node.ParentUser)
	}

	if node.Referrer != nil {
		gqlNode.Referrer = utils.Translate[gql_model.User](node.Referrer)
	}

	return gqlNode
}

// CreateReferralTreeSnapshot is the resolver for the createReferralTreeSnapshot field.
func (r *ReferralTreeResolver) CreateReferralTreeSnapshot(ctx context.Context) (*gql_model.CreateReferralTreeSnapshotResponse, error) {
	global.GVA_LOG.Info("Starting manual referral tree snapshot creation")

	// Import the task package
	task := infinite.NewReferralTreeSnapshotTask()

	// Call the public method that processes all snapshots
	// This method will query all root users (depth = 1 referrals) and create snapshots
	task.CreateReferralTreeSnapshots()

	// Since we can't directly access the results from the public method,
	// we'll return a success response indicating the task was triggered
	return &gql_model.CreateReferralTreeSnapshotResponse{
		Success: true,
		Message: "Successfully triggered referral tree snapshot creation for all root users",
	}, nil
}
