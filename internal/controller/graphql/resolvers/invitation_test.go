package resolvers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// Use the same key as the actual resolver
const userIDKey = "userId"

// MockInvitationService is a mock implementation of service.InvitationI
type MockInvitationService struct {
	mock.Mock
}

func (m *MockInvitationService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockInvitationService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	args := m.Called(ctx, userID, chain, name, walletAddress, walletID, walletAccountID, walletType, invitationCode, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	args := m.Called(ctx, referrerID, userID)
	return args.Error(0)
}

func (m *MockInvitationService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	args := m.Called(ctx, invitationCode)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationService) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationService) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationService) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockInvitationService) GetActivityTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockInvitationService) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockInvitationService) CreateUserWallet(ctx context.Context, userID uuid.UUID, chain model.ChainType, walletAddress string, walletID *uuid.UUID, walletAccountID *uuid.UUID) (*model.UserWallet, error) {
	args := m.Called(ctx, userID, chain, walletAddress, walletID, walletAccountID)
	return args.Get(0).(*model.UserWallet), args.Error(1)
}

func TestInvitationResolver_User(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		userID        uuid.UUID
		mockSetup     func(*MockInvitationService)
		expectedUser  *model.User
		expectedError error
	}{
		{
			name:   "successful user retrieval",
			userID: uuid.New(),
			mockSetup: func(mockService *MockInvitationService) {
				user := fixtures.CreateTestUser()
				mockService.On("GetUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(user, nil)
			},
			expectedUser:  fixtures.CreateTestUser(),
			expectedError: nil,
		},
		{
			name:   "user not found",
			userID: uuid.New(),
			mockSetup: func(mockService *MockInvitationService) {
				mockService.On("GetUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return((*model.User)(nil), errors.New("user not found"))
			},
			expectedUser:  nil,
			expectedError: errors.New("failed to get user: user not found"),
		},
		{
			name:   "service error",
			userID: uuid.New(),
			mockSetup: func(mockService *MockInvitationService) {
				mockService.On("GetUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return((*model.User)(nil), errors.New("database error"))
			},
			expectedUser:  nil,
			expectedError: errors.New("failed to get user: database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := &MockInvitationService{}
			tt.mockSetup(mockService)

			// Create resolver with mock service
			resolver := &InvitationResolver{
				s: mockService,
			}

			// Create context with user ID
			ctx := context.WithValue(context.Background(), userIDKey, tt.userID.String())

			// Execute test
			result, err := resolver.User(ctx)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertContains(err.Error(), "failed to get user")
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				// Note: We can't easily compare the full result due to the Translate function
				// but we can verify that we got a non-nil result
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestInvitationResolver_ReferralSnapshot(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name             string
		userID           uuid.UUID
		mockSetup        func(*MockInvitationService)
		expectedSnapshot *model.ReferralSnapshot
		expectedError    error
	}{
		{
			name:   "successful snapshot retrieval",
			userID: uuid.New(),
			mockSetup: func(mockService *MockInvitationService) {
				snapshot := fixtures.CreateTestReferralSnapshot(uuid.New())
				mockService.On("GetReferralSnapshot", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(snapshot, nil)
			},
			expectedSnapshot: fixtures.CreateTestReferralSnapshot(uuid.New()),
			expectedError:    nil,
		},
		{
			name:   "snapshot not found - returns empty snapshot",
			userID: uuid.New(),
			mockSetup: func(mockService *MockInvitationService) {
				mockService.On("GetReferralSnapshot", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return((*model.ReferralSnapshot)(nil), errors.New("snapshot not found"))
			},
			expectedSnapshot: &model.ReferralSnapshot{}, // Should return empty snapshot on error
			expectedError:    nil,                       // Error is handled internally
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := &MockInvitationService{}
			tt.mockSetup(mockService)

			// Create resolver with mock service
			resolver := &InvitationResolver{
				s: mockService,
			}

			// Create context with user ID
			ctx := context.WithValue(context.Background(), userIDKey, tt.userID.String())

			// Execute test
			result, err := resolver.ReferralSnapshot(ctx)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				// The resolver always returns a non-nil result due to ModelReferralSnapshotToGQL
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}
