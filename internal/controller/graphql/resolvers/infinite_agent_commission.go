package resolvers

import (
	"context"
	"fmt"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"go.uber.org/zap"
)

type InfiniteAgentCommissionResolver struct{}

func NewInfiniteAgentCommissionResolver() *InfiniteAgentCommissionResolver {
	return &InfiniteAgentCommissionResolver{}
}

// CreateInfiniteAgentCommission is the resolver for the createInfiniteAgentCommission field.
func (r *InfiniteAgentCommissionResolver) CreateInfiniteAgentCommission(ctx context.Context) (*gql_model.CreateInfiniteAgentCommissionResponse, error) {
	global.GVA_LOG.Info("开始通过 GraphQL API 手动创建无限代理收益计算")

	// 获取当前时间作为计算日期
	calculationDate := time.Now().UTC()
	calculationDateStr := calculationDate.Format("2006-01-02 15:04:05")

	global.GVA_LOG.Info("计算日期", zap.String("date", calculationDateStr))

	// 创建任务实例
	task := infinite.NewInfiniteAgentCommissionTask()

	// 获取所有活跃的无限代理配置
	activeInfiniteAgents, err := task.GetActiveInfiniteAgentConfigs()
	if err != nil {
		global.GVA_LOG.Error("获取活跃无限代理失败", zap.Error(err))
		return &gql_model.CreateInfiniteAgentCommissionResponse{
			Success:             false,
			Message:             fmt.Sprintf("获取活跃无限代理失败: %v", err),
			ProcessedCount:      0,
			ErrorCount:          0,
			TotalInfiniteAgents: 0,
			CalculationDate:     calculationDateStr,
		}, nil
	}

	global.GVA_LOG.Info("找到活跃无限代理", zap.Int("infinite_agent_count", len(activeInfiniteAgents)))

	processedCount := 0
	errorCount := 0

	// 处理每个无限代理的收益计算
	for _, infiniteAgent := range activeInfiniteAgents {
		if err := task.ProcessInfiniteAgentCommission(infiniteAgent); err != nil {
			global.GVA_LOG.Error("处理无限代理收益计算失败",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("无限代理收益计算完成",
		zap.String("date", calculationDateStr),
		zap.Int("total_infinite_agents", len(activeInfiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))

	return &gql_model.CreateInfiniteAgentCommissionResponse{
		Success:             true,
		Message:             "成功完成无限代理收益计算",
		ProcessedCount:      processedCount,
		ErrorCount:          errorCount,
		TotalInfiniteAgents: len(activeInfiniteAgents),
		CalculationDate:     calculationDateStr,
	}, nil
}
