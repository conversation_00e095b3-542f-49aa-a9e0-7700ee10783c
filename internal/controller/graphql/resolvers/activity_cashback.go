package resolvers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

// ActivityCashbackResolver handles activity cashback related GraphQL operations
type ActivityCashbackResolver struct {
	service      activity_cashback.ActivityCashbackServiceInterface
	adminService activity_cashback.AdminServiceInterface
}

// NewActivityCashbackResolver creates a new ActivityCashbackResolver
func NewActivityCashbackResolver() *ActivityCashbackResolver {
	return &ActivityCashbackResolver{
		service:      activity_cashback.NewActivityCashbackService(),
		adminService: activity_cashback.NewAdminService(),
	}
}

// ActivityCashbackDashboard retrieves user dashboard for activity cashback
func (r *ActivityCashbackResolver) ActivityCashbackDashboard(ctx context.Context) (*gql_model.UserDashboardResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Initialize activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Initialize user if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to initialize user data",
		}, nil
	}

	// Get dashboard data
	dashboard, err := service.GetUserDashboard(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user dashboard", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to retrieve dashboard data",
		}, nil
	}

	// Convert to GraphQL model
	gqlDashboard := convertUserDashboardToGQL(dashboard)

	return &gql_model.UserDashboardResponse{
		Success: true,
		Message: "Dashboard data retrieved successfully",
		Data:    gqlDashboard,
	}, nil
}

// TaskCenter retrieves task center data
func (r *ActivityCashbackResolver) TaskCenter(ctx context.Context) (*gql_model.TaskCenterResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	taskCenter, err := service.GetTaskCenter(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: "Failed to retrieve task center data",
		}, nil
	}

	// Convert to GraphQL model
	gqlTaskCenter := convertTaskCenterToGQL(taskCenter)

	return &gql_model.TaskCenterResponse{
		Success: true,
		Message: "Task center data retrieved successfully",
		Data:    gqlTaskCenter,
	}, nil
}

// ActivityCashbackSummary retrieves optimized summary data for frontend UI
func (r *ActivityCashbackResolver) ActivityCashbackSummary(ctx context.Context) (*gql_model.ActivityCashbackSummaryResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Initialize activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Initialize user if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.ActivityCashbackSummaryResponse{
			Success: false,
			Message: "Failed to initialize user data",
		}, nil
	}

	// Get summary data
	summary, err := service.GetActivityCashbackSummary(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get activity cashback summary", zap.Error(err))
		return &gql_model.ActivityCashbackSummaryResponse{
			Success: false,
			Message: "Failed to retrieve summary data",
		}, nil
	}

	// Convert to GraphQL model
	gqlSummary := convertActivityCashbackSummaryToGQL(summary)

	return &gql_model.ActivityCashbackSummaryResponse{
		Success: true,
		Message: "Summary data retrieved successfully",
		Data:    gqlSummary,
	}, nil
}

// CompleteTask completes a task for the user
func (r *ActivityCashbackResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	taskUUID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Parse verification data
	var verificationData map[string]interface{}
	if input.VerificationData != nil {
		if err := json.Unmarshal([]byte(*input.VerificationData), &verificationData); err != nil {
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: "Invalid verification data format",
			}, nil
		}
	}

	// Get task to determine if it's a community task that requires pending
	task, err := service.GetTaskByID(ctx, taskUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get task: %s", err.Error()),
		}, nil
	}

	// Check if this is a community task that requires 2-minute wait
	// Use category-based detection for better maintainability
	isCommunityTaskWithWait := task.Category.Name == model.CategoryCommunity &&
		task.TaskIdentifier != nil &&
		model.RequiresTwoMinuteWait(*task.TaskIdentifier)

	if isCommunityTaskWithWait {
		// Check if user already has a pending task
		hasPending, err := service.HasPendingCommunityTask(ctx, userUUID, taskUUID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to check pending task: %s", err.Error()),
			}, nil
		}

		if hasPending {
			// Get existing pending task to return remaining time
			pendingTask, err := service.GetPendingCommunityTask(ctx, userUUID, taskUUID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get pending community task", zap.Error(err))
			}

			remainingTime := int(120) // Default 2 minutes
			var completionTime *time.Time
			if pendingTask != nil {
				remainingTime = int(pendingTask.GetRemainingWaitTime())
				completionTime = pendingTask.CompletionTime
			}

			return &gql_model.TaskCompletionResponse{
				Success:                  true,
				Message:                  "Task is already pending. Please wait for completion.",
				PointsAwarded:            0,
				TierUpgraded:             false,
				IsPending:                true,
				RemainingWaitTimeSeconds: &remainingTime,
				CompletionTime:           completionTime,
			}, nil
		}

		// Create pending community task
		pendingTask, err := service.CreatePendingCommunityTask(ctx, userUUID, taskUUID, verificationData)
		if err != nil {
			global.GVA_LOG.Error("Failed to create pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to create pending task: %s", err.Error()),
			}, nil
		}

		remainingTime := int(pendingTask.GetRemainingWaitTime())
		return &gql_model.TaskCompletionResponse{
			Success:                  true,
			Message:                  "Task started! Please wait 2 minutes for completion and points to be awarded.",
			PointsAwarded:            0, // Points will be awarded later
			TierUpgraded:             false,
			IsPending:                true,
			RemainingWaitTimeSeconds: &remainingTime,
			CompletionTime:           pendingTask.CompletionTime,
		}, nil
	}

	// For non-community tasks, proceed with normal completion
	// Get user tier info before completion
	tierInfoBefore, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info before task completion", zap.Error(err))
	}

	// Complete the task
	if err := service.CompleteTask(ctx, userUUID, taskUUID, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to complete task", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to complete task: %s", err.Error()),
		}, nil
	}

	pointsAwarded := task.Points

	// Check if tier was upgraded
	tierUpgraded := false
	newTierLevel := 0
	if tierInfoBefore != nil {
		tierInfoAfter, err := service.GetUserTierInfo(ctx, userUUID)
		if err == nil && tierInfoAfter.CurrentTier > tierInfoBefore.CurrentTier {
			tierUpgraded = true
			newTierLevel = tierInfoAfter.CurrentTier
		}
	}

	return &gql_model.TaskCompletionResponse{
		Success:                  true,
		Message:                  "Task completed successfully",
		PointsAwarded:            pointsAwarded,
		NewTierLevel:             &newTierLevel,
		TierUpgraded:             tierUpgraded,
		IsPending:                false,
		RemainingWaitTimeSeconds: nil,
		CompletionTime:           nil,
	}, nil
}

// ClaimTaskReward claims reward for a completed task
func (r *ActivityCashbackResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskClaimResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	taskUUID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Get task progress to determine points
	progress, err := service.GetTaskProgress(ctx, userUUID, taskUUID)
	if err != nil {
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: "Task progress not found",
		}, nil
	}

	if err := service.ClaimTaskReward(ctx, userUUID, taskUUID); err != nil {
		global.GVA_LOG.Error("Failed to claim task reward", zap.Error(err))
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to claim reward: %s", err.Error()),
		}, nil
	}

	return &gql_model.TaskClaimResponse{
		Success:       true,
		Message:       "Task reward claimed successfully",
		PointsClaimed: progress.PointsEarned,
	}, nil
}

// ClaimCashback claims cashback for the user
func (r *ActivityCashbackResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	amountUSD := decimal.NewFromFloat(input.AmountUsd)

	// For now, assume 1 SOL = 100 USD (this should come from price service)
	amountSOL := amountUSD.Div(decimal.NewFromInt(100))

	// Create claim
	claim, err := service.CreateClaim(ctx, userUUID, model.ClaimTypeTradingCashback, amountUSD, amountSOL, nil)
	if err != nil {
		global.GVA_LOG.Error("Failed to create cashback claim", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create claim: %s", err.Error()),
		}, nil
	}

	// Update user tier info to reflect claimed amount
	if err := service.ClaimCashback(ctx, userUUID, amountUSD); err != nil {
		global.GVA_LOG.Error("Failed to update user cashback", zap.Error(err))
		// Don't return error here as claim is already created
	}

	amountUSDFloat, _ := amountUSD.Float64()
	amountSOLFloat, _ := amountSOL.Float64()

	return &gql_model.CashbackClaimResponse{
		Success:   true,
		Message:   "Cashback claim created successfully",
		ClaimID:   claim.ID.String(),
		AmountUsd: amountUSDFloat,
		AmountSol: amountSOLFloat,
	}, nil
}

// RefreshTaskList refreshes the task list for the user
func (r *ActivityCashbackResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return false, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return false, fmt.Errorf("failed to initialize user: %s", err.Error())
	}

	if err := service.RefreshTaskList(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to refresh task list", zap.Error(err))
		return false, err
	}

	return true, nil
}

// UserTaskListByCategory retrieves user task progress by category (progress only)
func (r *ActivityCashbackResolver) UserTaskListByCategory(ctx context.Context, input gql_model.UserTaskListByCategoryInput) (*gql_model.UserTaskListByCategoryResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Convert GraphQL enum to model enum
	categoryName := convertGQLCategoryToModel(input.CategoryName)
	if !categoryName.IsValid() {
		return &gql_model.UserTaskListByCategoryResponse{
			Success: false,
			Message: "Invalid category name",
			Data:    []*gql_model.TaskWithProgress{},
		}, nil
	}

	// Get user task progress by category with task details
	tasksWithProgress, err := service.GetUserTaskListByCategoryWithDetails(ctx, userUUID, categoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task list by category", zap.Error(err))
		return &gql_model.UserTaskListByCategoryResponse{
			Success: false,
			Message: "Failed to retrieve user task progress",
			Data:    []*gql_model.TaskWithProgress{},
		}, nil
	}

	// Convert to GraphQL types
	gqlTasksWithProgress := make([]*gql_model.TaskWithProgress, len(tasksWithProgress))
	for i, taskWithProgress := range tasksWithProgress {
		gqlTasksWithProgress[i] = convertTaskWithProgressToGQL(&taskWithProgress)
	}

	return &gql_model.UserTaskListByCategoryResponse{
		Success: true,
		Message: "User task progress retrieved successfully",
		Data:    gqlTasksWithProgress,
	}, nil
}

// Helper functions to convert models to GraphQL types

func convertUserDashboardToGQL(dashboard *activity_cashback.UserDashboard) *gql_model.UserDashboard {
	if dashboard == nil {
		return nil
	}

	gqlDashboard := &gql_model.UserDashboard{
		UserTierInfo:     convertUserTierInfoToGQL(dashboard.UserTierInfo),
		TierBenefit:      convertTierBenefitToGQL(dashboard.TierBenefit),
		PointsToNextTier: dashboard.PointsToNextTier,
		UserRank:         dashboard.UserRank,
	}

	if dashboard.NextTier != nil {
		gqlDashboard.NextTier = convertTierBenefitToGQL(dashboard.NextTier)
	}

	claimableCashback, _ := dashboard.ClaimableCashback.Float64()
	gqlDashboard.ClaimableCashback = claimableCashback

	for _, claim := range dashboard.RecentClaims {
		gqlDashboard.RecentClaims = append(gqlDashboard.RecentClaims, convertActivityCashbackClaimToGQL(&claim))
	}

	return gqlDashboard
}

func convertTaskCenterToGQL(taskCenter *activity_cashback.TaskCenter) *gql_model.TaskCenter {
	if taskCenter == nil {
		return nil
	}

	gqlTaskCenter := &gql_model.TaskCenter{
		CompletedToday:    taskCenter.CompletedToday,
		PointsEarnedToday: taskCenter.PointsEarnedToday,
	}

	for _, category := range taskCenter.Categories {
		gqlCategory := &gql_model.TaskCategoryWithTasks{
			Category: convertTaskCategoryToGQL(&category.Category),
		}

		for _, taskWithProgress := range category.Tasks {
			gqlTaskWithProgress := &gql_model.TaskWithProgress{
				Task: convertActivityTaskToGQL(&taskWithProgress.Task),
			}
			if taskWithProgress.Progress != nil {
				gqlTaskWithProgress.Progress = convertUserTaskProgressToGQL(taskWithProgress.Progress)
			}
			gqlCategory.Tasks = append(gqlCategory.Tasks, gqlTaskWithProgress)
		}

		gqlTaskCenter.Categories = append(gqlTaskCenter.Categories, gqlCategory)
	}

	for _, progress := range taskCenter.UserProgress {
		gqlTaskCenter.UserProgress = append(gqlTaskCenter.UserProgress, convertUserTaskProgressToGQL(&progress))
	}

	for _, streak := range taskCenter.StreakTasks {
		gqlTaskCenter.StreakTasks = append(gqlTaskCenter.StreakTasks, convertUserTaskProgressToGQL(&streak))
	}

	return gqlTaskCenter
}

func convertUserTierInfoToGQL(tierInfo *model.UserTierInfo) *gql_model.UserTierInfo {
	if tierInfo == nil {
		return nil
	}

	tradingVolume, _ := tierInfo.TradingVolumeUSD.Float64()
	cumulativeCashback, _ := tierInfo.CumulativeCashbackUSD.Float64()
	claimableCashback, _ := tierInfo.ClaimableCashbackUSD.Float64()
	claimedCashback, _ := tierInfo.ClaimedCashbackUSD.Float64()

	gqlTierInfo := &gql_model.UserTierInfo{
		UserID:                tierInfo.UserID.String(),
		CurrentTier:           tierInfo.CurrentTier,
		TotalPoints:           tierInfo.TotalPoints,
		PointsThisMonth:       tierInfo.PointsThisMonth,
		TradingVolumeUsd:      tradingVolume,
		ActiveDaysThisMonth:   tierInfo.ActiveDaysThisMonth,
		CumulativeCashbackUsd: cumulativeCashback,
		ClaimableCashbackUsd:  claimableCashback,
		ClaimedCashbackUsd:    claimedCashback,
		CreatedAt:             tierInfo.CreatedAt,
		UpdatedAt:             tierInfo.UpdatedAt,
	}

	if tierInfo.LastActivityDate != nil {
		gqlTierInfo.LastActivityDate = tierInfo.LastActivityDate
	}
	if tierInfo.TierUpgradedAt != nil {
		gqlTierInfo.TierUpgradedAt = tierInfo.TierUpgradedAt
	}
	if tierInfo.MonthlyResetAt != nil {
		gqlTierInfo.MonthlyResetAt = tierInfo.MonthlyResetAt
	}
	if tierInfo.TierBenefit != nil {
		gqlTierInfo.TierBenefit = convertTierBenefitToGQL(tierInfo.TierBenefit)
	}

	return gqlTierInfo
}

func convertTierBenefitToGQL(benefit *model.TierBenefit) *gql_model.TierBenefit {
	if benefit == nil {
		return nil
	}

	cashbackPercentage, _ := benefit.CashbackPercentage.Float64()
	netFee, _ := benefit.NetFee.Float64()

	gqlBenefit := &gql_model.TierBenefit{
		ID:                 strconv.Itoa(int(benefit.ID)),
		TierLevel:          benefit.TierLevel,
		TierName:           benefit.TierName,
		MinPoints:          benefit.MinPoints,
		CashbackPercentage: cashbackPercentage,
		NetFee:             netFee,
		IsActive:           benefit.IsActive,
		CreatedAt:          benefit.CreatedAt,
		UpdatedAt:          benefit.UpdatedAt,
	}

	if benefit.BenefitsDescription != nil {
		gqlBenefit.BenefitsDescription = benefit.BenefitsDescription
	}
	if benefit.TierColor != nil {
		gqlBenefit.TierColor = benefit.TierColor
	}
	if benefit.TierIcon != nil {
		gqlBenefit.TierIcon = benefit.TierIcon
	}

	return gqlBenefit
}

func convertTaskCategoryToGQL(category *model.TaskCategory) *gql_model.TaskCategory {
	if category == nil {
		return nil
	}

	gqlCategory := &gql_model.TaskCategory{
		ID:          strconv.Itoa(int(category.ID)),
		Name:        convertModelCategoryToGQL(category.Name),
		DisplayName: category.DisplayName,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}

	if category.Description != nil {
		gqlCategory.Description = category.Description
	}
	if category.Icon != nil {
		gqlCategory.Icon = category.Icon
	}

	// Note: Tasks field is intentionally not populated to avoid circular reference
	// TaskCategory -> ActivityTask -> TaskCategory infinite loop
	// Tasks can be retrieved separately via other APIs

	return gqlCategory
}

func convertActivityTaskToGQL(task *model.ActivityTask) *gql_model.ActivityTask {
	if task == nil {
		return nil
	}

	gqlTask := &gql_model.ActivityTask{
		ID:         task.ID.String(),
		CategoryID: strconv.Itoa(int(task.CategoryID)),
		Name:       task.Name,

		Frequency: convertTaskFrequencyToGQL(task.Frequency),
		Points:    task.Points,
		IsActive:  task.IsActive,
		SortOrder: task.SortOrder,
		CreatedAt: task.CreatedAt,
		UpdatedAt: task.UpdatedAt,
	}

	if task.Description != nil {
		gqlTask.Description = task.Description
	}
	if task.TaskIdentifier != nil {
		taskIdentifier := gql_model.TaskIdentifier(string(*task.TaskIdentifier))
		gqlTask.TaskIdentifier = &taskIdentifier
	}
	if task.MaxCompletions != nil {
		gqlTask.MaxCompletions = task.MaxCompletions
	}
	if task.ResetPeriod != nil {
		resetPeriod := string(*task.ResetPeriod)
		gqlTask.ResetPeriod = &resetPeriod
	}
	if task.Conditions != nil {
		conditionsJSON, _ := json.Marshal(task.Conditions)
		conditionsStr := string(conditionsJSON)
		gqlTask.Conditions = &conditionsStr
	}
	if task.ActionTarget != nil {
		gqlTask.ActionTarget = task.ActionTarget
	}
	if task.VerificationMethod != nil {
		verificationMethod := string(*task.VerificationMethod)
		gqlTask.VerificationMethod = &verificationMethod
	}
	if task.ExternalLink != nil {
		gqlTask.ExternalLink = task.ExternalLink
	}
	if task.TaskIcon != nil {
		gqlTask.TaskIcon = task.TaskIcon
	}
	if task.ButtonText != nil {
		gqlTask.ButtonText = task.ButtonText
	}
	if task.StartDate != nil {
		gqlTask.StartDate = utils.TimeToTimestamp(task.StartDate)
	}
	if task.EndDate != nil {
		gqlTask.EndDate = utils.TimeToTimestamp(task.EndDate)
	}
	// Note: Category field is intentionally not populated to avoid circular reference
	// TaskCategory -> ActivityTask -> TaskCategory infinite loop
	// Category information is available via categoryId field

	return gqlTask
}

func convertUserTaskProgressToGQL(progress *model.UserTaskProgress) *gql_model.UserTaskProgress {
	if progress == nil {
		return nil
	}

	gqlProgress := &gql_model.UserTaskProgress{
		ID:                 progress.ID.String(),
		UserID:             progress.UserID.String(),
		TaskID:             progress.TaskID.String(),
		Status:             convertTaskStatusToGQL(progress.Status),
		ProgressValue:      progress.ProgressValue,
		CompletionCount:    progress.CompletionCount,
		PointsEarned:       progress.PointsEarned,
		StreakCount:        progress.StreakCount,
		CreatedAt:          progress.CreatedAt,
		UpdatedAt:          progress.UpdatedAt,
		ProgressPercentage: progress.GetProgressPercentage(),
		CanBeClaimed:       progress.CanBeClaimed(),
	}

	if progress.TargetValue != nil {
		gqlProgress.TargetValue = progress.TargetValue
	}
	if progress.LastCompletedAt != nil {
		gqlProgress.LastCompletedAt = progress.LastCompletedAt
	}
	if progress.LastResetAt != nil {
		gqlProgress.LastResetAt = progress.LastResetAt
	}
	if progress.Metadata != nil {
		metadataJSON, _ := json.Marshal(progress.Metadata)
		metadataStr := string(metadataJSON)
		gqlProgress.Metadata = &metadataStr
	}

	return gqlProgress
}

func convertActivityCashbackClaimToGQL(claim *model.ActivityCashbackClaim) *gql_model.ActivityCashbackClaim {
	if claim == nil {
		return nil
	}

	totalAmountUSD, _ := claim.TotalAmountUSD.Float64()
	totalAmountSOL, _ := claim.TotalAmountSOL.Float64()

	gqlClaim := &gql_model.ActivityCashbackClaim{
		ID:             claim.ID.String(),
		UserID:         claim.UserID.String(),
		ClaimType:      convertClaimTypeToGQL(claim.ClaimType),
		TotalAmountUsd: totalAmountUSD,
		TotalAmountSol: totalAmountSOL,
		Status:         convertClaimStatusToGQL(claim.Status),
		ClaimedAt:      claim.ClaimedAt,
		CreatedAt:      claim.CreatedAt,
		UpdatedAt:      claim.UpdatedAt,
	}

	if claim.TransactionHash != nil {
		gqlClaim.TransactionHash = claim.TransactionHash
	}
	if claim.ProcessedAt != nil {
		gqlClaim.ProcessedAt = claim.ProcessedAt
	}
	if claim.Metadata != nil {
		metadataJSON, _ := json.Marshal(claim.Metadata)
		metadataStr := string(metadataJSON)
		gqlClaim.Metadata = &metadataStr
	}

	return gqlClaim
}

// Enum conversion functions

func convertTaskFrequencyToGQL(frequency model.TaskFrequency) gql_model.TaskFrequency {
	switch frequency {
	case model.FrequencyDaily:
		return gql_model.TaskFrequencyDaily
	case model.FrequencyOneTime:
		return gql_model.TaskFrequencyOneTime
	case model.FrequencyUnlimited:
		return gql_model.TaskFrequencyUnlimited
	case model.FrequencyProgressive:
		return gql_model.TaskFrequencyProgressive
	case model.FrequencyManual:
		return gql_model.TaskFrequencyManual
	default:
		return gql_model.TaskFrequencyDaily
	}
}

func convertTaskStatusToGQL(status model.TaskStatus) gql_model.TaskStatus {
	switch status {
	case model.TaskStatusNotStarted:
		return gql_model.TaskStatusNotStarted
	case model.TaskStatusInProgress:
		return gql_model.TaskStatusInProgress
	case model.TaskStatusCompleted:
		return gql_model.TaskStatusCompleted
	case model.TaskStatusClaimed:
		return gql_model.TaskStatusClaimed
	case model.TaskStatusExpired:
		return gql_model.TaskStatusExpired
	default:
		return gql_model.TaskStatusNotStarted
	}
}

func convertClaimTypeToGQL(claimType model.ClaimType) gql_model.ClaimType {
	switch claimType {
	case model.ClaimTypeTradingCashback:
		return gql_model.ClaimTypeTradingCashback
	case model.ClaimTypeTaskReward:
		return gql_model.ClaimTypeTaskReward
	case model.ClaimTypeTierBonus:
		return gql_model.ClaimTypeTierBonus
	case model.ClaimTypeReferralBonus:
		return gql_model.ClaimTypeReferralBonus
	default:
		return gql_model.ClaimTypeTradingCashback
	}
}

func convertClaimStatusToGQL(status model.ClaimStatus) gql_model.ClaimStatus {
	switch status {
	case model.ClaimStatusPending:
		return gql_model.ClaimStatusPending
	case model.ClaimStatusProcessing:
		return gql_model.ClaimStatusProcessing
	case model.ClaimStatusCompleted:
		return gql_model.ClaimStatusCompleted
	case model.ClaimStatusFailed:
		return gql_model.ClaimStatusFailed
	default:
		return gql_model.ClaimStatusPending
	}
}

// convertTaskWithProgressToGQL converts service TaskWithProgress to GraphQL TaskWithProgress
func convertTaskWithProgressToGQL(taskWithProgress *activity_cashback.TaskWithProgress) *gql_model.TaskWithProgress {
	if taskWithProgress == nil {
		return nil
	}

	gqlTask := convertActivityTaskToGQL(&taskWithProgress.Task)
	var gqlProgress *gql_model.UserTaskProgress
	if taskWithProgress.Progress != nil {
		gqlProgress = convertUserTaskProgressToGQL(taskWithProgress.Progress)
	}

	return &gql_model.TaskWithProgress{
		Task:     gqlTask,
		Progress: gqlProgress,
	}
}

// convertActivityCashbackSummaryToGQL converts service ActivityCashbackSummary to GraphQL ActivityCashbackSummary
func convertActivityCashbackSummaryToGQL(summary *activity_cashback.ActivityCashbackSummary) *gql_model.ActivityCashbackSummary {
	if summary == nil {
		return nil
	}

	// Convert decimal values to float64
	accumulatedTradingVolumeUSD, _ := summary.AccumulatedTradingVolumeUSD.Float64()
	accumulatedCashbackUSD, _ := summary.AccumulatedCashbackUSD.Float64()
	claimableCashbackUSD, _ := summary.ClaimableCashbackUSD.Float64()
	claimedCashbackUSD, _ := summary.ClaimedCashbackUSD.Float64()

	gqlSummary := &gql_model.ActivityCashbackSummary{
		// Current ranking info
		CurrentLevel:     summary.CurrentLevel,
		CurrentLevelName: summary.CurrentLevelName,
		NextLevel:        summary.NextLevel,
		NextLevelName:    summary.NextLevelName,

		// Progress calculation
		CurrentScore:           summary.CurrentScore,
		TotalScoreForNextLevel: summary.TotalScoreForNextLevel,
		ScoreRequiredToUpgrade: summary.ScoreRequiredToUpgrade,
		ProgressPercentage:     summary.ProgressPercentage,

		// Trading volume (MEME only for now)
		AccumulatedTradingVolumeUsd: accumulatedTradingVolumeUSD,

		// Activity tracking
		ActiveLogonDays: summary.ActiveLogonDays,

		// Cashback information
		AccumulatedCashbackUsd: accumulatedCashbackUSD,
		ClaimableCashbackUsd:   claimableCashbackUSD,
		ClaimedCashbackUsd:     claimedCashbackUSD,

		// Additional tier info
		CurrentTierColor: summary.CurrentTierColor,
		CurrentTierIcon:  summary.CurrentTierIcon,
		NextTierColor:    summary.NextTierColor,
		NextTierIcon:     summary.NextTierIcon,
	}

	return gqlSummary
}

// Helper functions to convert between model and GraphQL category enums
func convertModelCategoryToGQL(modelCategory model.TaskCategoryName) gql_model.TaskCategoryName {
	switch modelCategory {
	case model.CategoryDaily:
		return gql_model.TaskCategoryNameDaily
	case model.CategoryCommunity:
		return gql_model.TaskCategoryNameCommunity
	case model.CategoryTrading:
		return gql_model.TaskCategoryNameTrading
	default:
		return gql_model.TaskCategoryNameDaily
	}
}

func convertGQLCategoryToModel(gqlCategory gql_model.TaskCategoryName) model.TaskCategoryName {
	switch gqlCategory {
	case gql_model.TaskCategoryNameDaily:
		return model.CategoryDaily
	case gql_model.TaskCategoryNameCommunity:
		return model.CategoryCommunity
	case gql_model.TaskCategoryNameTrading:
		return model.CategoryTrading
	default:
		return model.CategoryDaily
	}
}
