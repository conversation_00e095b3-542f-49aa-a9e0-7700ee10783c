package resolvers

import (
	"context"
	"fmt"
	"sort"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
)

type RewardResolver struct {
	rewardService           *reward.RewardDataService
	invitationRecordService *invitation.InvitationRecordService
}

func NewRewardResolver() *RewardResolver {
	return &RewardResolver{
		rewardService:           reward.NewRewardDataService(),
		invitationRecordService: invitation.NewInvitationRecordService(),
	}
}

func (r *RewardResolver) InvitationRecords(ctx context.Context, input gql_model.InvitationRecordRequest) (*gql_model.InvitationRecordResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	request := &invitation.InvitationRecordRequest{
		Page:     input.Page,
		PageSize: input.PageSize,
	}

	invitationRecords, err := r.invitationRecordService.GetInvitationRecords(ctx, userID, request)
	if err != nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain invitation record: %v", err),
		}, nil
	}

	var gqlRecords []*gql_model.InvitationRecord
	for _, record := range invitationRecords.Data {
		gqlRecord := &gql_model.InvitationRecord{
			Address:           record.Address,
			TransactionVolume: record.TransactionVolume,
			InvitedWithdrawal: record.InvitedWithdrawal,
			Date:              record.Date,
		}
		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.InvitationRecordResponse{
		Success:  true,
		Message:  "Get invitation record successfully",
		Data:     gqlRecords,
		Total:    invitationRecords.Total,
		Page:     invitationRecords.Page,
		PageSize: invitationRecords.PageSize,
	}, nil
}

func (r *RewardResolver) WithdrawalRecords(ctx context.Context, input gql_model.WithdrawalRecordRequest) (*gql_model.WithdrawalRecordResponse, error) {

	// Create repository instance
	activityCashbackRepo := repo.NewActivityCashbackRepository()

	// Get current user ID (from authentication info in context)
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "Failed to get user information"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed activity cashback records with pagination
	claimedCashbacks, err := activityCashbackRepo.GetClaimedCashbacksByUserIDWithPagination(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		message := "Failed to query activity cashback records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed commission records with pagination
	claimedCommissions, totalCommissions, err := r.getClaimedCommissionsByUserIDWithPagination(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		message := "Failed to query commission records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Convert record format
	var withdrawalRecords []*gql_model.WithdrawalRecord

	// Convert activity cashback records
	for _, cashback := range claimedCashbacks.Data {
		// meme 查询AffiliateTransaction的TxHash，关联ActivityCashback的CashbackAmountUSD，status为CLAIMED
		// Generate hash value (using ID as unique identifier)
		hash := fmt.Sprintf("01x35...%s", cashback.ID.String()[:8])

		// Format reward amount (including currency symbol)
		var withdrawalReward string
		if cashback.CashbackAmountSOL.GreaterThan(decimal.Zero) {
			withdrawalReward = cashback.CashbackAmountSOL.String()
		} else {
			withdrawalReward = cashback.CashbackAmountUSD.String()
		}

		// Format date
		var date string
		if cashback.ClaimedAt != nil {
			date = cashback.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	for _, commission := range claimedCommissions {
		// 合约 查询HyperLiquidTransaction的Hash，关联CommissionLedger的CommissionAmount， status为CLAIMED
		// TODO: Process commission record
		hash := fmt.Sprintf("01x35...%s", commission.ID.String()[:8])

		withdrawalReward := commission.CommissionAmount.String()

		var date string
		if commission.ClaimedAt != nil {
			date = commission.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	sort.Slice(withdrawalRecords, func(i, j int) bool {
		return withdrawalRecords[i].Date > withdrawalRecords[j].Date
	})

	// Calculate total records (cashbacks + commissions)
	totalRecords := int(claimedCashbacks.Total + totalCommissions)

	message := "Query successful"
	return &gql_model.WithdrawalRecordResponse{
		Data:     withdrawalRecords,
		Total:    totalRecords,
		Page:     input.Page,
		PageSize: input.PageSize,
		Success:  true,
		Message:  &message,
	}, nil
}

// getClaimedCommissionsByUserID Query user's claimed commission records
func (r *RewardResolver) getClaimedCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error) {
	// Directly query database to get claimed commission records
	var commissions []model.CommissionLedger
	err := global.GVA_DB.WithContext(ctx).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Preload("RecipientUser").
		Find(&commissions).Error

	if err != nil {
		return nil, fmt.Errorf("Failed to query commission records: %w", err)
	}

	return commissions, nil
}

// getClaimedCommissionsByUserIDWithPagination Query user's claimed commission records with pagination
func (r *RewardResolver) getClaimedCommissionsByUserIDWithPagination(ctx context.Context, userID uuid.UUID, page, pageSize int) ([]model.CommissionLedger, int64, error) {
	var commissions []model.CommissionLedger
	var total int64

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get total count
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("Failed to query commission records count: %w", err)
	}

	// Get paginated data
	err = global.GVA_DB.WithContext(ctx).Debug().
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Preload("RecipientUser").
		Order("claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&commissions).Error

	if err != nil {
		return nil, 0, fmt.Errorf("Failed to query commission records: %w", err)
	}

	return commissions, total, nil
}
