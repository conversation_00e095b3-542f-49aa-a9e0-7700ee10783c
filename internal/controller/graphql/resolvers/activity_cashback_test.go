package resolvers

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	admin_gql_model "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// MockGraphQLContext creates a mock context with user ID
func MockGraphQLContext(userID string) context.Context {
	ctx := context.Background()
	return context.WithValue(ctx, "userId", userID)
}

// TestActivityCashbackDashboard tests the dashboard GraphQL resolver
func TestActivityCashbackDashboard(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	// This test would require mocking the service layer
	// For now, we'll test the basic structure
	response, err := resolver.ActivityCashbackDashboard(ctx)

	// In a real test, you'd mock the service and verify the response
	assert.NotNil(t, response)
	assert.NoError(t, err)
}

// TestTaskCenter tests the task center GraphQL resolver
func TestTaskCenter(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	response, err := resolver.TaskCenter(ctx)

	assert.NotNil(t, response)
	assert.NoError(t, err)
}

// TestCompleteTask tests the complete task GraphQL mutation
func TestCompleteTask(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	taskID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	input := gql_model.CompleteTaskInput{
		TaskID:           taskID.String(),
		VerificationData: &[]string{`{"volume": 100.0, "trade_type": "MEME"}`}[0],
	}

	response, err := resolver.CompleteTask(ctx, input)

	// The test should handle the case where the task doesn't exist
	// Since we're not creating test data, we expect a response with success=false
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
}

// TestClaimTaskReward tests the claim task reward GraphQL mutation
func TestClaimTaskReward(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	taskID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	input := gql_model.ClaimTaskRewardInput{
		TaskID: taskID.String(),
	}

	response, err := resolver.ClaimTaskReward(ctx, input)

	// Since we're not creating test data, we expect a response with success=false
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
}

// TestClaimCashback tests the claim cashback GraphQL mutation
func TestClaimCashback(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	input := gql_model.ClaimCashbackInput{
		AmountUsd: 10.0,
	}

	response, err := resolver.ClaimCashback(ctx, input)

	// The response should be successful (claim is created) but the actual claim will fail due to insufficient balance
	assert.NotNil(t, response)
	assert.NoError(t, err)
	// The response.Success might be true (claim created) even if the actual processing fails
}

// TestRefreshTaskList tests the refresh task list GraphQL mutation
func TestRefreshTaskList(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	result, err := resolver.RefreshTaskList(ctx)

	assert.NotNil(t, result)
	assert.NoError(t, err)
}

// TestConversionFunctions tests the model conversion functions
func TestConvertUserTierInfoToGQL(t *testing.T) {
	userID := uuid.New()
	tierInfo := &model.UserTierInfo{
		UserID:      userID,
		CurrentTier: 2,
		TotalPoints: 500,
	}

	gqlTierInfo := convertUserTierInfoToGQL(tierInfo)

	assert.NotNil(t, gqlTierInfo)
	assert.Equal(t, userID.String(), gqlTierInfo.UserID)
	assert.Equal(t, 2, gqlTierInfo.CurrentTier)
	assert.Equal(t, 500, gqlTierInfo.TotalPoints)
}

func TestConvertActivityTaskToGQL(t *testing.T) {
	taskID := uuid.New()
	task := &model.ActivityTask{
		ID:         taskID,
		CategoryID: 1,
		Name:       "Test Task",
		TaskType:   model.TaskTypeDaily,
		Frequency:  model.FrequencyDaily,
		Points:     10,
		IsActive:   true,
	}

	gqlTask := convertActivityTaskToGQL(task)

	assert.NotNil(t, gqlTask)
	assert.Equal(t, taskID.String(), gqlTask.ID)
	assert.Equal(t, "1", gqlTask.CategoryID)
	assert.Equal(t, "Test Task", gqlTask.Name)
	assert.Equal(t, gql_model.TaskTypeDaily, gqlTask.TaskType)
	assert.Equal(t, gql_model.TaskFrequencyDaily, gqlTask.Frequency)
	assert.Equal(t, 10, gqlTask.Points)
	assert.True(t, gqlTask.IsActive)
}

func TestConvertUserTaskProgressToGQL(t *testing.T) {
	progressID := uuid.New()
	userID := uuid.New()
	taskID := uuid.New()

	progress := &model.UserTaskProgress{
		ID:              progressID,
		UserID:          userID,
		TaskID:          taskID,
		Status:          model.TaskStatusInProgress,
		ProgressValue:   5,
		CompletionCount: 1,
		PointsEarned:    10,
		StreakCount:     3,
	}

	gqlProgress := convertUserTaskProgressToGQL(progress)

	assert.NotNil(t, gqlProgress)
	assert.Equal(t, progressID.String(), gqlProgress.ID)
	assert.Equal(t, userID.String(), gqlProgress.UserID)
	assert.Equal(t, taskID.String(), gqlProgress.TaskID)
	assert.Equal(t, gql_model.TaskStatusInProgress, gqlProgress.Status)
	assert.Equal(t, 5, gqlProgress.ProgressValue)
	assert.Equal(t, 1, gqlProgress.CompletionCount)
	assert.Equal(t, 10, gqlProgress.PointsEarned)
	assert.Equal(t, 3, gqlProgress.StreakCount)
}

func TestConvertTaskTypeToGQL(t *testing.T) {
	tests := []struct {
		input    model.TaskType
		expected gql_model.TaskType
	}{
		{model.TaskTypeDaily, gql_model.TaskTypeDaily},
		{model.TaskTypeOneTime, gql_model.TaskTypeOneTime},
		{model.TaskTypeUnlimited, gql_model.TaskTypeUnlimited},
		{model.TaskTypeProgressive, gql_model.TaskTypeProgressive},
		{model.TaskTypeManualUpdate, gql_model.TaskTypeManualUpdate},
	}

	for _, tt := range tests {
		result := convertTaskTypeToGQL(tt.input)
		assert.Equal(t, tt.expected, result)
	}
}

func TestConvertTaskStatusToGQL(t *testing.T) {
	tests := []struct {
		input    model.TaskStatus
		expected gql_model.TaskStatus
	}{
		{model.TaskStatusNotStarted, gql_model.TaskStatusNotStarted},
		{model.TaskStatusInProgress, gql_model.TaskStatusInProgress},
		{model.TaskStatusCompleted, gql_model.TaskStatusCompleted},
		{model.TaskStatusClaimed, gql_model.TaskStatusClaimed},
		{model.TaskStatusExpired, gql_model.TaskStatusExpired},
	}

	for _, tt := range tests {
		result := convertTaskStatusToGQL(tt.input)
		assert.Equal(t, tt.expected, result)
	}
}

func TestConvertClaimTypeToGQL(t *testing.T) {
	tests := []struct {
		input    model.ClaimType
		expected gql_model.ClaimType
	}{
		{model.ClaimTypeTradingCashback, gql_model.ClaimTypeTradingCashback},
		{model.ClaimTypeTaskReward, gql_model.ClaimTypeTaskReward},
		{model.ClaimTypeTierBonus, gql_model.ClaimTypeTierBonus},
		{model.ClaimTypeReferralBonus, gql_model.ClaimTypeReferralBonus},
	}

	for _, tt := range tests {
		result := convertClaimTypeToGQL(tt.input)
		assert.Equal(t, tt.expected, result)
	}
}

func TestConvertClaimStatusToGQL(t *testing.T) {
	tests := []struct {
		input    model.ClaimStatus
		expected gql_model.ClaimStatus
	}{
		{model.ClaimStatusPending, gql_model.ClaimStatusPending},
		{model.ClaimStatusProcessing, gql_model.ClaimStatusProcessing},
		{model.ClaimStatusCompleted, gql_model.ClaimStatusCompleted},
		{model.ClaimStatusFailed, gql_model.ClaimStatusFailed},
	}

	for _, tt := range tests {
		result := convertClaimStatusToGQL(tt.input)
		assert.Equal(t, tt.expected, result)
	}
}

// TestErrorHandling tests error handling in resolvers
func TestErrorHandlingInResolvers(t *testing.T) {
	resolver := NewActivityCashbackResolver()

	// Test with invalid context (no user ID)
	ctx := context.Background()

	response, err := resolver.ActivityCashbackDashboard(ctx)
	assert.Error(t, err)
	assert.Nil(t, response)
}

// TestAdminResolvers tests admin-specific resolvers
func TestCreateTaskResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	adminID := uuid.New()
	ctx := MockGraphQLContext(adminID.String())

	input := admin_gql_model.CreateTaskInput{
		CategoryID: "1",
		Name:       "Test Admin Task",
		TaskType:   admin_gql_model.TaskTypeTrading,
		Frequency:  admin_gql_model.TaskFrequencyDaily,
		Points:     15,
		SortOrder:  &[]int{1}[0],
	}

	response, err := resolver.CreateTask(ctx, input)

	assert.NotNil(t, response)
	assert.NoError(t, err)
	// Since we didn't provide a TaskIdentifier, it should be nil
	assert.Nil(t, response.TaskIdentifier)
}

// TestCreateTaskWithoutIdentifier tests creating a task without identifier
func TestCreateTaskWithoutIdentifier(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	adminID := uuid.New()
	ctx := MockGraphQLContext(adminID.String())

	input := admin_gql_model.CreateTaskInput{
		CategoryID: "1",
		Name:       "Test Task Without Identifier",
		TaskType:   admin_gql_model.TaskTypeTrading,
		Frequency:  admin_gql_model.TaskFrequencyDaily,
		Points:     15,
	}

	response, err := resolver.CreateTask(ctx, input)

	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Nil(t, response.TaskIdentifier) // Should be nil when not provided
}

func TestUpdateTaskResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	adminID := uuid.New()
	taskID := uuid.New()
	ctx := MockGraphQLContext(adminID.String())

	input := admin_gql_model.UpdateTaskInput{
		ID:     taskID.String(),
		Name:   &[]string{"Updated Task Name"}[0],
		Points: &[]int{20}[0],
	}

	response, err := resolver.UpdateTask(ctx, input)

	// Since the task doesn't exist, expect an error
	assert.Nil(t, response)
	assert.Error(t, err)
}

func TestDeleteTaskResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	adminID := uuid.New()
	taskID := uuid.New()
	ctx := MockGraphQLContext(adminID.String())

	result, err := resolver.DeleteTask(ctx, taskID.String())

	assert.NotNil(t, result)
	assert.NoError(t, err)
}

// TestQueryResolvers tests query resolvers
func TestTierBenefitsResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	ctx := context.Background()

	response, err := resolver.TierBenefits(ctx)

	assert.NotNil(t, response)
	assert.NoError(t, err)
}

func TestTaskCategoriesResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	ctx := context.Background()

	_, err := resolver.TaskCategories(ctx)

	// Since there are no task categories in the test database, response might be empty but not nil
	assert.NoError(t, err)
	// Response can be nil or empty slice, both are valid
}

func TestTasksByCategoryResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	ctx := context.Background()

	_, err := resolver.TasksByCategory(ctx, gql_model.TaskCategoryNameDaily)

	// Since there are no task categories in the test database, we expect an error (record not found)
	assert.Error(t, err)
}

func TestUserTierInfoResolver(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	response, err := resolver.UserTierInfo(ctx)

	assert.NotNil(t, response)
	assert.NoError(t, err)
}

// Benchmark tests for resolvers
func BenchmarkActivityCashbackDashboard(b *testing.B) {
	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = resolver.ActivityCashbackDashboard(ctx)
	}
}

func BenchmarkTaskCenter(b *testing.B) {
	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = resolver.TaskCenter(ctx)
	}
}

func BenchmarkCompleteTask(b *testing.B) {
	resolver := NewActivityCashbackResolver()
	userID := uuid.New()
	taskID := uuid.New()
	ctx := MockGraphQLContext(userID.String())

	input := gql_model.CompleteTaskInput{
		TaskID: taskID.String(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = resolver.CompleteTask(ctx, input)
	}
}
