package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/data_overview"
)

type DataOverviewResolver struct {
	s data_overview.DataOverviewServiceInterface
}

func NewDataOverviewResolver() *DataOverviewResolver {
	return &DataOverviewResolver{
		s: data_overview.NewDataOverviewService(),
	}
}

// DataOverview handles the data overview query
func (d *DataOverviewResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	timeRange := string(input.TimeRange)

	// Get data overview based on time range
	dataOverviewResponse, err := d.s.GetDataOverview(ctx, userID, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get data overview: %w", err)
	}

	// Convert to GraphQL response
	var message *string
	if dataOverviewResponse.Message != "" {
		message = &dataOverviewResponse.Message
	}

	response := &gql_model.DataOverviewWithSummary{
		Data:    DTODataOverviewListToGQL(dataOverviewResponse.Data),
		Summary: DTODataOverviewSummaryToGQL(dataOverviewResponse.Summary),
		Success: dataOverviewResponse.Success,
		Message: message,
	}

	return response, nil
}

// DTODataOverviewListToGQL converts DTO list to GraphQL model list
func DTODataOverviewListToGQL(dataList []*response.DataOverview) []*gql_model.DataOverview {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.DataOverview
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.DataOverview{
				RebateAmount:      DTODataOverviewCategoryToGQL(data.RebateAmount),
				TransactionVolume: DTODataOverviewCategoryToGQL(data.TransactionVolume),
				InvitationCount:   DTODataOverviewCategoryToGQL(data.InvitationCount),
				Timestamp:         data.Timestamp,
				Period:            data.Period,
			})
		}
	}

	return result
}

// DTODataOverviewCategoryToGQL converts DTO category to GraphQL model
func DTODataOverviewCategoryToGQL(category *response.DataOverviewCategory) *gql_model.DataOverviewCategory {
	if category == nil {
		return nil
	}

	return &gql_model.DataOverviewCategory{
		All:      category.All,
		Meme:     category.Meme,
		Contract: category.Contract,
	}
}

// DTODataOverviewSummaryToGQL converts DTO summary to GraphQL model
func DTODataOverviewSummaryToGQL(summary *response.DataOverviewSummary) *gql_model.DataOverviewSummary {
	if summary == nil {
		return nil
	}

	return &gql_model.DataOverviewSummary{
		TotalRebateAmount:        DTODataOverviewCategoryToGQL(summary.TotalRebateAmount),
		TotalTransactionVolume:   DTODataOverviewCategoryToGQL(summary.TotalTransactionVolume),
		TotalInvitationCount:     DTODataOverviewCategoryToGQL(summary.TotalInvitationCount),
		PeakRebateAmount:         DTODataOverviewCategoryToGQL(summary.PeakRebateAmount),
		PeakTransactionVolume:    DTODataOverviewCategoryToGQL(summary.PeakTransactionVolume),
		PeakInvitationCount:      DTODataOverviewCategoryToGQL(summary.PeakInvitationCount),
		AverageRebateAmount:      DTODataOverviewCategoryToGQL(summary.AverageRebateAmount),
		AverageTransactionVolume: DTODataOverviewCategoryToGQL(summary.AverageTransactionVolume),
		AverageInvitationCount:   DTODataOverviewCategoryToGQL(summary.AverageInvitationCount),
	}
}
