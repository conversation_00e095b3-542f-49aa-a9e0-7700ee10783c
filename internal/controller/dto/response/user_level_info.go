package response

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// UserLevelInfo represents user's current level information
type UserLevelInfo struct {
	CurrentLevel              *model.AgentLevel `json:"currentLevel"`
	MemeVolume                float64           `json:"memeVolume"`
	ContractVolume            float64           `json:"contractVolume"`
	TotalVolume               float64           `json:"totalVolume"`
	NextLevel                 *model.AgentLevel `json:"nextLevel"`
	VolumeToNextLevel         float64           `json:"volumeToNextLevel"`
	MemeVolumeToNextLevel     float64           `json:"memeVolumeToNextLevel"`
	ContractVolumeToNextLevel float64           `json:"contractVolumeToNextLevel"`
	IsMaxLevel                bool              `json:"isMaxLevel"`
}

// UserLevelInfoResponse represents the response for user level info query
type UserLevelInfoResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *UserLevelInfo `json:"data"`
}
