package response

import "time"

// DataOverviewCategory represents category-specific data
type DataOverviewCategory struct {
	All      float64 `json:"all"`      // 全部
	Meme     float64 `json:"meme"`     // Meme(SOL)
	Contract float64 `json:"contract"` // 合约(USDC)
}

// DataOverview represents data overview response
type DataOverview struct {
	RebateAmount      *DataOverviewCategory `json:"rebateAmount"`      // 返佣金额
	TransactionVolume *DataOverviewCategory `json:"transactionVolume"` // 成交量
	InvitationCount   *DataOverviewCategory `json:"invitationCount"`   // 受邀人数
	Timestamp         time.Time             `json:"timestamp"`
	Period            string                `json:"period"`
}

// DataOverviewSummary represents summary statistics for data overview
// 数据总览统计摘要，包含累计、峰值和平均值等统计信息
type DataOverviewSummary struct {
	// TotalRebateAmount 累计返佣金额统计
	// 包含全部(USDT)、Meme(SOL)、合约(USDC)三个类别的累计返佣金额
	TotalRebateAmount *DataOverviewCategory `json:"totalRebateAmount"`

	// TotalTransactionVolume 累计成交量统计
	// 包含全部(USDT)、Meme(SOL)、合约(USDC)三个类别的累计成交量
	TotalTransactionVolume *DataOverviewCategory `json:"totalTransactionVolume"`

	// TotalInvitationCount 累计受邀人数统计
	// 包含全部(USDT)、Meme(SOL)、合约(USDC)三个类别的累计受邀人数
	TotalInvitationCount *DataOverviewCategory `json:"totalInvitationCount"`

	// PeakRebateAmount 峰值返佣金额统计
	// 记录所选时间段内返佣金额的最高值，用于展示业务峰值表现
	PeakRebateAmount *DataOverviewCategory `json:"peakRebateAmount"`

	// PeakTransactionVolume 峰值成交量统计
	// 记录所选时间段内成交量的最高值，用于展示业务峰值表现
	PeakTransactionVolume *DataOverviewCategory `json:"peakTransactionVolume"`

	// PeakInvitationCount 峰值受邀人数统计
	// 记录所选时间段内受邀人数的最高值，用于展示业务峰值表现
	PeakInvitationCount *DataOverviewCategory `json:"peakInvitationCount"`

	// AverageRebateAmount 平均返佣金额统计
	// 计算所选时间段内返佣金额的平均值，用于评估业务平均水平
	AverageRebateAmount *DataOverviewCategory `json:"averageRebateAmount"`

	// AverageTransactionVolume 平均成交量统计
	// 计算所选时间段内成交量的平均值，用于评估业务平均水平
	AverageTransactionVolume *DataOverviewCategory `json:"averageTransactionVolume"`

	// AverageInvitationCount 平均受邀人数统计
	// 计算所选时间段内受邀人数的平均值，用于评估业务平均水平
	AverageInvitationCount *DataOverviewCategory `json:"averageInvitationCount"`
}

// DataOverviewResponse represents the response for data overview query
type DataOverviewResponse struct {
	Data    []*DataOverview      `json:"data"`
	Summary *DataOverviewSummary `json:"summary"`
	Success bool                 `json:"success"`
	Message string               `json:"message,omitempty"`
}
