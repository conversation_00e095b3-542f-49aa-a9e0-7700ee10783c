package response

// TransactionData represents unified transaction data response
type TransactionData struct {
	TransactionAmountUsd float64 `json:"transactionAmountUsd"`
	ClaimedUsd           float64 `json:"claimedUsd"`
	PendingClaimUsd      float64 `json:"pendingClaimUsd"`
	InvitationCount      int     `json:"invitationCount"`
	TransactingUserCount int     `json:"transactingUserCount"`
	ContractVolumeUsd    float64 `json:"contractVolumeUsd"`
	MemeVolumeUsd        float64 `json:"memeVolumeUsd"`
}

// TransactionDataResponse represents the response for transaction data query
type TransactionDataResponse struct {
	TransactionData []*TransactionData `json:"transactionData"`
	Success         bool               `json:"success"`
	Message         string             `json:"message,omitempty"`
}
