package response

import "time"

// InvitationListItem represents a single invitation record in the list
type InvitationListItem struct {
	UserAddress           string    `json:"userAddress"`       
	InvitationTime        time.Time `json:"invitationTime"`     
	TransactionType       string    `json:"transactionType"`     
	TransactionAmount     float64   `json:"transactionAmount"`     
	AccumulatedCommission float64   `json:"accumulatedCommission"` 
	Date                  string    `json:"date"`                
}

// InvitationListRequest represents the request parameters for invitation list
type InvitationListRequest struct {
	TransactionType string `json:"transactionType"` // (MEME, CONTRACT, SPOT, ALL)
	Page            int    `json:"page"`           
	PageSize        int    `json:"pageSize"`        
}

// InvitationListResponse represents the response for invitation list query
type InvitationListResponse struct {
	Data     []*InvitationListItem `json:"data"`
	Total    int                   `json:"total"`  
	Page     int                   `json:"page"`     
	PageSize int                   `json:"pageSize"` 
	Success  bool                  `json:"success"`
	Message  string                `json:"message,omitempty"`
}

// WithdrawalRecord represents a single withdrawal record
type WithdrawalR<PERSON>ord struct {
	ID               string    `json:"id"`
	UserAddress      string    `json:"userAddress"`
	WithdrawalAmount float64   `json:"withdrawalAmount"`
	WithdrawalAsset  string    `json:"withdrawalAsset"`
	Status           string    `json:"status"`
	TransactionHash  string    `json:"transactionHash"`
	CreatedAt        time.Time `json:"createdAt"`
	Date             string    `json:"date"`
}

// WithdrawalRecordRequest represents the request parameters for withdrawal records
type WithdrawalRecordRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
	Status   string `json:"status,omitempty"`
}

// WithdrawalRecordResponse represents the response for withdrawal records query
type WithdrawalRecordResponse struct {
	Data     []*WithdrawalRecord `json:"data"`
	Total    int                 `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"pageSize"`
	Success  bool                `json:"success"`
	Message  string              `json:"message,omitempty"`
}

// WithdrawInvitationRewardRequest represents the request for withdrawing invitation rewards
type WithdrawInvitationRewardRequest struct {
	Amount        float64 `json:"amount"`
	Asset         string  `json:"asset"`
	WalletAddress string  `json:"walletAddress"`
}

// WithdrawInvitationRewardResponse represents the response for withdrawing invitation rewards
type WithdrawInvitationRewardResponse struct {
	Success         bool   `json:"success"`
	Message         string `json:"message,omitempty"`
	TransactionHash string `json:"transactionHash,omitempty"`
	WithdrawalID    string `json:"withdrawalId,omitempty"`
}

// InvitationSummary represents the summary statistics for invited users
type InvitationSummary struct {
	InvitedUserCount int `json:"invitedUserCount"` 
	TradingUserCount int `json:"tradingUserCount"`
}

// InvitationSummaryResponse represents the response for invitation summary query
type InvitationSummaryResponse struct {
	Data    *InvitationSummary `json:"data"`
	Success bool               `json:"success"`
	Message string             `json:"message,omitempty"`
}
