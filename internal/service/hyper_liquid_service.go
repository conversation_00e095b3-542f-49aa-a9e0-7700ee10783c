package service

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/pierrec/lz4/v4"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type HyperLiquidService struct {
	hlBuilderTransactionRepo  transaction.HyperLiquidBuilderTransactionRepositoryInterface
	builderCrawlerHistoryRepo transaction.BuilderCrawlerHistoryInterface
	logger                    zap.Logger
}

func NewHyperLiquidService() HyperLiquidI {
	return &HyperLiquidService{
		hlBuilderTransactionRepo:  transaction.NewHyperLiquidBuilderTransactionRepository(),
		builderCrawlerHistoryRepo: transaction.NewBuilderCrawlerHistoryRepository(),
		logger:                    *global.GVA_LOG,
	}
}

func (s *HyperLiquidService) GetLatestBuilderTransactionCrawler() (string, time.Time, int64) {
	ctx := context.Background()
	builder := global.GVA_SENSITIVE_CONFIG.HyperLiquid.BuilderAddress

	lastCrawler, _ := s.builderCrawlerHistoryRepo.GetLastHistory(ctx, builder)
	startDate := global.GVA_CONFIG.HyperLiquid.SystemLaunchDate

	if lastCrawler == nil {
		s.logger.Info("No previous crawler history found, starting from system launch date", zap.String("builder", builder), zap.Time("startDate", startDate))
		return builder, startDate, 0
	}

	if lastCrawler.IsEnded {
		s.logger.Info("Last crawler has ended, starting from last index", zap.String("builder", builder), zap.Time("startDate", lastCrawler.Date), zap.Int64("lastIndex", lastCrawler.LastIndex))
		return builder, lastCrawler.Date.Add(24 * time.Hour), lastCrawler.LastIndex
	}

	return builder, lastCrawler.Date, lastCrawler.LastIndex
}

func (s *HyperLiquidService) ParseBuilderDataUrl(builder string, date time.Time) string {
	dateString := date.Format("20060102")

	return fmt.Sprintf("https://stats-data.hyperliquid.xyz/Mainnet/builder_fills/%s/%s.csv.lz4", builder, dateString)
}

func (s *HyperLiquidService) ProcessBuilderData(url string, startIndex int64) (*int64, error) {
	rowCount := int64(0)
	ctx := context.Background()

	resp, err := http.Get(url)
	if err != nil {
		panic(fmt.Errorf("failed to download: %w", err))
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusForbidden {
		return &rowCount, nil
	}

	if resp.StatusCode != http.StatusOK {
		panic(fmt.Errorf("unexpected status: %s", resp.Status))
	}

	lz4Reader := lz4.NewReader(resp.Body)
	csvReader := csv.NewReader(lz4Reader)

	var trades []model.HyperLiquidBuilderTransaction
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			// todo check 403 status
			panic(fmt.Errorf("failed to read csv: %w", err))
		}

		fmt.Println(record)

		if rowCount == 0 {
			// Skip header row
			rowCount++
			continue
		}

		rowCount++
		if rowCount < startIndex {
			continue
		}

		if len(record) < 14 {
			s.logger.Warn("Skipping incomplete record", zap.Int64("rowCount", rowCount), zap.Strings("record", record))
			continue
		}

		parsedTime, err := time.Parse(time.RFC3339, record[0])
		if err != nil {
			s.logger.Error("Failed to parse time", zap.Int64("rowCount", rowCount), zap.String("time", record[0]), zap.Error(err))
			continue
		}

		newId, err := uuid.NewV7()
		if err != nil {
			s.logger.Error("Failed to generate UUID", zap.Int64("rowCount", rowCount), zap.Error(err))
			newId = uuid.New()
		}

		trades = append(trades, model.HyperLiquidBuilderTransaction{
			ID:               newId,
			CreatedAt:        time.Now(),
			Time:             parsedTime,
			User:             record[1],
			Coin:             record[2],
			Side:             record[3],
			Price:            utils.StringToFloat(record[4]),
			Size:             utils.StringToFloat(record[5]),
			Crossed:          utils.StringToBool(record[6]),
			SpecialTradeType: record[7],
			TIF:              record[8],
			IsTrigger:        utils.StringToBool(record[9]),
			Counterparty:     record[10],
			ClosedPNL:        utils.StringToFloat(record[11]),
			TwapID:           utils.StringToInt64(record[12]),
			BuilderFee:       utils.StringToFloat(record[13]),
		})

		if len(trades) >= 100 {
			err := s.hlBuilderTransactionRepo.BulkCreate(ctx, trades)
			if err != nil {
				s.logger.Error("Failed to bulk insert trades", zap.Error(err))
				return nil, err
			}
			trades = trades[:0] // Clear the slice
		}
	}

	if len(trades) > 0 {
		err := s.hlBuilderTransactionRepo.BulkCreate(ctx, trades)
		if err != nil {
			s.logger.Error("Failed to bulk insert trades", zap.Error(err))
			return nil, err
		}
	}

	return &rowCount, nil
}

func (s *HyperLiquidService) CrawlHyperLiquidBuilderTransaction() error {
	builder, startDate, startIndex := s.GetLatestBuilderTransactionCrawler()
	currentTime := time.Now()
	lastActiveStartDate := startDate

	for {
		url := s.ParseBuilderDataUrl(builder, startDate)

		s.logger.Info("Starting HyperLiquid builder transaction crawler", zap.String("builder", builder), zap.Time("startDate", startDate), zap.Int64("startIndex", startIndex))

		newIndex, err := s.ProcessBuilderData(url, startIndex)
		if err != nil {
			s.logger.Error("Error processing builder data", zap.Error(err))
			s.logger.Info("Retrying in 5s")
			time.Sleep(5 * time.Second)
			continue
		}

		if newIndex != nil {
			if *newIndex == 0 && startDate.After(currentTime) {
				s.logger.Info("No new data found for the current date, skipping", zap.String("builder", builder), zap.Time("startDate", startDate))
				startDate = lastActiveStartDate // Move to the next day
				time.Sleep(1 * time.Minute)     // Wait before retrying
				s.logger.Info("Retrying after 1 minute", zap.String("builder", builder), zap.Time("startDate", startDate))
				continue
			}

			lastActiveStartDate = startDate
			history := &model.BuilderCrawlerHistory{
				ID:             uuid.New(),
				BuilderAddress: builder,
				Date:           startDate,
				LastIndex:      *newIndex,
				Url:            url,
				IsEnded:        true,
			}
			err = s.builderCrawlerHistoryRepo.Create(context.Background(), history)
			if err != nil {
				s.logger.Error("Failed to save crawler history", zap.Error(err))
				return err
			}
		}

		startDate = startDate.Add(24 * time.Hour) // Move to the next day
	}
}
