package invitation

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// InvitationListServiceInterface defines the interface for invitation list operations
type InvitationListServiceInterface interface {
}

// InvitationListService implements invitation list operations
type InvitationListService struct {
	userRepo        transaction.UserRepositoryInterface
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
}

// NewInvitationListService creates a new invitation list service
func NewInvitationListService() InvitationListServiceInterface {
	return &InvitationListService{
		userRepo:        transaction.NewUserRepository(),
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
	}
}

// GetInvitationList retrieves invitation list with filtering and pagination
func (s *InvitationListService) GetInvitationList(ctx context.Context, userID uuid.UUID, request *response.InvitationListRequest) (*response.InvitationListResponse, error) {
	// Get all direct referrals (level 1)
	directReferrals, err := s.userRepo.GetDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get direct referrals", zap.Error(err))
		return nil, fmt.Errorf("failed to get direct referrals: %w", err)
	}

	// TransactionType分别为Meme和合约
	// 获取 MEME 邀请记录
	// UserAddress查询AffiliateTransaction表的UserAddress，关联ActivityCashback的查询CashbackAmountUSD为AccumulatedCommission， 一天有多个就相加
	// TransactionAmount：这个地址的交易量CashbackAmountSOL*SolPriceUSD，一天可能多个相同地址，把他们相加

	// 获取合约邀请记录
	// user表通过userID 关联 address查询HyperLiquidTransaction的WalletAddress,关联CommissionLedger的查询CommissionAmount, userID在一天中可能有多个返佣
	//  这个用户地址的交易量HyperLiquidTransaction的avg_price * totalSz, 一天又多个进行相加

	var invitationItems []*response.InvitationListItem

	for _, referral := range directReferrals {
		item, err := s.buildInvitationListItem(ctx, &referral, request.TransactionType)
		if err != nil {
			global.GVA_LOG.Error("Failed to build invitation list item",
				zap.String("user_id", referral.ID.String()),
				zap.Error(err))
			continue
		}

		// Apply transaction type filter
		if request.TransactionType != "ALL" && item.TransactionType != request.TransactionType {
			continue
		}

		invitationItems = append(invitationItems, item)
	}

	// Apply pagination
	total := len(invitationItems)
	startIndex := (request.Page - 1) * request.PageSize
	endIndex := startIndex + request.PageSize

	if startIndex >= total {
		startIndex = total
	}
	if endIndex > total {
		endIndex = total
	}

	var paginatedItems []*response.InvitationListItem
	if startIndex < total {
		paginatedItems = invitationItems[startIndex:endIndex]
	}

	// Sort by invitation time (newest first)
	s.sortByInvitationTime(paginatedItems)

	return &response.InvitationListResponse{
		Data:     paginatedItems,
		Total:    total,
		Page:     request.Page,
		PageSize: request.PageSize,
		Success:  true,
		Message:  "Successfully obtained the invitation list",
	}, nil
}

// buildInvitationListItem builds a single invitation list item
func (s *InvitationListService) buildInvitationListItem(ctx context.Context, user *model.User, transactionType string) (*response.InvitationListItem, error) {
	// Get user wallet address
	walletAddress, err := s.getUserWalletAddress(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet address: %w", err)
	}

	// Get invitation time from referral record
	invitationTime, err := s.getInvitationTime(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitation time: %w", err)
	}

	// Format date as "MM-DD"
	date := invitationTime.Format("01-02")

	// Get transaction data based on type
	var transactionTypeStr string
	var transactionAmount float64
	var accumulatedCommission float64

	switch transactionType {
	case "MEME":
		transactionTypeStr = "MEME"
		amount, commission, err := s.getMemeTransactionData(ctx, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get MEME transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	case "CONTRACT":
		transactionTypeStr = "CONTRACT"
		amount, commission, err := s.getContractTransactionData(ctx, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get contract transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	case "SPOT":
		transactionTypeStr = "SPOT"
		amount, commission, err := s.getSpotTransactionData(ctx, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get spot transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	default:
		// For "ALL" type, get the highest transaction amount and commission
		transactionTypeStr, transactionAmount, accumulatedCommission = s.getBestTransactionData(ctx, user.ID)
	}

	return &response.InvitationListItem{
		UserAddress:           walletAddress,
		InvitationTime:        invitationTime,
		TransactionType:       transactionTypeStr,
		TransactionAmount:     transactionAmount,
		AccumulatedCommission: accumulatedCommission,
		Date:                  date,
	}, nil
}

// getUserWalletAddress gets the wallet address for a user
func (s *InvitationListService) getUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	var walletAddress string
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ?", userID).
		Limit(1).
		Pluck("wallet_address", &walletAddress).Error

	if err != nil {
		return "", err
	}

	if walletAddress == "" {
		return "Unbound wallet", nil
	}

	return walletAddress, nil
}

// getInvitationTime gets the invitation time from referral record
func (s *InvitationListService) getInvitationTime(ctx context.Context, userID uuid.UUID) (time.Time, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ? AND depth = 1", userID).
		First(&referral).Error

	if err != nil {
		return time.Time{}, err
	}

	return referral.CreatedAt, nil
}

// getMemeTransactionData gets MEME transaction data for a user
func (s *InvitationListService) getMemeTransactionData(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	// Get user wallet address first
	walletAddress, err := s.getUserWalletAddress(ctx, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get wallet address: %w", err)
	}

	if walletAddress == "Unbound wallet" {
		return 0, 0, nil
	}

	// Get transaction volume: CashbackAmountSOL * SolPriceUSD for each day, sum up multiple records per day
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Joins("JOIN affiliate_transactions ON activity_cashback.affiliate_transaction_id = affiliate_transactions.id").
		Select("COALESCE(SUM(activity_cashback.cashback_amount_sol * activity_cashback.sol_price_usd), 0) as total_volume").
		Where("affiliate_transactions.user_address = ? AND affiliate_transactions.transaction_type = ? AND affiliate_transactions.status = ?",
			walletAddress, "MEME", model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	// Get commission amount: sum up CashbackAmountUSD for each day, multiple records per day are summed
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Joins("JOIN affiliate_transactions ON activity_cashback.affiliate_transaction_id = affiliate_transactions.id").
		Select("COALESCE(SUM(activity_cashback.cashback_amount_usd), 0) as total_commission").
		Where("affiliate_transactions.user_address = ? AND affiliate_transactions.transaction_type = ? AND affiliate_transactions.status = ?",
			walletAddress, "MEME", model.StatusCompleted).
		Scan(&commissionResult).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get MEME commission amount", zap.Error(err))
	}

	volume, _ := result.TotalVolume.Float64()
	commission, _ := commissionResult.TotalCommission.Float64()

	return volume, commission, nil
}

// getContractTransactionData gets contract transaction data for a user
func (s *InvitationListService) getContractTransactionData(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	// Get user wallet address first
	walletAddress, err := s.getUserWalletAddress(ctx, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get wallet address: %w", err)
	}

	if walletAddress == "Unbound wallet" {
		return 0, 0, nil
	}

	// Get transaction volume: avg_price * totalSz for each day, sum up multiple records per day
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Select("COALESCE(SUM(avg_price * total_sz::decimal), 0) as total_volume").
		Where("wallet_address = ? AND status = ?", walletAddress, "completed").
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	// Get commission amount: sum up CommissionAmount for each day, multiple records per day are summed
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_commission").
		Where("source_user_id = ? AND source_transaction_type = ? AND status = ?",
			userID, "Direct", "CLAIMED").
		Scan(&commissionResult).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get contract commission amount", zap.Error(err))
	}

	volume, _ := result.TotalVolume.Float64()
	commission, _ := commissionResult.TotalCommission.Float64()

	return volume, commission, nil
}

// getSpotTransactionData gets spot transaction data for a user (placeholder for future implementation)
func (s *InvitationListService) getSpotTransactionData(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	// TODO: Implement spot transaction data retrieval
	return 0, 0, nil
}

// getBestTransactionData gets the best transaction data across all types
func (s *InvitationListService) getBestTransactionData(ctx context.Context, userID uuid.UUID) (string, float64, float64) {
	memeVolume, memeCommission, _ := s.getMemeTransactionData(ctx, userID)
	contractVolume, contractCommission, _ := s.getContractTransactionData(ctx, userID)
	spotVolume, spotCommission, _ := s.getSpotTransactionData(ctx, userID)

	// Find the type with highest volume
	if memeVolume >= contractVolume && memeVolume >= spotVolume {
		return "MEME", memeVolume, memeCommission
	} else if contractVolume >= spotVolume {
		return "CONTRACT", contractVolume, contractCommission
	} else {
		return "SPOT", spotVolume, spotCommission
	}
}

// sortByInvitationTime sorts items by invitation time (newest first)
func (s *InvitationListService) sortByInvitationTime(items []*response.InvitationListItem) {
	for i := 0; i < len(items)-1; i++ {
		for j := 0; j < len(items)-i-1; j++ {
			if items[j].InvitationTime.Before(items[j+1].InvitationTime) {
				items[j], items[j+1] = items[j+1], items[j]
			}
		}
	}
}
