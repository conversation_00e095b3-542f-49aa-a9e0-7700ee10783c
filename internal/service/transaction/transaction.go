package transaction

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
)

type HyperLiquidTransactionServiceInterface interface {
	BulkInsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error
	UpdateHyperLiquidTransactionByCloid(ctx context.Context, cloid string, txEvent *model.HyperLiquidTransaction) error
	BulkUpsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error
	FindHyperLiquidTransactionByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error)
}

type HyperLiquidTransactionService struct {
	transactionRepo transaction.HyperLiquidTransactionRepositoryInterface
}

func NewHyperLiquidTransactionService() HyperLiquidTransactionServiceInterface {
	return &HyperLiquidTransactionService{
		transactionRepo: transaction.NewHyperLiquidTransactionRepository(),
	}
}

func (s *HyperLiquidTransactionService) BulkInsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error {
	return s.transactionRepo.BulkCreate(ctx, txEvents)
}

func (s *HyperLiquidTransactionService) UpdateHyperLiquidTransactionByCloid(ctx context.Context, cloid string, txEvent *model.HyperLiquidTransaction) error {
	return s.transactionRepo.UpdateByCloid(ctx, cloid, txEvent)
}

func (s *HyperLiquidTransactionService) BulkUpsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error {
	return s.transactionRepo.BulkUpsertByCloid(ctx, txEvents)
}

func (s *HyperLiquidTransactionService) FindHyperLiquidTransactionByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error) {
	return s.transactionRepo.FindByCloid(ctx, cloid)
}
