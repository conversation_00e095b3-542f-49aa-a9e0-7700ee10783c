package transaction

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// Mock repositories
type MockAffiliateTransactionRepository struct {
	mock.Mock
}

func (m *MockAffiliateTransactionRepository) GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockAffiliateTransactionRepository) GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockAffiliateTransactionRepository) GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userIDs, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

type MockHyperLiquidTransactionRepository struct {
	mock.Mock
}

func (m *MockHyperLiquidTransactionRepository) Create(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockHyperLiquidTransactionRepository) BulkCreate(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	args := m.Called(ctx, txs)
	return args.Error(0)
}

func (m *MockHyperLiquidTransactionRepository) UpdateByCloid(ctx context.Context, cloid string, tx *model.HyperLiquidTransaction) error {
	args := m.Called(ctx, cloid, tx)
	return args.Error(0)
}

func (m *MockHyperLiquidTransactionRepository) BulkUpsertByCloid(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	args := m.Called(ctx, txs)
	return args.Error(0)
}

func (m *MockHyperLiquidTransactionRepository) FindByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error) {
	args := m.Called(ctx, cloid)
	return args.Get(0).(*model.HyperLiquidTransaction), args.Error(1)
}

func (m *MockHyperLiquidTransactionRepository) GetTotalVolumeByUserIDs(ctx context.Context, userIDs []uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockHyperLiquidTransactionRepository) GetTransactingUserIDs(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockHyperLiquidTransactionRepository) GetVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userIDs, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

type MockCommissionLedgerRepository struct {
	mock.Mock
}

func (m *MockCommissionLedgerRepository) GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetClaimedAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.CommissionLedger), args.Error(1)
}

type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetDirectReferrals(ctx context.Context, userID uuid.UUID) ([]model.User, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.User), args.Error(1)
}

func (m *MockUserRepository) GetAllDownlineUsers(ctx context.Context, userID uuid.UUID, maxDepth int) ([]uuid.UUID, error) {
	args := m.Called(ctx, userID, maxDepth)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockUserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetInvitationCountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockUserRepository) GetUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	args := m.Called(ctx, userID)
	return args.String(0), args.Error(1)
}

func TestTransactionDataService_GetTransactionData_ALL(t *testing.T) {
	// Setup
	ctx := context.Background()
	userID := uuid.New()

	mockAffiliateRepo := &MockAffiliateTransactionRepository{}
	mockHyperLiquidRepo := &MockHyperLiquidTransactionRepository{}
	mockCommissionRepo := &MockCommissionLedgerRepository{}
	mockUserRepo := &MockUserRepository{}

	service := &TransactionDataService{
		affiliateRepo:   mockAffiliateRepo,
		hyperLiquidRepo: mockHyperLiquidRepo,
		commissionRepo:  mockCommissionRepo,
		userRepo:        mockUserRepo,
	}

	// Mock data
	directReferrals := []model.User{{ID: uuid.New()}, {ID: uuid.New()}}
	allDownlineUsers := []uuid.UUID{uuid.New(), uuid.New(), uuid.New()}

	// Setup expectations
	mockUserRepo.On("GetDirectReferrals", ctx, userID).Return(directReferrals, nil)
	mockUserRepo.On("GetAllDownlineUsers", ctx, userID, 3).Return(allDownlineUsers, nil)
	mockUserRepo.On("GetInvitationCountByUserIDAndPeriod", mock.Anything, userID, mock.Anything, mock.Anything).Return(2, nil)

	mockAffiliateRepo.On("GetVolumeByUserIDsAndPeriod", ctx, allDownlineUsers, mock.Anything, mock.Anything).Return(decimal.NewFromFloat(1000.0), nil)
	mockHyperLiquidRepo.On("GetVolumeByUserIDsAndPeriod", ctx, allDownlineUsers, mock.Anything, mock.Anything).Return(decimal.NewFromFloat(2000.0), nil)
	mockCommissionRepo.On("GetClaimedAmountByUserIDAndTypeAndPeriod", ctx, userID, "ALL", mock.Anything, mock.Anything).Return(decimal.NewFromFloat(500.0), nil)
	mockCommissionRepo.On("GetPendingClaimAmountByUserIDAndTypeAndPeriod", ctx, userID, "ALL", mock.Anything, mock.Anything).Return(decimal.NewFromFloat(300.0), nil)

	mockAffiliateRepo.On("GetTransactingUserIDs", ctx, allDownlineUsers).Return([]uuid.UUID{uuid.New()}, nil)
	mockHyperLiquidRepo.On("GetTransactingUserIDs", ctx, allDownlineUsers).Return([]uuid.UUID{uuid.New()}, nil)

	// Execute
	result, err := service.GetTransactionData(ctx, userID, "ALL", "LAST_30_DAYS")

	// Assert
	require.NoError(t, err)
	require.Len(t, result, 1)

	data := result[0]
	assert.Equal(t, 3000.0, data.TransactionAmountUsd) // 1000 + 2000
	assert.Equal(t, 500.0, data.ClaimedUsd)
	assert.Equal(t, 300.0, data.PendingClaimUsd)
	assert.Equal(t, 2, data.InvitationCount)
	assert.Equal(t, 2, data.TransactingUserCount) // 1 + 1 (deduplicated)

	// Verify all mocks were called
	mockAffiliateRepo.AssertExpectations(t)
	mockHyperLiquidRepo.AssertExpectations(t)
	mockCommissionRepo.AssertExpectations(t)
	mockUserRepo.AssertExpectations(t)
}

func TestTransactionDataService_GetTransactionData_MEME(t *testing.T) {
	// Setup
	ctx := context.Background()
	userID := uuid.New()

	mockAffiliateRepo := &MockAffiliateTransactionRepository{}
	mockHyperLiquidRepo := &MockHyperLiquidTransactionRepository{}
	mockCommissionRepo := &MockCommissionLedgerRepository{}
	mockUserRepo := &MockUserRepository{}

	service := &TransactionDataService{
		affiliateRepo:   mockAffiliateRepo,
		hyperLiquidRepo: mockHyperLiquidRepo,
		commissionRepo:  mockCommissionRepo,
		userRepo:        mockUserRepo,
	}

	// Mock data
	directReferrals := []model.User{{ID: uuid.New()}}
	allDownlineUsers := []uuid.UUID{uuid.New(), uuid.New()}

	// Setup expectations
	mockUserRepo.On("GetDirectReferrals", ctx, userID).Return(directReferrals, nil)
	mockUserRepo.On("GetAllDownlineUsers", ctx, userID, 3).Return(allDownlineUsers, nil)
	mockUserRepo.On("GetInvitationCountByUserIDAndPeriod", mock.Anything, userID, mock.Anything, mock.Anything).Return(1, nil)

	mockAffiliateRepo.On("GetVolumeByUserIDsAndPeriod", ctx, allDownlineUsers, mock.Anything, mock.Anything).Return(decimal.NewFromFloat(1500.0), nil)
	mockCommissionRepo.On("GetClaimedAmountByUserIDAndTypeAndPeriod", ctx, userID, "MEME", mock.Anything, mock.Anything).Return(decimal.NewFromFloat(200.0), nil)
	mockCommissionRepo.On("GetPendingClaimAmountByUserIDAndTypeAndPeriod", ctx, userID, "MEME", mock.Anything, mock.Anything).Return(decimal.NewFromFloat(100.0), nil)

	mockAffiliateRepo.On("GetTransactingUserIDs", ctx, allDownlineUsers).Return([]uuid.UUID{uuid.New()}, nil)
	mockHyperLiquidRepo.On("GetTransactingUserIDs", ctx, allDownlineUsers).Return([]uuid.UUID{}, nil)

	// Execute
	result, err := service.GetTransactionData(ctx, userID, "MEME", "TODAY")

	// Assert
	require.NoError(t, err)
	require.Len(t, result, 1)

	data := result[0]
	assert.Equal(t, 1500.0, data.TransactionAmountUsd)
	assert.Equal(t, 200.0, data.ClaimedUsd)
	assert.Equal(t, 100.0, data.PendingClaimUsd)
	assert.Equal(t, 1, data.InvitationCount)
	assert.Equal(t, 1, data.TransactingUserCount)

	// Verify all mocks were called
	mockAffiliateRepo.AssertExpectations(t)
	mockHyperLiquidRepo.AssertExpectations(t)
	mockCommissionRepo.AssertExpectations(t)
	mockUserRepo.AssertExpectations(t)
}

func TestTransactionDataService_CalculateTimeRange(t *testing.T) {
	service := &TransactionDataService{}

	// Test TODAY
	start, _, err := service.calculateTimeRange("TODAY")
	require.NoError(t, err)

	now := time.Now().UTC()
	expectedStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)

	assert.Equal(t, expectedStart.Year(), start.Year())
	assert.Equal(t, expectedStart.Month(), start.Month())
	assert.Equal(t, expectedStart.Day(), start.Day())
	assert.Equal(t, expectedStart.Hour(), start.Hour())

	// Test LAST_30_DAYS
	start, _, err = service.calculateTimeRange("LAST_30_DAYS")
	require.NoError(t, err)

	expectedStart30Days := now.AddDate(0, 0, -30)
	assert.Equal(t, expectedStart30Days.Year(), start.Year())
	assert.Equal(t, expectedStart30Days.Month(), start.Month())
	assert.Equal(t, expectedStart30Days.Day(), start.Day())

	// Test invalid time range
	_, _, err = service.calculateTimeRange("INVALID")
	assert.Error(t, err)
}
