package data_overview

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// DataOverviewServiceInterface defines the interface for data overview operations
type DataOverviewServiceInterface interface {
	GetDataOverview(ctx context.Context, userID uuid.UUID, timeRange string) (*response.DataOverviewResponse, error)
}

// DataOverviewService implements data overview operations
type DataOverviewService struct {
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
	userRepo        transaction.UserRepositoryInterface
}

// NewDataOverviewService creates a new data overview service
func NewDataOverviewService() DataOverviewServiceInterface {
	return &DataOverviewService{
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
		userRepo:        transaction.NewUserRepository(),
	}
}

// GetDataOverview retrieves data overview based on the specified time range
func (s *DataOverviewService) GetDataOverview(ctx context.Context, userID uuid.UUID, timeRange string) (*response.DataOverviewResponse, error) {
	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get data based on time range
	var data []*response.DataOverview
	var startTime, endTime time.Time

	switch timeRange {
	case "TODAY":
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour).UTC()
		data, err = s.getHourlyData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_30_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-30 * 24 * time.Hour).UTC()
		data, err = s.getDailyData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_60_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-60 * 24 * time.Hour).UTC()
		data, err = s.getDailyData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "ALL_TIME":
		endTime = time.Now()
		startTime = endTime.Add(-12 * 30 * 24 * time.Hour).UTC() // 12 months
		data, err = s.getMonthlyData(ctx, userID, allDownlineUsers, startTime, endTime)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	if err != nil {
		return nil, err
	}

	// Calculate summary statistics
	summary := s.calculateSummary(data)

	return &response.DataOverviewResponse{
		Data:    data,
		Summary: summary,
		Success: true,
	}, nil
}

// getHourlyData gets data for the past 24 hours with hourly granularity
func (s *DataOverviewService) getHourlyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	for i := 0; i < 24; i++ {
		periodStart := startTime.Add(time.Duration(i) * time.Hour)
		periodEnd := periodStart.Add(time.Hour)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("15:04"),
		})
	}

	return data, nil
}

// getDailyData gets data for the specified days with daily granularity
func (s *DataOverviewService) getDailyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	days := int(endTime.Sub(startTime).Hours() / 24)
	for i := 0; i < days; i++ {
		periodStart := startTime.AddDate(0, 0, i)
		periodEnd := periodStart.AddDate(0, 0, 1)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("01/02"),
		})
	}

	return data, nil
}

// getMonthlyData gets data for the past 12 months with monthly granularity
func (s *DataOverviewService) getMonthlyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	for i := 0; i < 12; i++ {
		periodStart := startTime.AddDate(0, i, 0)
		periodEnd := periodStart.AddDate(0, 1, 0)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("2006-01"),
		})
	}

	return data, nil
}

// getPeriodData gets data for a specific time period
func (s *DataOverviewService) getPeriodData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (*response.DataOverviewCategory, *response.DataOverviewCategory, *response.DataOverviewCategory, error) {
	// Get MEME data
	memeRebate, memeVolume, err := s.getMemeData(ctx, userID, downlineUsers, startTime, endTime)
	if err != nil {
		return nil, nil, nil, err
	}

	// Get CONTRACT data
	contractRebate, contractVolume, err := s.getContractData(ctx, userID, downlineUsers, startTime, endTime)
	if err != nil {
		return nil, nil, nil, err
	}

	// Calculate total (USDT equivalent)
	totalRebate := memeRebate + contractRebate
	totalVolume := memeVolume + contractVolume

	rebateAmount := &response.DataOverviewCategory{
		All:      totalRebate,
		Meme:     memeRebate,
		Contract: contractRebate,
	}

	transactionVolume := &response.DataOverviewCategory{
		All:      totalVolume,
		Meme:     memeVolume,
		Contract: contractVolume,
	}

	// Get invitation count for the period
	invitationCountInt, err := s.getInvitationCount(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, nil, nil, err
	}

	invitationCount := &response.DataOverviewCategory{
		All:      float64(invitationCountInt),
		Meme:     0, // invitation count does not distinguish by type
		Contract: 0, // invitation count does not distinguish by type
	}

	return rebateAmount, transactionVolume, invitationCount, nil
}

// getMemeData gets MEME transaction data for a specific period
func (s *DataOverviewService) getMemeData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (float64, float64, error) {
	// Get MEME transaction volume from DailyMemeVolume
	volume, err := s.getDailyMemeVolumeByUserIDsAndPeriod(ctx, downlineUsers, startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get MEME volume from DailyMemeVolume: %w", err)
	}

	// Get MEME rebate amount from ActivityCashback
	rebate, err := s.getActivityCashbackAmountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get MEME rebate from ActivityCashback: %w", err)
	}

	return rebate, volume, nil
}

// getContractData gets CONTRACT transaction data for a specific period
func (s *DataOverviewService) getContractData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (float64, float64, error) {
	// Get CONTRACT transaction volume from DailyUserVolume
	volume, err := s.getDailyUserVolumeByUserIDsAndPeriod(ctx, downlineUsers, startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get CONTRACT volume from DailyUserVolume: %w", err)
	}

	// Get CONTRACT rebate amount from CommissionLedger
	rebate, err := s.commissionRepo.GetRebateAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get CONTRACT rebate: %w", err)
	}

	rebateFloat, _ := rebate.Float64()

	return rebateFloat, volume, nil
}

// getInvitationCount gets invitation count for a specific period
func (s *DataOverviewService) getInvitationCount(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	// 总的邀请人数是三级的邀请人数，查询ReferralSnapshot的TotalDownlineCount字段
	var snapshot model.ReferralSnapshot
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.ReferralSnapshot{}).
		Select("total_downline_count").
		Where("user_id = ?", userID).
		First(&snapshot).Error

	if err != nil {
		// 如果查询失败，返回0而不是错误，因为可能用户还没有snapshot记录
		global.GVA_LOG.Warn("Failed to get referral snapshot for user",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return 0, nil
	}

	return snapshot.TotalDownlineCount, nil
}

// calculateSummary calculates summary statistics from the data
func (s *DataOverviewService) calculateSummary(data []*response.DataOverview) *response.DataOverviewSummary {
	if len(data) == 0 {
		return &response.DataOverviewSummary{}
	}

	// Initialize summary categories
	totalRebateAmount := &response.DataOverviewCategory{}
	totalTransactionVolume := &response.DataOverviewCategory{}
	totalInvitationCount := &response.DataOverviewCategory{}
	peakRebateAmount := &response.DataOverviewCategory{}
	peakTransactionVolume := &response.DataOverviewCategory{}
	peakInvitationCount := &response.DataOverviewCategory{}

	for _, item := range data {
		if item.RebateAmount != nil {
			totalRebateAmount.All += item.RebateAmount.All
			totalRebateAmount.Meme += item.RebateAmount.Meme
			totalRebateAmount.Contract += item.RebateAmount.Contract

			if item.RebateAmount.All > peakRebateAmount.All {
				peakRebateAmount.All = item.RebateAmount.All
			}
			if item.RebateAmount.Meme > peakRebateAmount.Meme {
				peakRebateAmount.Meme = item.RebateAmount.Meme
			}
			if item.RebateAmount.Contract > peakRebateAmount.Contract {
				peakRebateAmount.Contract = item.RebateAmount.Contract
			}
		}

		if item.TransactionVolume != nil {
			totalTransactionVolume.All += item.TransactionVolume.All
			totalTransactionVolume.Meme += item.TransactionVolume.Meme
			totalTransactionVolume.Contract += item.TransactionVolume.Contract

			if item.TransactionVolume.All > peakTransactionVolume.All {
				peakTransactionVolume.All = item.TransactionVolume.All
			}
			if item.TransactionVolume.Meme > peakTransactionVolume.Meme {
				peakTransactionVolume.Meme = item.TransactionVolume.Meme
			}
			if item.TransactionVolume.Contract > peakTransactionVolume.Contract {
				peakTransactionVolume.Contract = item.TransactionVolume.Contract
			}
		}

		if item.InvitationCount != nil {
			totalInvitationCount.All += item.InvitationCount.All
			totalInvitationCount.Meme += item.InvitationCount.Meme
			totalInvitationCount.Contract += item.InvitationCount.Contract

			if item.InvitationCount.All > peakInvitationCount.All {
				peakInvitationCount.All = item.InvitationCount.All
			}
			if item.InvitationCount.Meme > peakInvitationCount.Meme {
				peakInvitationCount.Meme = item.InvitationCount.Meme
			}
			if item.InvitationCount.Contract > peakInvitationCount.Contract {
				peakInvitationCount.Contract = item.InvitationCount.Contract
			}
		}
	}

	count := float64(len(data))

	// Calculate averages
	averageRebateAmount := &response.DataOverviewCategory{
		All:      math.Round(totalRebateAmount.All/count*100) / 100,
		Meme:     math.Round(totalRebateAmount.Meme/count*100) / 100,
		Contract: math.Round(totalRebateAmount.Contract/count*100) / 100,
	}

	averageTransactionVolume := &response.DataOverviewCategory{
		All:      math.Round(totalTransactionVolume.All/count*100) / 100,
		Meme:     math.Round(totalTransactionVolume.Meme/count*100) / 100,
		Contract: math.Round(totalTransactionVolume.Contract/count*100) / 100,
	}

	averageInvitationCount := &response.DataOverviewCategory{
		All:      math.Round(totalInvitationCount.All/count*100) / 100,
		Meme:     math.Round(totalInvitationCount.Meme/count*100) / 100,
		Contract: math.Round(totalInvitationCount.Contract/count*100) / 100,
	}

	return &response.DataOverviewSummary{
		TotalRebateAmount:        totalRebateAmount,
		TotalTransactionVolume:   totalTransactionVolume,
		TotalInvitationCount:     totalInvitationCount,
		PeakRebateAmount:         peakRebateAmount,
		PeakTransactionVolume:    peakTransactionVolume,
		PeakInvitationCount:      peakInvitationCount,
		AverageRebateAmount:      averageRebateAmount,
		AverageTransactionVolume: averageTransactionVolume,
		AverageInvitationCount:   averageInvitationCount,
	}
}

// getDailyMemeVolumeByUserIDsAndPeriod gets MEME volume from DailyMemeVolume table for given user IDs within a time period
func (s *DataOverviewService) getDailyMemeVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (float64, error) {
	var result struct {
		TotalVolume float64
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.DailyMemeVolume{}).
		Select("COALESCE(SUM(meme_volume_usd), 0) as total_volume").
		Where("user_id IN ? AND date >= ? AND date < ?", userIDs, startTime, endTime).
		Scan(&result).Error

	if err != nil {
		return 0, fmt.Errorf("failed to get daily meme volume: %w", err)
	}

	return result.TotalVolume, nil
}

// getDailyUserVolumeByUserIDsAndPeriod gets CONTRACT volume from DailyUserVolume table for given user IDs within a time period
func (s *DataOverviewService) getDailyUserVolumeByUserIDsAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (float64, error) {
	var result struct {
		TotalVolume float64
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.DailyUserVolume{}).
		Select("COALESCE(SUM(contract_volume_usd), 0) as total_volume").
		Where("user_id IN ? AND date >= ? AND date < ?", userIDs, startTime, endTime).
		Scan(&result).Error

	if err != nil {
		return 0, fmt.Errorf("failed to get daily user volume: %w", err)
	}

	return result.TotalVolume, nil
}

// getActivityCashbackAmountByUserIDAndPeriod gets MEME rebate amount from ActivityCashback table for a user within a time period
func (s *DataOverviewService) getActivityCashbackAmountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (float64, error) {
	var result struct {
		TotalAmount float64
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Scan(&result).Error

	if err != nil {
		return 0, fmt.Errorf("failed to get activity cashback amount: %w", err)
	}

	return result.TotalAmount, nil
}
