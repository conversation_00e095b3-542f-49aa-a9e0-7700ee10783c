package contract

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type ContractCommissionService struct {
	userRepo       repo.InvitationRepo
	levelRepo      repo.LevelRepo
	commissionRepo transaction.CommissionLedgerRepositoryInterface
}

func NewContractCommissionService() *ContractCommissionService {
	return &ContractCommissionService{
		userRepo:       &agent_referral.InvitationRepository{},
		levelRepo:      repo.NewLevelRepository(),
		commissionRepo: transaction.NewCommissionLedgerRepository(),
	}
}

// Process contract trading commission calculations
func (s *ContractCommissionService) ProcessContractCommission(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction) error {
	global.GVA_LOG.Info("calculate contract commission",
		zap.String("cloid", hyperliquidTx.Cloid),
	)

	// Update FirstTransactionAt if this is the user's first transaction
	err := s.updateFirstTransactionAt(ctx, hyperliquidTx)
	if err != nil {
		global.GVA_LOG.Error("Failed to update FirstTransactionAt",
			zap.String("cloid", hyperliquidTx.Cloid),
			zap.String("user_id", hyperliquidTx.UserID.String()),
			zap.Error(err))
		// Continue processing even if FirstTransactionAt update fails
	}

	// Save user wallet if wallet address is provided
	err = s.saveUserWallet(ctx, hyperliquidTx)
	if err != nil {
		global.GVA_LOG.Error("Failed to save user wallet",
			zap.String("cloid", hyperliquidTx.Cloid),
			zap.String("user_id", hyperliquidTx.UserID.String()),
			zap.Error(err))
		// Continue processing even if wallet save fails
	}

	err = s.processCommission(ctx, hyperliquidTx)
	if err != nil {
		return fmt.Errorf("failed to process commission distribution: %w", err)
	}

	return nil
}

func (s *ContractCommissionService) processCommission(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	referralInfo, err := s.getReferralInfo(ctx, *tx.UserID)
	if err != nil {
		global.GVA_LOG.Debug("No referral found for user",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("User has no referrer",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	// Get the upline hierarchy for the user
	if tx.BuildFee != nil {
		err := s.getUplineHierarchy(ctx, *tx.UserID, tx)
		if err != nil {
			return fmt.Errorf("failed to get upline hierarchy: %w", err)
		}
	}

	return nil
}

// getUserWithLevel gets user with their agent level information
func (s *ContractCommissionService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// getUplineHierarchy gets the upline hierarchy for a user (up to 3 levels)
func (s *ContractCommissionService) getUplineHierarchy(ctx context.Context, userID uuid.UUID, tx *model.HyperLiquidTransaction) error {
	// Get direct referrer (L1)
	referralInfo, err := s.getReferralInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get referral info: %w", err)
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L1 referrer found",
			zap.String("user_id", userID.String()),
			zap.String("cloid", tx.Cloid))
		// If L1 referrer is not found, we can skip creating direct commission
		// but we can log it for debugging purposes
		return nil
	}

	// Get L1 upline
	l1User, err := s.getUserWithLevel(ctx, *referralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	if l1User == nil {
		global.GVA_LOG.Debug("No L1 user found for referral",
			zap.String("referrer_id", referralInfo.ReferrerID.String()),
			zap.String("cloid", tx.Cloid))
		// If L1 user is not found, we can skip creating direct commission
		// but we can log it for debugging purposes
		return nil
	}

	// get direct rebate by agent level
	// buildFee * DirectCommissionRate
	directLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l1User.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to get agent level: %w", err)
	}

	now := time.Now()
	if directLevel != nil && tx.BuildFee != nil {
		// Check if commission record already exists for this cloid and recipient
		var existingDirectCommission model.CommissionLedger
		err := global.GVA_DB.WithContext(ctx).
			Where("source_transaction_id = ? AND recipient_user_id = ? AND source_transaction_type = ?",
				tx.Cloid, l1User.ID, "Direct").
			First(&existingDirectCommission).Error

		if err == nil {
			// Record already exists, skip creation
			global.GVA_LOG.Info("Extended commission already exists for cloid:",
				zap.String("cloid ", tx.Cloid), zap.String("recipient:", l1User.ID.String()))
			return nil
		}

		// Record doesn't exist, create new one
		commissionLedgerDirect := &model.CommissionLedger{
			RecipientUserID:       l1User.ID,
			SourceUserID:          *tx.UserID,
			SourceTransactionID:   tx.Cloid,
			SourceTransactionType: "Direct",
			Status:                "PENDING_CLAIM",
			CreatedAt:             &now,
			UpdatedAt:             &now,
		}
		directFee := tx.BuildFee.Mul(directLevel.DirectCommissionRate)
		commissionLedgerDirect.CommissionAsset = directLevel.DirectCommissionRate.String()
		commissionLedgerDirect.CommissionAmount = directFee
		if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerDirect).Error; err != nil {
			return fmt.Errorf("failed to create commission direct: %w", err)
		}
		fmt.Println("Created direct commission for cloid:", tx.Cloid, "recipient:", l1User.ID.String())
	} else {
		global.GVA_LOG.Info("Skipping direct commission - missing level or build fee",
			zap.String("cloid", tx.Cloid),
			zap.Bool("has_direct_level", directLevel != nil),
			zap.Bool("has_build_fee", tx.BuildFee != nil))
		return nil
	}

	// Indirect Get L2 upline (referrer of L1)
	l2ReferralInfo, err := s.getReferralInfo(ctx, *referralInfo.ReferrerID)
	if err != nil || l2ReferralInfo.ReferrerID == nil {
		return fmt.Errorf("failed to referral info indirect: %w", err)
	}

	if l2ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L2 referrer found",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	l2User, err := s.getUserWithLevel(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to user level indirect: %w", err)
	}

	if l2User == nil {
		global.GVA_LOG.Info("No L2 user found for referral",
			zap.String("referrer_id", l2ReferralInfo.ReferrerID.String()),
			zap.String("cloid", tx.Cloid))
		// If L2 user is not found, we can skip creating indirect commission
		// but we can log it for debugging purposes
		return nil
	}
	// buildFee * IndirectCommissionRate
	indirectLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l2User.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to agent level indirect: %w", err)
	}

	if indirectLevel != nil && tx.BuildFee != nil {
		// Check if commission record already exists for this cloid and recipient
		var existingIndirectCommission model.CommissionLedger
		err := global.GVA_DB.WithContext(ctx).
			Where("source_transaction_id = ? AND recipient_user_id = ? AND source_transaction_type = ?",
				tx.Cloid, l2User.ID, "Indirect").
			First(&existingIndirectCommission).Error

		if err == nil {
			// Record already exists, skip creation
			global.GVA_LOG.Info("Extended commission already exists for cloid:",
				zap.String("cloid ", tx.Cloid), zap.String("recipient:", l2User.ID.String()))
			return nil
		}

		// Record doesn't exist, create new one
		commissionLedgerIndirect := &model.CommissionLedger{
			RecipientUserID:       l2User.ID,
			SourceUserID:          *tx.UserID,
			SourceTransactionID:   tx.Cloid,
			SourceTransactionType: "Indirect",
			Status:                "PENDING_CLAIM",
			CreatedAt:             &now,
			UpdatedAt:             &now,
		}
		indirectFee := tx.BuildFee.Mul(indirectLevel.IndirectCommissionRate)
		commissionLedgerIndirect.CommissionAsset = indirectLevel.IndirectCommissionRate.String()
		commissionLedgerIndirect.CommissionAmount = indirectFee
		if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerIndirect).Error; err != nil {
			return fmt.Errorf("failed to create commission indirect: %w", err)
		}
		fmt.Println("Created indirect commission for cloid:", tx.Cloid, "recipient:", l2User.ID.String())
	} else {
		global.GVA_LOG.Info("Skipping indirect commission - missing level or build fee",
			zap.String("cloid", tx.Cloid),
			zap.Bool("has_indirect_level", indirectLevel != nil),
			zap.Bool("has_build_fee", tx.BuildFee != nil))
		return nil
	}

	// Get L3 upline (referrer of L2)
	l3ReferralInfo, err := s.getReferralInfo(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil || l3ReferralInfo.ReferrerID == nil {
		return fmt.Errorf("failed to referral info extended: %w", err)
	}
	if l3ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Info("No L3 referrer found",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	l3User, err := s.getUserWithLevel(ctx, *l3ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to user level extended: %w", err)
	}
	if l3User == nil {
		global.GVA_LOG.Debug("No L3 user found for referral",
			zap.String("referrer_id", l3ReferralInfo.ReferrerID.String()),
			zap.String("cloid", tx.Cloid))
		// If L3 user is not found, we can skip creating extended commission
		// but we can log it for debugging purposes
		return nil
	}

	// buildFee * extendedCommissionRate
	extendedLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l3User.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to agent level extended: %w", err)
	}

	if extendedLevel != nil && tx.BuildFee != nil {
		// Check if commission record already exists for this cloid and recipient
		var existingExtendedCommission model.CommissionLedger
		err := global.GVA_DB.WithContext(ctx).
			Where("source_transaction_id = ? AND recipient_user_id = ? AND source_transaction_type = ?",
				tx.Cloid, l3User.ID, "Extended").
			First(&existingExtendedCommission).Error

		if err == nil {
			// Record already exists, skip creation
			global.GVA_LOG.Info("Extended commission already exists for cloid:",
				zap.String("cloid ", tx.Cloid), zap.String("recipient:", l3User.ID.String()))
			return nil
		}

		// Record doesn't exist, create new one
		commissionLedgerExtended := &model.CommissionLedger{
			RecipientUserID:       l3User.ID,
			SourceUserID:          *tx.UserID,
			SourceTransactionID:   tx.Cloid,
			SourceTransactionType: "Extended",
			Status:                "PENDING_CLAIM",
			CreatedAt:             &now,
			UpdatedAt:             &now,
		}
		extendedFee := tx.BuildFee.Mul(extendedLevel.ExtendedCommissionRate)
		commissionLedgerExtended.CommissionAsset = extendedLevel.ExtendedCommissionRate.String()
		commissionLedgerExtended.CommissionAmount = extendedFee
		if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerExtended).Error; err != nil {
			return fmt.Errorf("failed to create commission extended: %w", err)
		}
		fmt.Println("Created extended commission for cloid:", tx.Cloid, "recipient:", l3User.ID.String())
	} else {
		global.GVA_LOG.Info("Skipping extended commission - missing level or build fee",
			zap.String("cloid", tx.Cloid),
			zap.Bool("has_extended_level", extendedLevel != nil),
			zap.Bool("has_build_fee", tx.BuildFee != nil))
		return nil
	}

	return nil
}

// getReferralInfo gets referral information for a user
func (s *ContractCommissionService) getReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&referral).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

// updateFirstTransactionAt updates the FirstTransactionAt field for a user if it's their first transaction
func (s *ContractCommissionService) updateFirstTransactionAt(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	// Check if user already has FirstTransactionAt set
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Select("id, first_transaction_at").
		Where("id = ?", tx.UserID).
		First(&user).Error
	if err != nil {
		// If user not found, log it but don't treat as error
		// This might happen if user hasn't been created in the system yet
		global.GVA_LOG.Debug("User not found when updating FirstTransactionAt",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid),
			zap.Error(err))
		return nil
	}

	// If FirstTransactionAt is already set, no need to update
	if user.FirstTransactionAt != nil {
		global.GVA_LOG.Debug("FirstTransactionAt already set for user",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid),
			zap.Time("first_transaction_at", *user.FirstTransactionAt))
		return nil
	}

	// Update FirstTransactionAt with current transaction time
	now := time.Now()
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", tx.UserID).
		Update("first_transaction_at", &now).Error
	if err != nil {
		return fmt.Errorf("failed to update FirstTransactionAt: %w", err)
	}

	global.GVA_LOG.Info("Updated FirstTransactionAt for user",
		zap.String("user_id", tx.UserID.String()),
		zap.String("cloid", tx.Cloid),
		zap.Time("first_transaction_at", now))

	return nil
}

// saveUserWallet saves user wallet information to the UserWallet table
func (s *ContractCommissionService) saveUserWallet(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	// Check if we have the required information
	if tx.UserID == nil || tx.WalletAddress == nil || *tx.WalletAddress == "" {
		global.GVA_LOG.Debug("Skipping wallet save - missing user ID or wallet address",
			zap.String("cloid", tx.Cloid),
			zap.Bool("has_user_id", tx.UserID != nil),
			zap.Bool("has_wallet_address", tx.WalletAddress != nil && *tx.WalletAddress != ""))
		return nil
	}

	// Detect chain type from wallet address
	chainTypeStr := utils.DetectChainTypeFromAddress(*tx.WalletAddress)
	if chainTypeStr == "" {
		global.GVA_LOG.Warn("Could not detect chain type from wallet address",
			zap.String("cloid", tx.Cloid),
			zap.String("wallet_address", *tx.WalletAddress))
		return nil
	}

	// Convert string to ChainType
	var chainType model.ChainType
	switch chainTypeStr {
	case "EVM":
		chainType = model.ChainEvm
	case "ARB":
		chainType = model.ChainArb
	case "SOLANA":
		chainType = model.ChainSolana
	case "TRON":
		chainType = model.ChainTron
	default:
		global.GVA_LOG.Warn("Unsupported chain type detected",
			zap.String("cloid", tx.Cloid),
			zap.String("chain_type", chainTypeStr))
		return nil
	}

	// Check if wallet already exists for this user and address
	var existingWallet model.UserWallet
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ? AND wallet_address = ? AND chain = ?",
			*tx.UserID, *tx.WalletAddress, chainType).
		First(&existingWallet).Error

	if err == nil {
		// Wallet already exists, no need to create
		global.GVA_LOG.Debug("Wallet already exists for user",
			zap.String("cloid", tx.Cloid),
			zap.String("user_id", tx.UserID.String()),
			zap.String("wallet_address", *tx.WalletAddress),
			zap.String("chain", string(chainType)))
		return nil
	}

	// Create new wallet record
	userWallet := &model.UserWallet{
		UserID:        *tx.UserID,
		Chain:         chainType,
		WalletAddress: *tx.WalletAddress,
		// Note: WalletID and WalletAccountID are not available in HyperLiquidTransaction
		// They will be set to nil
	}

	if err := global.GVA_DB.WithContext(ctx).Create(userWallet).Error; err != nil {
		// Check if it's a duplicate key error (should not happen due to our check above)
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			global.GVA_LOG.Debug("Wallet already exists (duplicate key error)",
				zap.String("cloid", tx.Cloid),
				zap.String("user_id", tx.UserID.String()),
				zap.String("wallet_address", *tx.WalletAddress),
				zap.String("chain", string(chainType)))
			return nil
		}
		return fmt.Errorf("failed to create user wallet: %w", err)
	}

	global.GVA_LOG.Info("Created user wallet",
		zap.String("cloid", tx.Cloid),
		zap.String("user_id", tx.UserID.String()),
		zap.String("wallet_address", *tx.WalletAddress),
		zap.String("chain", string(chainType)))

	return nil
}
