package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	// "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
)

// Mock repositories for testing
type MockActivityTaskRepository struct {
	mock.Mock
}

func (m *MockActivityTaskRepository) Create(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) Update(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetByCategoryID(ctx context.Context, categoryID uint) ([]model.ActivityTask, error) {
	args := m.Called(ctx, categoryID)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetByTaskType(ctx context.Context, taskType model.TaskType) ([]model.ActivityTask, error) {
	args := m.Called(ctx, taskType)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetActive(ctx context.Context) ([]model.ActivityTask, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetAvailable(ctx context.Context, now time.Time) ([]model.ActivityTask, error) {
	args := m.Called(ctx, now)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetAll(ctx context.Context) ([]model.ActivityTask, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetByFrequency(ctx context.Context, frequency model.TaskFrequency) ([]model.ActivityTask, error) {
	args := m.Called(ctx, frequency)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

// Mock TierManagementService
type MockTierManagementService struct {
	mock.Mock
}

func (m *MockTierManagementService) AddPoints(ctx context.Context, userID uuid.UUID, points int, source string) error {
	args := m.Called(ctx, userID, points, source)
	return args.Error(0)
}

func (m *MockTierManagementService) CheckTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockTierManagementService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockTierManagementService) InitializeUserTierInfo(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockTierManagementService) UpdateTradingVolume(ctx context.Context, userID uuid.UUID, volumeUSD float64) error {
	args := m.Called(ctx, userID, volumeUSD)
	return args.Error(0)
}

func (m *MockTierManagementService) GetTierBenefits(ctx context.Context, tierLevel int) (*model.TierBenefit, error) {
	args := m.Called(ctx, tierLevel)
	return args.Get(0).(*model.TierBenefit), args.Error(1)
}

func (m *MockTierManagementService) GetAllTierBenefits(ctx context.Context) ([]model.TierBenefit, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.TierBenefit), args.Error(1)
}

func (m *MockTierManagementService) CalculateUserRank(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// Mock TaskProgressService
type MockTaskProgressService struct {
	mock.Mock
}

func (m *MockTaskProgressService) GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) UpdateProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error {
	args := m.Called(ctx, userID, taskID, increment)
	return args.Error(0)
}

func (m *MockTaskProgressService) SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	args := m.Called(ctx, userID, taskID, value)
	return args.Error(0)
}

func (m *MockTaskProgressService) CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockTaskProgressService) ResetProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockTaskProgressService) GetUserProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	args := m.Called(ctx, userID, startDate, endDate)
	return args.Get(0).(map[string]int), args.Error(1)
}

func (m *MockTaskProgressService) GetUserStreaks(ctx context.Context, userID uuid.UUID) (map[string]int, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(map[string]int), args.Error(1)
}

func TestTaskManagementService_CompleteTask(t *testing.T) {
	// Setup mocks
	mockTaskRepo := &MockActivityTaskRepository{}
	mockTierService := &MockTierManagementService{}
	// mockProgressService := &MockTaskProgressService{}

	// Create service with mocks
	service := &TaskManagementService{
		// taskRepo:          mockTaskRepo,
		// completionFactory: activity_cashback.NewTaskCompletionRepositoryFactory(),
		// tierService:       mockTierService,
		// progressService:   mockProgressService,
	}

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()
	verificationData := map[string]interface{}{"test": "data"}

	// Create test task
	task := &model.ActivityTask{
		ID:        taskID,
		Name:      "Test Task",
		Frequency: model.FrequencyDaily,
		Points:    100,
		IsActive:  true,
	}

	// Setup mock expectations
	mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)
	mockTierService.On("AddPoints", ctx, userID, 100, mock.AnythingOfType("string")).Return(nil)
	mockTierService.On("CheckTierUpgrade", ctx, userID).Return(&model.UserTierInfo{}, nil)

	// Test successful completion
	t.Run("Successful Task Completion", func(t *testing.T) {
		err := service.CompleteTask(ctx, userID, taskID, verificationData)
		assert.NoError(t, err)

		// Verify mock calls
		mockTaskRepo.AssertExpectations(t)
		mockTierService.AssertExpectations(t)
	})

	// Test with inactive task
	t.Run("Inactive Task", func(t *testing.T) {
		inactiveTask := &model.ActivityTask{
			ID:        taskID,
			Name:      "Inactive Task",
			Frequency: model.FrequencyDaily,
			Points:    100,
			IsActive:  false,
		}

		mockTaskRepo.On("GetByID", ctx, taskID).Return(inactiveTask, nil).Once()

		err := service.CompleteTask(ctx, userID, taskID, verificationData)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "task is not active")
	})

	// Test with non-existent task
	t.Run("Non-existent Task", func(t *testing.T) {
		nonExistentTaskID := uuid.New()
		mockTaskRepo.On("GetByID", ctx, nonExistentTaskID).Return((*model.ActivityTask)(nil), assert.AnError).Once()

		err := service.CompleteTask(ctx, userID, nonExistentTaskID, verificationData)
		assert.Error(t, err)
	})
}

func TestTaskManagementService_GetTaskByID(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	service := &TaskManagementService{
		// taskRepo: mockTaskRepo,
	}

	ctx := context.Background()
	taskID := uuid.New()

	task := &model.ActivityTask{
		ID:   taskID,
		Name: "Test Task",
	}

	mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)

	result, err := service.GetTaskByID(ctx, taskID)
	assert.NoError(t, err)
	assert.Equal(t, task, result)

	mockTaskRepo.AssertExpectations(t)
}

func TestTaskManagementService_GetActiveTasks(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	// service := &TaskManagementService{
	// 	taskRepo: mockTaskRepo,
	// }

	ctx := context.Background()

	tasks := []model.ActivityTask{
		{ID: uuid.New(), Name: "Task 1", IsActive: true},
		{ID: uuid.New(), Name: "Task 2", IsActive: true},
	}

	mockTaskRepo.On("GetActive", ctx).Return(tasks, nil)

	// result, err := service.GetActiveTasks(ctx)
	// assert.NoError(t, err)
	// assert.Equal(t, tasks, result)

	mockTaskRepo.AssertExpectations(t)
}

func TestTaskManagementService_VerifyTaskCompletion(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	// service := &TaskManagementService{
	// 	taskRepo: mockTaskRepo,
	// }

	// ctx := context.Background()
	// userID := uuid.New()
	// taskID := uuid.New()
	// verificationData := map[string]interface{}{"test": "data"}

	// task := &model.ActivityTask{
	// 	ID:                 taskID,
	// 	Name:               "Test Task",
	// 	TaskType:           model.TaskTypeDaily,
	// 	VerificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
	// }

	// mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)

	// result, err := service.VerifyTaskCompletion(ctx, userID, taskID, verificationData)
	// assert.NoError(t, err)
	// assert.True(t, result) // Auto verification should return true

	mockTaskRepo.AssertExpectations(t)
}
