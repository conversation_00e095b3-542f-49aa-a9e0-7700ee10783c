package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
)

// CashbackClaimService implements CashbackClaimServiceInterface
type CashbackClaimService struct {
	claimRepo activity_cashback.ActivityCashbackClaimRepositoryInterface
}

// NewCashbackClaimService creates a new CashbackClaimService
func NewCashbackClaimService(
	claimRepo activity_cashback.ActivityCashbackClaimRepositoryInterface,
) CashbackClaimServiceInterface {
	return &CashbackClaimService{
		claimRepo: claimRepo,
	}
}

// CreateClaim creates a new cashback claim
func (s *CashbackClaimService) CreateClaim(ctx context.Context, userID uuid.UUID, claimType model.ClaimType, amountUSD, amountSOL decimal.Decimal, metadata *model.ClaimMetadata) (*model.ActivityCashbackClaim, error) {
	claim := &model.ActivityCashbackClaim{
		UserID:         userID,
		ClaimType:      claimType,
		TotalAmountUSD: amountUSD,
		TotalAmountSOL: amountSOL,
		Status:         model.ClaimStatusPending,
		Metadata:       metadata,
	}

	if err := s.claimRepo.Create(ctx, claim); err != nil {
		return nil, fmt.Errorf("failed to create claim: %w", err)
	}

	global.GVA_LOG.Info("Cashback claim created",
		zap.String("claim_id", claim.ID.String()),
		zap.String("user_id", userID.String()),
		zap.String("claim_type", string(claimType)),
		zap.String("amount_usd", amountUSD.String()))

	return claim, nil
}

// ProcessClaim processes a pending claim
func (s *CashbackClaimService) ProcessClaim(ctx context.Context, claimID uuid.UUID) error {
	claim, err := s.claimRepo.GetByID(ctx, claimID)
	if err != nil {
		return fmt.Errorf("failed to get claim: %w", err)
	}

	if !claim.IsPending() {
		return fmt.Errorf("claim is not in pending status")
	}

	// Mark as processing
	claim.MarkAsProcessing()
	if err := s.claimRepo.Update(ctx, claim); err != nil {
		return fmt.Errorf("failed to update claim status: %w", err)
	}

	global.GVA_LOG.Info("Claim processing started",
		zap.String("claim_id", claimID.String()))

	// Here you would integrate with blockchain/payment processing
	// For now, we'll simulate processing
	return nil
}

// CompleteClaim marks a claim as completed with transaction hash
func (s *CashbackClaimService) CompleteClaim(ctx context.Context, claimID uuid.UUID, transactionHash string) error {
	claim, err := s.claimRepo.GetByID(ctx, claimID)
	if err != nil {
		return fmt.Errorf("failed to get claim: %w", err)
	}

	if !claim.IsProcessing() {
		return fmt.Errorf("claim is not in processing status")
	}

	// Mark as completed
	claim.MarkAsCompleted(transactionHash)
	if err := s.claimRepo.Update(ctx, claim); err != nil {
		return fmt.Errorf("failed to update claim status: %w", err)
	}

	global.GVA_LOG.Info("Claim completed",
		zap.String("claim_id", claimID.String()),
		zap.String("transaction_hash", transactionHash))

	return nil
}

// FailClaim marks a claim as failed with error details
func (s *CashbackClaimService) FailClaim(ctx context.Context, claimID uuid.UUID, errorDetails map[string]interface{}) error {
	claim, err := s.claimRepo.GetByID(ctx, claimID)
	if err != nil {
		return fmt.Errorf("failed to get claim: %w", err)
	}

	// Mark as failed
	claim.MarkAsFailed(errorDetails)
	if err := s.claimRepo.Update(ctx, claim); err != nil {
		return fmt.Errorf("failed to update claim status: %w", err)
	}

	global.GVA_LOG.Error("Claim failed",
		zap.String("claim_id", claimID.String()),
		zap.Any("error_details", errorDetails))

	return nil
}

// GetUserClaims retrieves claims for a user with pagination
func (s *CashbackClaimService) GetUserClaims(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error) {
	claims, err := s.claimRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get user claims: %w", err)
	}
	return claims, nil
}

// GetClaimByID retrieves a specific claim by ID
func (s *CashbackClaimService) GetClaimByID(ctx context.Context, claimID uuid.UUID) (*model.ActivityCashbackClaim, error) {
	claim, err := s.claimRepo.GetByID(ctx, claimID)
	if err != nil {
		return nil, fmt.Errorf("failed to get claim: %w", err)
	}
	return claim, nil
}

// GetPendingClaims retrieves all pending claims
func (s *CashbackClaimService) GetPendingClaims(ctx context.Context) ([]model.ActivityCashbackClaim, error) {
	claims, err := s.claimRepo.GetPendingClaims(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending claims: %w", err)
	}
	return claims, nil
}

// GetUserClaimHistory retrieves user claim history within a date range
func (s *CashbackClaimService) GetUserClaimHistory(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]model.ActivityCashbackClaim, error) {
	claims, err := s.claimRepo.GetUserClaimHistory(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get user claim history: %w", err)
	}
	return claims, nil
}

// GetTotalClaimedAmount retrieves total claimed amount for a user
func (s *CashbackClaimService) GetTotalClaimedAmount(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	amount, err := s.claimRepo.GetTotalClaimedAmount(ctx, userID)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total claimed amount: %w", err)
	}
	return decimal.NewFromFloat(amount), nil
}

// GetClaimStats retrieves claim statistics within a date range
func (s *CashbackClaimService) GetClaimStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	// Get claims by status
	pendingClaims, err := s.claimRepo.GetClaimsByStatus(ctx, model.ClaimStatusPending)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending claims: %w", err)
	}

	processingClaims, err := s.claimRepo.GetClaimsByStatus(ctx, model.ClaimStatusProcessing)
	if err != nil {
		return nil, fmt.Errorf("failed to get processing claims: %w", err)
	}

	completedClaims, err := s.claimRepo.GetClaimsByStatus(ctx, model.ClaimStatusCompleted)
	if err != nil {
		return nil, fmt.Errorf("failed to get completed claims: %w", err)
	}

	failedClaims, err := s.claimRepo.GetClaimsByStatus(ctx, model.ClaimStatusFailed)
	if err != nil {
		return nil, fmt.Errorf("failed to get failed claims: %w", err)
	}

	// Calculate totals
	var totalPendingAmount, totalProcessingAmount, totalCompletedAmount, totalFailedAmount decimal.Decimal

	for _, claim := range pendingClaims {
		totalPendingAmount = totalPendingAmount.Add(claim.TotalAmountUSD)
	}

	for _, claim := range processingClaims {
		totalProcessingAmount = totalProcessingAmount.Add(claim.TotalAmountUSD)
	}

	for _, claim := range completedClaims {
		totalCompletedAmount = totalCompletedAmount.Add(claim.TotalAmountUSD)
	}

	for _, claim := range failedClaims {
		totalFailedAmount = totalFailedAmount.Add(claim.TotalAmountUSD)
	}

	stats := map[string]interface{}{
		"pending_count":     len(pendingClaims),
		"processing_count":  len(processingClaims),
		"completed_count":   len(completedClaims),
		"failed_count":      len(failedClaims),
		"pending_amount":    totalPendingAmount,
		"processing_amount": totalProcessingAmount,
		"completed_amount":  totalCompletedAmount,
		"failed_amount":     totalFailedAmount,
		"total_claims":      len(pendingClaims) + len(processingClaims) + len(completedClaims) + len(failedClaims),
		"total_amount":      totalPendingAmount.Add(totalProcessingAmount).Add(totalCompletedAmount).Add(totalFailedAmount),
	}

	return stats, nil
}
