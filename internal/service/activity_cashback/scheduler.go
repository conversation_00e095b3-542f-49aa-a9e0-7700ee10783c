package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// SchedulerService handles scheduled tasks for activity cashback system
type SchedulerService struct {
	service          ActivityCashbackServiceInterface
	processorManager *TaskProcessorManager
	stopChan         chan struct{}
	isRunning        bool
}

// NewSchedulerService creates a new SchedulerService
func NewSchedulerService() *SchedulerService {
	service := NewActivityCashbackService()
	processorManager := NewTaskProcessorManager(service)

	return &SchedulerService{
		service:          service,
		processorManager: processorManager,
		stopChan:         make(chan struct{}),
		isRunning:        false,
	}
}

// Start starts the scheduler
func (s *SchedulerService) Start() {
	if s.isRunning {
		global.GVA_LOG.Warn("Scheduler is already running")
		return
	}

	s.isRunning = true
	global.GVA_LOG.Info("Activity Cashback Scheduler started")

	// Start daily reset job
	go s.runDailyResetJob()

	// Start weekly reset job
	go s.runWeeklyResetJob()

	// Start monthly reset job
	go s.runMonthlyResetJob()

	// Start tier upgrade check job
	go s.runTierUpgradeCheckJob()

	// Start cashback processing job
	go s.runCashbackProcessingJob()
}

// Stop stops the scheduler
func (s *SchedulerService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.stopChan)
	global.GVA_LOG.Info("Activity Cashback Scheduler stopped")
}

// runDailyResetJob runs daily task reset at UTC 00:00
func (s *SchedulerService) runDailyResetJob() {
	ticker := time.NewTicker(1 * time.Hour) // Check every hour
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			now := time.Now().UTC()
			// Run at 00:00 UTC
			if now.Hour() == 0 && now.Minute() < 5 {
				s.resetDailyTasks()
			}
		}
	}
}

// runWeeklyResetJob runs weekly task reset on Mondays at UTC 00:00
func (s *SchedulerService) runWeeklyResetJob() {
	ticker := time.NewTicker(1 * time.Hour) // Check every hour
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			now := time.Now().UTC()
			// Run on Mondays at 00:00 UTC
			if now.Weekday() == time.Monday && now.Hour() == 0 && now.Minute() < 5 {
				s.resetWeeklyTasks()
			}
		}
	}
}

// runMonthlyResetJob runs monthly task reset on the 1st of each month at UTC 00:00
func (s *SchedulerService) runMonthlyResetJob() {
	ticker := time.NewTicker(1 * time.Hour) // Check every hour
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			now := time.Now().UTC()
			// Run on the 1st of each month at 00:00 UTC
			if now.Day() == 1 && now.Hour() == 0 && now.Minute() < 5 {
				s.resetMonthlyTasks()
				s.resetMonthlyStats()
			}
		}
	}
}

// runTierUpgradeCheckJob checks for tier upgrades every 10 minutes
func (s *SchedulerService) runTierUpgradeCheckJob() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkTierUpgrades()
		}
	}
}

// runCashbackProcessingJob processes pending cashback claims every 5 minutes
func (s *SchedulerService) runCashbackProcessingJob() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.processPendingClaims()
		}
	}
}

// resetDailyTasks resets all daily tasks
func (s *SchedulerService) resetDailyTasks() {
	ctx := context.Background()

	global.GVA_LOG.Info("Starting daily task reset")

	if err := s.service.ResetDailyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Daily task reset completed successfully")
}

// resetWeeklyTasks resets all weekly tasks
func (s *SchedulerService) resetWeeklyTasks() {
	ctx := context.Background()

	global.GVA_LOG.Info("Starting weekly task reset")

	if err := s.service.ResetWeeklyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset weekly tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Weekly task reset completed successfully")
}

// resetMonthlyTasks resets all monthly tasks
func (s *SchedulerService) resetMonthlyTasks() {
	ctx := context.Background()

	global.GVA_LOG.Info("Starting monthly task reset")

	if err := s.service.ResetMonthlyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Monthly task reset completed successfully")
}

// resetMonthlyStats resets monthly statistics for all users
func (s *SchedulerService) resetMonthlyStats() {
	ctx := context.Background()

	global.GVA_LOG.Info("Starting monthly stats reset")

	if err := s.service.ResetMonthlyStats(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly stats", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Monthly stats reset completed successfully")
}

// checkTierUpgrades checks for users eligible for tier upgrades
func (s *SchedulerService) checkTierUpgrades() {
	// This would typically get a list of users who might be eligible for upgrades
	// For now, we'll skip this as it would require a more complex implementation
	// In production, you might want to:
	// 1. Get users who have gained points recently
	// 2. Check their tier eligibility
	// 3. Upgrade them if eligible

	global.GVA_LOG.Debug("Tier upgrade check completed")
}

// processPendingClaims processes pending cashback claims
func (s *SchedulerService) processPendingClaims() {
	ctx := context.Background()

	// Get pending claims
	claims, err := s.service.GetPendingClaims(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending claims", zap.Error(err))
		return
	}

	if len(claims) == 0 {
		return // No pending claims
	}

	global.GVA_LOG.Info("Processing pending claims", zap.Int("count", len(claims)))

	for _, claim := range claims {
		// Process each claim
		if err := s.service.ProcessClaim(ctx, claim.ID); err != nil {
			global.GVA_LOG.Error("Failed to process claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
			continue
		}

		// In a real implementation, you would:
		// 1. Integrate with blockchain/payment system
		// 2. Create actual transactions
		// 3. Update claim status based on transaction result

		// For now, we'll simulate successful processing
		mockTxHash := "0x" + claim.ID.String()[:40] // Mock transaction hash
		if err := s.service.CompleteClaim(ctx, claim.ID, mockTxHash); err != nil {
			global.GVA_LOG.Error("Failed to complete claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
		}
	}

	global.GVA_LOG.Info("Pending claims processing completed", zap.Int("processed", len(claims)))
}

// ProcessUserActivity processes user activity events
func (s *SchedulerService) ProcessUserActivity(ctx context.Context, userID string, activityType string, data map[string]interface{}) error {
	// This method can be called from other parts of the system to process user activities

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	switch activityType {
	case "trade":
		// Process trading activity
		return s.processorManager.ProcessTradingEvent(ctx, userUUID, data)
	case "login":
		// Process login activity (daily check-in)
		return s.processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDDailyCheckin, "daily", data)
	case "market_check":
		// Process market check activity
		return s.processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDMarketPageView, "daily", data)
	default:
		global.GVA_LOG.Warn("Unknown activity type", zap.String("type", activityType))
	}

	return nil
}

// GetSchedulerStatus returns the current status of the scheduler
func (s *SchedulerService) GetSchedulerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running": s.isRunning,
		"start_time": time.Now().UTC(),
		"jobs": []string{
			"daily_reset",
			"weekly_reset",
			"monthly_reset",
			"tier_upgrade_check",
			"cashback_processing",
		},
	}
}
