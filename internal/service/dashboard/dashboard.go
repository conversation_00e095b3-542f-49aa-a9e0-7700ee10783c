package dashboard

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/infinite_agent"
	"gorm.io/gorm"
)

// DashboardService provides administrative functions for agent management
type DashboardService struct {
	levelRepo         repo.LevelRepo
	infiniteAgentRepo infinite_agent.InfiniteAgentConfigI
}

// Ensure DashboardService implements DashboardI interface
var _ DashboardI = (*DashboardService)(nil)

// NewDashboardService creates a new instance of DashboardService
func NewDashboardService() *DashboardService {
	return &DashboardService{
		levelRepo:         repo.NewLevelRepository(),
		infiniteAgentRepo: infinite_agent.NewInfiniteAgentConfigRepository(global.GVA_DB),
	}
}

// GetUserReferralSnapshot retrieves a user's referral snapshot information
func (s *DashboardService) GetUserReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	var snapshot model.ReferralSnapshot

	err := global.GVA_DB.WithContext(ctx).
		Preload("User").
		Preload("L1Upline").
		Preload("L2Upline").
		Preload("L3Upline").
		Where("user_id = ?", userID).
		First(&snapshot).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("referral snapshot not found for user %s", userID)
		}
		return nil, fmt.Errorf("failed to get referral snapshot: %w", err)
	}

	return &snapshot, nil
}

// GetAllAgentLevels retrieves all agent levels
func (s *DashboardService) GetAllAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevels(ctx)
}

// GetTransactionStats retrieves transaction data statistics including daily_meme_volume and daily_user_volume
func (s *DashboardService) GetTransactionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (*TransactionStats, error) {
	// Get MEME transaction volume
	var memeVolume decimal.Decimal
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.DailyMemeVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(meme_volume_usd), 0)").
		Scan(&memeVolume).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get MEME volume: %w", err)
	}

	// Get contract transaction volume
	var contractVolume decimal.Decimal
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.DailyUserVolume{}).
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Select("COALESCE(SUM(contract_volume_usd), 0)").
		Scan(&contractVolume).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get contract volume: %w", err)
	}

	return &TransactionStats{
		MemeVolumeUSD:     memeVolume,
		ContractVolumeUSD: contractVolume,
		TotalVolumeUSD:    memeVolume.Add(contractVolume),
		Period: Period{
			StartDate: startDate,
			EndDate:   endDate,
		},
	}, nil
}

// GetUserReferralRecords retrieves user invitation records
func (s *DashboardService) GetUserReferralRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*ReferralRecordsResponse, error) {
	var referrals []model.Referral
	var total int64

	// Get total count
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id = ?", userID).
		Count(&total).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get referral count: %w", err)
	}

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get referrals with pagination
	err = global.GVA_DB.WithContext(ctx).
		Preload("User").
		Where("referrer_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&referrals).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get referrals: %w", err)
	}

	return &ReferralRecordsResponse{
		Referrals: referrals,
		Pagination: Pagination{
			Page:       page,
			PageSize:   pageSize,
			Total:      int(total),
			TotalPages: (int(total) + pageSize - 1) / pageSize,
		},
	}, nil
}

// GetAllInfiniteAgents retrieves all infinite agents
func (s *DashboardService) GetAllInfiniteAgents(ctx context.Context, page, pageSize int) ([]*model.InfiniteAgentConfig, error) {
	return s.infiniteAgentRepo.GetAllWithUser(ctx)
}

// GetInfiniteAgentByUserID retrieves infinite agent configuration by user ID
func (s *DashboardService) GetInfiniteAgentByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error) {
	return s.infiniteAgentRepo.GetByUserIDWithUser(ctx, userID)
}

// Response structures

// UserInvitationStats represents user invitation statistics
type UserInvitationStats struct {
	DirectReferralCount   int             `json:"direct_referral_count"`
	IndirectReferralCount int             `json:"indirect_referral_count"`
	ExtendedReferralCount int             `json:"extended_referral_count"`
	TotalDownlineCount    int             `json:"total_downline_count"`
	TradingUserCount      int             `json:"trading_user_count"`
	TotalVolumeUSD        decimal.Decimal `json:"total_volume_usd"`
}

// UserRewardData represents user reward data
type UserRewardData struct {
	ReferralSnapshot      *model.ReferralSnapshot `json:"referral_snapshot"`
	TotalCommissionEarned decimal.Decimal         `json:"total_commission_earned"`
	TotalCashbackEarned   decimal.Decimal         `json:"total_cashback_earned"`
}

// TransactionStats represents transaction statistics
type TransactionStats struct {
	MemeVolumeUSD     decimal.Decimal `json:"meme_volume_usd"`
	ContractVolumeUSD decimal.Decimal `json:"contract_volume_usd"`
	TotalVolumeUSD    decimal.Decimal `json:"total_volume_usd"`
	Period            Period          `json:"period"`
}

// PlatformTransactionStats represents platform-wide transaction statistics
type PlatformTransactionStats struct {
	TotalMemeVolume     decimal.Decimal `json:"total_meme_volume"`
	TotalContractVolume decimal.Decimal `json:"total_contract_volume"`
	TotalVolume         decimal.Decimal `json:"total_volume"`
	MemeUserCount       int             `json:"meme_user_count"`
	ContractUserCount   int             `json:"contract_user_count"`
	Period              Period          `json:"period"`
}

// ReferralRecordsResponse represents referral records response with pagination
type ReferralRecordsResponse struct {
	Referrals  []model.Referral `json:"referrals"`
	Pagination Pagination       `json:"pagination"`
}

// Period represents a time period
type Period struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}
