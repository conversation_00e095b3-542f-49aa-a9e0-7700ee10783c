package dashboard

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// DashboardI defines the interface for admin service
type DashboardI interface {
	// GetUserReferralSnapshot retrieves a user's referral snapshot information
	GetUserReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)

	// GetAllAgentLevels retrieves all agent levels
	GetAllAgentLevels(ctx context.Context) ([]model.AgentLevel, error)

	// GetTransactionStats retrieves transaction data statistics including daily_meme_volume and daily_user_volume
	GetTransactionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (*TransactionStats, error)

	// GetUserReferralRecords retrieves user invitation records
	GetUserReferralRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*ReferralRecordsResponse, error)

	// GetAllInfiniteAgents retrieves all infinite agents
	GetAllInfiniteAgents(ctx context.Context, page, pageSize int) ([]*model.InfiniteAgentConfig, error)

	// GetInfiniteAgentByUserID retrieves infinite agent configuration by user ID
	GetInfiniteAgentByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error)
}
