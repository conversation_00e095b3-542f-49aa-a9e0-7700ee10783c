package reward

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type RewardDataService struct {
	userRepo       repo.InvitationRepo
	commissionRepo transaction.CommissionLedgerRepositoryInterface
	affiliateRepo  transaction.AffiliateTransactionRepositoryInterface
	levelRepo      repo.LevelRepo
}

func NewRewardDataService() *RewardDataService {
	return &RewardDataService{
		userRepo:       &agent_referral.InvitationRepository{},
		commissionRepo: transaction.NewCommissionLedgerRepository(),
		affiliateRepo:  transaction.NewAffiliateTransactionRepository(),
		levelRepo:      repo.NewLevelRepository(),
	}
}

type RewardData struct {
	AccumulatedRewards        decimal.Decimal `json:"accumulated_rewards"`
	USDCRebate                decimal.Decimal `json:"usdc_rebate"`
	SOLRebate                 decimal.Decimal `json:"sol_rebate"`
	InvitationTransactionRank int             `json:"invitation_transaction_rank"`
	InvitationRevenueRank     int             `json:"invitation_revenue_rank"`
	ProgressPrompt            string          `json:"progress_prompt"`
}

func (s *RewardDataService) GetRewardData(ctx context.Context, userID uuid.UUID) (*RewardData, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	accumulatedRewards, err := s.calculateAccumulatedRewards(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate accumulated rewards", zap.Error(err))
		return nil, fmt.Errorf("failed to calculate accumulated rewards: %w", err)
	}

	usdcRebate, err := s.getUSDCRebate(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get USDC rebate", zap.Error(err))
		return nil, fmt.Errorf("failed to get USDC rebate: %w", err)
	}

	solRebate, err := s.getSOLRebate(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get SOL rebate", zap.Error(err))
		return nil, fmt.Errorf("failed to get SOL rebate: %w", err)
	}

	invitationTransactionRank, err := s.calculateInvitationTransactionRank(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate invitation transaction rank", zap.Error(err))
		return nil, fmt.Errorf("failed to calculate invitation transaction rank: %w", err)
	}

	invitationRevenueRank, err := s.calculateInvitationRevenueRank(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate invitation revenue rank", zap.Error(err))
		return nil, fmt.Errorf("failed to calculate invitation revenue rank: %w", err)
	}

	progressPrompt, err := s.generateProgressPrompt(ctx, user)
	if err != nil {
		global.GVA_LOG.Error("Failed to generate progress prompt", zap.Error(err))
		return nil, fmt.Errorf("failed to generate progress prompt: %w", err)
	}

	return &RewardData{
		AccumulatedRewards:        accumulatedRewards,
		USDCRebate:                usdcRebate,
		SOLRebate:                 solRebate,
		InvitationTransactionRank: invitationTransactionRank,
		InvitationRevenueRank:     invitationRevenueRank,
		ProgressPrompt:            progressPrompt,
	}, nil
}

func (s *RewardDataService) calculateAccumulatedRewards(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	claimedAmount, err := s.commissionRepo.GetClaimedAmountByUserID(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	pendingAmount, err := s.commissionRepo.GetPendingClaimAmountByUserID(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	return claimedAmount.Add(pendingAmount), nil
}

func (s *RewardDataService) getUSDCRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	claimedUSDC, err := s.commissionRepo.GetClaimedAmountByUserIDAndType(ctx, userID, "USDC")
	if err != nil {
		return decimal.Zero, err
	}

	pendingUSDC, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndType(ctx, userID, "USDC")
	if err != nil {
		return decimal.Zero, err
	}

	return claimedUSDC.Add(pendingUSDC), nil
}

func (s *RewardDataService) getSOLRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	claimedSOL, err := s.commissionRepo.GetClaimedAmountByUserIDAndType(ctx, userID, "SOL")
	if err != nil {
		return decimal.Zero, err
	}

	pendingSOL, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndType(ctx, userID, "SOL")
	if err != nil {
		return decimal.Zero, err
	}

	return claimedSOL.Add(pendingSOL), nil
}

func (s *RewardDataService) calculateInvitationTransactionRank(ctx context.Context, userID uuid.UUID) (int, error) {
	var rank int
	err := global.GVA_DB.WithContext(ctx).
		Raw(`
			WITH user_volumes AS (
				SELECT
					u.id,
					COALESCE(rs.total_volume_usd, 0) as total_volume
				FROM users u
				LEFT JOIN referral_snapshots rs ON u.id = rs.user_id
			),
			ranked_users AS (
				SELECT
					id,
					RANK() OVER (ORDER BY total_volume DESC) as rank
				FROM user_volumes
			)
			SELECT rank FROM ranked_users WHERE id = ?
		`, userID).
		Scan(&rank).Error

	if err != nil {
		return 0, err
	}

	return rank, nil
}

func (s *RewardDataService) calculateInvitationRevenueRank(ctx context.Context, userID uuid.UUID) (int, error) {
	var rank int
	err := global.GVA_DB.WithContext(ctx).
		Raw(`
			WITH user_revenues AS (
				SELECT
					u.id,
					COALESCE(rs.total_rewards_distributed, 0) as total_revenue
				FROM users u
				LEFT JOIN referral_snapshots rs ON u.id = rs.user_id
			),
			ranked_users AS (
				SELECT
					id,
					RANK() OVER (ORDER BY total_revenue DESC) as rank
				FROM user_revenues
			)
			SELECT rank FROM ranked_users WHERE id = ?
		`, userID).
		Scan(&rank).Error

	if err != nil {
		return 0, err
	}

	return rank, nil
}

func (s *RewardDataService) generateProgressPrompt(ctx context.Context, user *model.User) (string, error) {
	nextLevel, err := s.levelRepo.GetAgentLevelByID(ctx, user.AgentLevelID+1)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "You have reached the highest level", nil
		}
		return "", err
	}

	currentVolume, err := s.getUserCurrentVolume(ctx, user.ID)
	if err != nil {
		return "", err
	}

	requiredVolume := nextLevel.MemeVolumeThreshold.Sub(currentVolume)
	if requiredVolume.LessThanOrEqual(decimal.Zero) {
		return "You have met the upgrade requirements", nil
	}

	requiredVolumeFloat, _ := requiredVolume.Float64()
	return fmt.Sprintf("%.2f", requiredVolumeFloat), nil
}

func (s *RewardDataService) getUserCurrentVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)

	userRepo := transaction.NewUserRepository()
	downlineUsers, err := userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		return decimal.Zero, err
	}

	allUsers := append([]uuid.UUID{userID}, downlineUsers...)

	memeVolume, err := s.affiliateRepo.GetVolumeByUserIDsAndPeriod(ctx, allUsers, thirtyDaysAgo, time.Now())
	if err != nil {
		return decimal.Zero, err
	}

	return memeVolume, nil
}
