package app

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"
)

func InitializeOnchainCrawler() {
	// Initialize the onchain crawler application
	// Crawler hyperliquid builder transaction

	// CrawlHyperLiquidBuilderTransaction

	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_SENSITIVE_CONFIG = initializer.LoadSensitiveConfig()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	hyperLiquidService := service.NewHyperLiquidService()

	go hyperLiquidService.CrawlHyperLiquidBuilderTransaction()

	select {}
}
