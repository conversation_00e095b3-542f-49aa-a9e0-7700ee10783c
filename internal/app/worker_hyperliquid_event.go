package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"

	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

func InitializeHyperLiquidEventWorker() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	initializer.InitNatsDex()

	dexNats := global.GVA_NATS_DEX
	err := dexNats.EnsureReadonlyStreamExists(natsClient.DexHyperLiquidTransactionStream)
	if err != nil {
		panic(fmt.Sprintf("Failed to ensure readonly stream %s exists: %v", natsClient.DexHyperLiquidTransactionStream, err))
	}

	go runSyncHyperLiquidEventNatsWorker()
	select {}
}

func runSyncHyperLiquidEventNatsWorker() error {
	logger := global.GVA_LOG

	nc := global.GVA_NATS_DEX

	subject := string(task.HyperLiquidTransactionEventSubject)
	opts := []nats.SubOpt{
		nats.BindStream(natsClient.DexHyperLiquidTransactionStream),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
	}
	jsConsumer, err := nc.PullSubscribe(subject, natsClient.DexHyperLiquidTransactionConsumer, opts...)
	if err != nil {
		logger.Error("Could not subscribe to subject", zap.String("Subject", subject), zap.Error(err))
		return err
	}

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	for {
		startTime := time.Now()
		msgs, err := jsConsumer.Fetch(100, nats.MaxWait(2*time.Second))

		if errors.Is(err, nats.ErrTimeout) {
			logger.Info("No messages available at the moment")
			continue
		}

		if err != nil {
			logger.Error("failed to fetch msgs:", zap.Error(err))
			continue
		}

		if len(msgs) == 0 {
			logger.Info("No messages fetched", zap.String("Subject", subject))
			continue
		}

		err = task.ConsumeHyperLiquidTransactionEvent(msgs)

		for _, m := range msgs {
			if err == nil {
				if ackErr := m.Ack(); ackErr != nil {
					logger.Error("failed to ack message", zap.Error(ackErr))
				}
			} else {
				if nakErr := m.Nak(); nakErr != nil {
					logger.Error("failed to nak message", zap.Error(nakErr))
				}
			}
		}

		elapsed := time.Since(startTime)
		logger.Info("Worker process completed",
			zap.String("Subject", subject),
			zap.Duration("duration", elapsed),
			zap.Int("MessageCount", len(msgs)))
	}
}
