# Production Environment Configuration
# This file contains environment variables for production deployment
# IMPORTANT: Update all sensitive values before deploying to production

# Application Configuration
APP_ENV=production
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL)
# IMPORTANT: Update these with your production database credentials
POSTGRES_AGENCY_HOST=
POSTGRES_AGENCY_PORT=
POSTGRES_AGENCY_USER=
POSTGRES_AGENCY_PASS=
POSTGRES_AGENCY_SSL_MODE=
POSTGRES_DB=

# Database URL for migrations
DATABASE_URL=***************************************************************************************/agent?sslmode=enable

# Redis Configuration (Optional)
REDIS_HOST=
REDIS_PORT=
REDIS_PASS=
REDIS_DB=

# JWT Configuration
# IMPORTANT: Generate a strong secret key for production
JWT_SECRET=
JWT_EXPIRES_TIME=
JWT_BUFFER_TIME=
JWT_ISSUER=

# Admin Configuration
# IMPORTANT: Generate a strong API key for production
INTERNAL_API_KEY=

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_DIRECTOR=log
LOG_SHOW_LINE=false
LOG_IN_CONSOLE=false

# CORS Configuration
# IMPORTANT: Restrict origins to your frontend domains
CORS_ALLOW_ORIGINS=
CORS_ALLOW_METHODS=
CORS_ALLOW_HEADERS=
CORS_ALLOW_CREDENTIALS=

# Rate Limiting
RATE_LIMIT_COUNT=10000
RATE_LIMIT_TIME=3600

# Production Settings
DEBUG=false
ENABLE_PLAYGROUND=false

# Level downgrade configuration (disabled for first 6 months after launch)
ENABLE_LEVEL_DOWNGRADE=false

# SSL/TLS Configuration (if applicable)
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# Monitoring and Observability (Optional)
# SENTRY_DSN=your-sentry-dsn
# DATADOG_API_KEY=your-datadog-api-key

SYSTEM_LAUNCH_DATE=2025-08-16