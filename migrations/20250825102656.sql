-- Modify "referral_snapshots" table
ALTER TABLE "public"."referral_snapshots" ADD COLUMN "trading_user_count" bigint NULL DEFAULT 0, ADD COLUMN "total_perps_volume_usd" numeric(38,6) NULL DEFAULT 0, ADD COLUMN "total_perps_fees" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_perps_fees_paid" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_perps_fees_un_paid" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_perps_trades_count" bigint NULL DEFAULT 0, ADD COLUMN "total_meme_volume_usd" numeric(38,6) NULL DEFAULT 0, ADD COLUMN "total_meme_fees" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_meme_fees_paid" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_meme_fees_un_paid" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_meme_trades_count" bigint NULL DEFAULT 0, ADD COLUMN "total_commission_earned_usd" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "claimed_commission_usd" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "unclaimed_commission_usd" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "total_cashback_earned_usd" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "claimed_cashback_usd" numeric(38,18) NULL DEFAULT 0, ADD COLUMN "unclaimed_cashback_usd" numeric(38,18) NULL DEFAULT 0;
