-- Create "users" table
CREATE TABLE "public"."users" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "email" text NULL,
  "is_first_login" boolean NULL DEFAULT true,
  "is_exported_wallet" boolean NULL DEFAULT false,
  "invitation_code" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_users_email" UNIQUE ("email")
);
-- Create index "idx_users_deleted_at" to table: "users"
CREATE INDEX "idx_users_deleted_at" ON "public"."users" ("deleted_at");
-- Create "referral_snapshots" table
CREATE TABLE "public"."referral_snapshots" (
  "user_id" uuid NOT NULL,
  "direct_count" bigint NULL DEFAULT 0,
  "total_downline_count" bigint NULL DEFAULT 0,
  "total_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "total_rewards_distributed" numeric(38,6) NULL DEFAULT 0,
  PRIMARY KEY ("user_id"),
  CONSTRAINT "fk_users_referral_snapshot" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create "referrals" table
CREATE TABLE "public"."referrals" (
  "id" bigserial NOT NULL,
  "user_id" uuid NOT NULL,
  "referrer_id" uuid NULL,
  "depth" bigint NULL DEFAULT 1,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_referrals_user_id" UNIQUE ("user_id"),
  CONSTRAINT "fk_users_referral" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_users_referrals" FOREIGN KEY ("referrer_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_referrals_referrer_id" to table: "referrals"
CREATE INDEX "idx_referrals_referrer_id" ON "public"."referrals" ("referrer_id");
-- Create index "idx_referrals_user_id" to table: "referrals"
CREATE INDEX "idx_referrals_user_id" ON "public"."referrals" ("user_id");
-- Create "user_wallets" table
CREATE TABLE "public"."user_wallets" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "chain" text NOT NULL,
  "name" text NULL,
  "wallet_address" text NOT NULL,
  "wallet_id" uuid NULL,
  "wallet_account_id" uuid NULL,
  "wallet_type" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_users_wallets" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "chk_user_wallets_wallet_type" CHECK (wallet_type = ANY (ARRAY['embedded'::text, 'managed'::text]))
);
-- Create index "idx_user_wallets_deleted_at" to table: "user_wallets"
CREATE INDEX "idx_user_wallets_deleted_at" ON "public"."user_wallets" ("deleted_at");
-- Create index "idx_user_wallets_user_id" to table: "user_wallets"
CREATE INDEX "idx_user_wallets_user_id" ON "public"."user_wallets" ("user_id");
