-- Create "sol_price_snapshots" table
CREATE TABLE "public"."sol_price_snapshots" (
  "id" bigserial NOT NULL,
  "price" numeric(36,18) NOT NULL,
  "symbol" character varying(20) NOT NULL,
  "chain_id" character varying(50) NOT NULL,
  "address" character varying(100) NOT NULL,
  "timestamp" timestamptz NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);
-- Create index "idx_sol_price_snapshots_symbol" to table: "sol_price_snapshots"
CREATE INDEX "idx_sol_price_snapshots_symbol" ON "public"."sol_price_snapshots" ("symbol");
-- <PERSON>reate index "idx_sol_price_snapshots_timestamp" to table: "sol_price_snapshots"
CREATE INDEX "idx_sol_price_snapshots_timestamp" ON "public"."sol_price_snapshots" ("timestamp");
-- Create "affiliate_transactions" table
CREATE TABLE "public"."affiliate_transactions" (
  "id" bigserial NOT NULL,
  "order_id" uuid NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "transaction_type" character varying(10) NOT NULL,
  "type" character varying(20) NOT NULL,
  "chain_id" character varying(50) NOT NULL,
  "base_address" character varying(100) NOT NULL,
  "base_symbol" character varying(20) NOT NULL,
  "quote_address" character varying(100) NOT NULL,
  "quote_symbol" character varying(20) NOT NULL,
  "user_id" uuid NOT NULL,
  "user_address" character varying(100) NOT NULL,
  "base_amount" numeric(36,18) NOT NULL,
  "quote_amount" numeric(36,18) NOT NULL,
  "total_fee" numeric(36,18) NOT NULL,
  "slippage" numeric(10,6) NOT NULL,
  "status" character varying(20) NOT NULL,
  "tx_hash" character varying(100) NOT NULL,
  "mev_protect" boolean NULL DEFAULT false,
  "referrer_id" uuid NULL,
  "referral_depth" bigint NULL DEFAULT 0,
  "commission_rate" numeric(10,6) NULL DEFAULT 0,
  "commission_amount" numeric(36,18) NULL DEFAULT 0,
  "commission_paid" boolean NULL DEFAULT false,
  "commission_paid_at" timestamptz NULL,
  "volume_usd" numeric(36,18) NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_affiliate_transactions_order_id" UNIQUE ("order_id"),
  CONSTRAINT "uni_affiliate_transactions_tx_hash" UNIQUE ("tx_hash"),
  CONSTRAINT "fk_affiliate_transactions_referrer" FOREIGN KEY ("referrer_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_affiliate_transactions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_affiliate_transactions_base_symbol" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_base_symbol" ON "public"."affiliate_transactions" ("base_symbol");
-- Create index "idx_affiliate_transactions_chain_id" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_chain_id" ON "public"."affiliate_transactions" ("chain_id");
-- Create index "idx_affiliate_transactions_commission_paid" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_commission_paid" ON "public"."affiliate_transactions" ("commission_paid");
-- Create index "idx_affiliate_transactions_created_at" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_created_at" ON "public"."affiliate_transactions" ("created_at");
-- Create index "idx_affiliate_transactions_deleted_at" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_deleted_at" ON "public"."affiliate_transactions" ("deleted_at");
-- Create index "idx_affiliate_transactions_order_id" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_order_id" ON "public"."affiliate_transactions" ("order_id");
-- Create index "idx_affiliate_transactions_quote_symbol" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_quote_symbol" ON "public"."affiliate_transactions" ("quote_symbol");
-- Create index "idx_affiliate_transactions_referral_depth" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_referral_depth" ON "public"."affiliate_transactions" ("referral_depth");
-- Create index "idx_affiliate_transactions_referrer_id" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_referrer_id" ON "public"."affiliate_transactions" ("referrer_id");
-- Create index "idx_affiliate_transactions_status" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_status" ON "public"."affiliate_transactions" ("status");
-- Create index "idx_affiliate_transactions_transaction_type" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_transaction_type" ON "public"."affiliate_transactions" ("transaction_type");
-- Create index "idx_affiliate_transactions_tx_hash" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_tx_hash" ON "public"."affiliate_transactions" ("tx_hash");
-- Create index "idx_affiliate_transactions_user_address" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_user_address" ON "public"."affiliate_transactions" ("user_address");
-- Create index "idx_affiliate_transactions_user_id" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_user_id" ON "public"."affiliate_transactions" ("user_id");
-- Create index "idx_affiliate_transactions_volume_usd" to table: "affiliate_transactions"
CREATE INDEX "idx_affiliate_transactions_volume_usd" ON "public"."affiliate_transactions" ("volume_usd");
