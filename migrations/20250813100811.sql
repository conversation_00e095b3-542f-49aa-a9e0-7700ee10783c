-- Create "daily_meme_volumes" table
CREATE TABLE "public"."daily_meme_volumes" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "date" date NOT NULL,
  "meme_volume_usd" numeric(38,2) NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
-- Create "meme_cashback" table
CREATE TABLE "public"."meme_cashback" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "user_address" character varying(100) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING_CLAIM',
  "affiliate_transaction_id" bigint NOT NULL,
  "sol_price_usd" numeric(36,18) NOT NULL,
  "cashback_amount_usd" numeric(36,18) NOT NULL,
  "cashback_amount_sol" numeric(36,18) NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "claimed_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_meme_cashback_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_meme_cashback_affiliate_transaction_id" to table: "meme_cashback"
CREATE INDEX "idx_meme_cashback_affiliate_transaction_id" ON "public"."meme_cashback" ("affiliate_transaction_id");
-- Create index "idx_meme_cashback_status" to table: "meme_cashback"
CREATE INDEX "idx_meme_cashback_status" ON "public"."meme_cashback" ("status");
-- Create index "idx_meme_cashback_user_address" to table: "meme_cashback"
CREATE INDEX "idx_meme_cashback_user_address" ON "public"."meme_cashback" ("user_address");
-- Create index "idx_meme_cashback_user_id" to table: "meme_cashback"
CREATE INDEX "idx_meme_cashback_user_id" ON "public"."meme_cashback" ("user_id");
