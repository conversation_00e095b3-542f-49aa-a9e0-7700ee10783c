-- Create "builder_crawler_histories" table
CREATE TABLE "public"."builder_crawler_histories" (
  "id" uuid NOT NULL,
  "builder_address" character varying(255) NOT NULL,
  "date" timestamptz NULL,
  "last_index" bigint NULL,
  "url" character varying(511) NOT NULL,
  "is_ended" boolean NULL DEFAULT false,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);
-- <PERSON>reate index "idx_builder_address" to table: "builder_crawler_histories"
CREATE INDEX "idx_builder_address" ON "public"."builder_crawler_histories" USING hash ("builder_address");
-- Create "hyper_liquid_builder_transactions" table
CREATE TABLE "public"."hyper_liquid_builder_transactions" (
  "id" uuid NOT NULL,
  "time" timestamp NOT NULL,
  "user" character varying(255) NOT NULL,
  "coin" character varying(255) NOT NULL,
  "side" character varying(55) NOT NULL,
  "price" double precision NULL,
  "size" double precision NULL,
  "crossed" boolean NOT NULL,
  "special_trade_type" character varying(255) NULL,
  "tif" character varying(255) NULL,
  "is_trigger" boolean NULL,
  "counterparty" character varying(255) NULL,
  "closed_pnl" double precision NULL,
  "twap_id" bigint NULL,
  "builder_fee" double precision NULL,
  "created_at" timestamptz NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_user_address" to table: "hyper_liquid_builder_transactions"
CREATE INDEX "idx_user_address" ON "public"."hyper_liquid_builder_transactions" USING hash ("user");
