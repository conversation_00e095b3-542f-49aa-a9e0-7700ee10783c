-- Create "hyper_liquid_transactions" table
CREATE TABLE "public"."hyper_liquid_transactions" (
  "cloid" text NOT NULL,
  "user_id" uuid NULL,
  "user_address" text NULL,
  "side" text NULL,
  "order_type" text NULL,
  "symbol" text NULL,
  "is_buy" boolean NULL,
  "leverage" bigint NULL,
  "margin" numeric(20,10) NULL,
  "is_market" boolean NULL,
  "trigger_px" text NULL,
  "tpsl" text NULL,
  "tif" text NULL,
  "base" text NULL,
  "quote" text NULL,
  "size" numeric(20,10) NULL,
  "price" numeric(20,10) NULL,
  "avg_price" numeric(20,10) NOT NULL,
  "build_fee" numeric(20,10) NULL,
  "total_fee" text NULL,
  "fee_bp" bigint NULL,
  "build_address" text NULL,
  "status" text NULL,
  "oid" bigint NULL,
  "created_at" text NULL,
  "total_sz" text NULL,
  "hash" text NULL,
  "asset" text NULL,
  "coin" text NOT NULL,
  "reduce_only" boolean NULL,
  "grouping" text NULL,
  "operation" text NULL
);
