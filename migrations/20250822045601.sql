-- Create "activity_cashback" table
CREATE TABLE "public"."activity_cashback" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "user_address" character varying(100) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING_CLAIM',
  "affiliate_transaction_id" bigint NOT NULL,
  "sol_price_usd" numeric(36,18) NOT NULL,
  "cashback_amount_usd" numeric(36,18) NOT NULL,
  "cashback_amount_sol" numeric(36,18) NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "claimed_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_cashback_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_activity_cashback_affiliate_transaction_id" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_affiliate_transaction_id" ON "public"."activity_cashback" ("affiliate_transaction_id");
-- Create index "idx_activity_cashback_status" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_status" ON "public"."activity_cashback" ("status");
-- Create index "idx_activity_cashback_user_address" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_user_address" ON "public"."activity_cashback" ("user_address");
-- Create index "idx_activity_cashback_user_id" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_user_id" ON "public"."activity_cashback" ("user_id");
-- Drop "meme_cashback" table
DROP TABLE "public"."meme_cashback";
