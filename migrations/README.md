#### Install atlas cli

- macOS
```
brew install ariga/tap/atlas
```

- linux
```
curl -sSf https://atlasgo.sh | sh
```

### Migration Strategy

This project uses **Atlas migrations** for database schema management across all environments.

### Migration Workflow

#### For All Environments (Atlas Migrations)
- Database schema is managed through Atlas migration files
- Consistent schema management across development, staging, and production
- Explicit version control for database changes

1. Write your model first, and add it to atlasloader (`cmd/atlasloader/main.go`)
2. Generate migration file
```bash
$ make db-diff
```

3. Apply your migration
```bash
$ make db-apply
# or directly with Atlas
$ make db-apply-atlas
```

#### For Production
- Migrations are applied via entrypoint.sh during container startup
- Or through CI/CD pipeline before deployment
- Migration files provide explicit control over schema changes

### Database Schema

This project implements a multi-level Agent Referral system and Activity Cashback system with the following tables:

#### users
- `id` (UUID, Primary Key)
- `email` (TEXT, Unique)
- `invitation_code` (TEXT, 5-15 characters, can be null initially)

#### user_wallets
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key to users.id)
- `chain` (TEXT, Not Null)
- `name` (TEXT)
- `wallet_address` (TEXT, Not Null)
- `wallet_id` (UUID)
- `wallet_account_id` (UUID)
- `wallet_type` (TEXT, CHECK: 'embedded' or 'managed')
- Unique constraint on (wallet_address, chain)

#### referrals
- `id` (SERIAL, Primary Key)
- `user_id` (UUID, Foreign Key to users.id, Unique)
- `referrer_id` (UUID, Foreign Key to users.id)
- `depth` (INT, Default: 1) - 1 means direct referral
- `created_at` (TIMESTAMP, Default: CURRENT_TIMESTAMP)

#### referral_snapshots
- `user_id` (UUID, Primary Key, Foreign Key to users.id)
- `direct_count` (INT, Default: 0)
- `total_downline_count` (INT, Default: 0)
- `total_volume_usd` (NUMERIC(38,2), Default: 0)
- `total_rewards_distributed` (NUMERIC(38,6), Default: 0)

### Sample Query: Get All Downlines (Recursive CTE)
```sql
WITH RECURSIVE downlines AS (
  SELECT user_id, referrer_id, 1 AS depth
  FROM referrals
  WHERE referrer_id = 'REFERRER-UUID-HERE'
  UNION ALL
  SELECT r.user_id, r.referrer_id, d.depth + 1
  FROM referrals r
  INNER JOIN downlines d ON r.referrer_id = d.user_id
)
SELECT * FROM downlines;
```
