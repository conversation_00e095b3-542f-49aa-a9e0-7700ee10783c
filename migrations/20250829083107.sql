-- Create "reward_claim_results" table
CREATE TABLE "public"."reward_claim_results" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "address" character varying(255) NOT NULL,
  "amount" numeric(38,6) NOT NULL,
  "token" character varying(50) NOT NULL,
  "chain_id" bigint NOT NULL,
  "result" character varying(20) NOT NULL,
  "error_message" text NULL,
  "error_code" character varying(100) NULL,
  "processed_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_reward_claim_results_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_reward_claim_results_user_id" to table: "reward_claim_results"
CREATE INDEX "idx_reward_claim_results_user_id" ON "public"."reward_claim_results" ("user_id");
