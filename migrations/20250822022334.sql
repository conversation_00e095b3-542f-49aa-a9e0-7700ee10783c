-- Create "infinite_agent_configs" table
CREATE TABLE "public"."infinite_agent_configs" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "commission_rate_n" numeric(10,6) NOT NULL,
  "total_net_fee_usd" numeric(38,18) NOT NULL,
  "total_standard_commission_paid_usd" numeric(38,18) NOT NULL,
  "final_commission_amount_usd" numeric(38,18) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'ACTIVE',
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_infinite_agent_configs_user_id" UNIQUE ("user_id"),
  CONSTRAINT "fk_infinite_agent_configs_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_infinite_agent_configs_status" to table: "infinite_agent_configs"
CREATE INDEX "idx_infinite_agent_configs_status" ON "public"."infinite_agent_configs" ("status");
-- Create index "idx_infinite_agent_configs_user_id" to table: "infinite_agent_configs"
CREATE INDEX "idx_infinite_agent_configs_user_id" ON "public"."infinite_agent_configs" ("user_id");
-- Create "infinite_agent_referral_trees" table
CREATE TABLE "public"."infinite_agent_referral_trees" (
  "id" bigserial NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "infinite_agent_user_id" uuid NOT NULL,
  "commission_rate_n" numeric(10,6) NOT NULL,
  "root_user_id" uuid NOT NULL,
  "snapshot_date" timestamptz NOT NULL,
  "total_nodes" bigint NULL DEFAULT 0,
  "max_depth" bigint NULL DEFAULT 0,
  "direct_count" bigint NULL DEFAULT 0,
  "active_users" bigint NULL DEFAULT 0,
  "trading_users" bigint NULL DEFAULT 0,
  "total_commission_earned" numeric(38,6) NULL DEFAULT 0,
  "total_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "status" character varying(20) NULL DEFAULT 'ACTIVE',
  "description" text NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_infinite_agent_referral_trees_infinite_agent_config" FOREIGN KEY ("infinite_agent_user_id") REFERENCES "public"."infinite_agent_configs" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_referral_trees_infinite_agent_user" FOREIGN KEY ("infinite_agent_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_referral_trees_root_user" FOREIGN KEY ("root_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_infinite_agent_referral_trees_infinite_agent_user_id" to table: "infinite_agent_referral_trees"
CREATE INDEX "idx_infinite_agent_referral_trees_infinite_agent_user_id" ON "public"."infinite_agent_referral_trees" ("infinite_agent_user_id");
-- Create index "idx_infinite_agent_referral_trees_root_user_id" to table: "infinite_agent_referral_trees"
CREATE INDEX "idx_infinite_agent_referral_trees_root_user_id" ON "public"."infinite_agent_referral_trees" ("root_user_id");
-- Create index "idx_infinite_agent_referral_trees_snapshot_date" to table: "infinite_agent_referral_trees"
CREATE INDEX "idx_infinite_agent_referral_trees_snapshot_date" ON "public"."infinite_agent_referral_trees" ("snapshot_date");
-- Create "infinite_agent_tree_nodes" table
CREATE TABLE "public"."infinite_agent_tree_nodes" (
  "id" bigserial NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "tree_id" bigint NOT NULL,
  "user_id" uuid NOT NULL,
  "parent_user_id" uuid NULL,
  "referrer_id" uuid NULL,
  "depth" bigint NULL DEFAULT 0,
  "level" bigint NULL DEFAULT 0,
  "position" bigint NULL DEFAULT 0,
  "is_active" boolean NULL DEFAULT true,
  "is_trading" boolean NULL DEFAULT false,
  "agent_level_id" bigint NULL DEFAULT 1,
  "commission_earned" numeric(38,6) NULL DEFAULT 0,
  "volume_usd" numeric(38,2) NULL DEFAULT 0,
  "fee_volume_usd" numeric(38,2) NULL DEFAULT 0,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_infinite_agent_referral_trees_tree_nodes" FOREIGN KEY ("tree_id") REFERENCES "public"."infinite_agent_referral_trees" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_tree_nodes_agent_level" FOREIGN KEY ("agent_level_id") REFERENCES "public"."agent_levels" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_tree_nodes_parent_user" FOREIGN KEY ("parent_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_tree_nodes_referrer" FOREIGN KEY ("referrer_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_infinite_agent_tree_nodes_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_infinite_agent_tree_nodes_parent_user_id" to table: "infinite_agent_tree_nodes"
CREATE INDEX "idx_infinite_agent_tree_nodes_parent_user_id" ON "public"."infinite_agent_tree_nodes" ("parent_user_id");
-- Create index "idx_infinite_agent_tree_nodes_referrer_id" to table: "infinite_agent_tree_nodes"
CREATE INDEX "idx_infinite_agent_tree_nodes_referrer_id" ON "public"."infinite_agent_tree_nodes" ("referrer_id");
-- Create index "idx_infinite_agent_tree_nodes_tree_id" to table: "infinite_agent_tree_nodes"
CREATE INDEX "idx_infinite_agent_tree_nodes_tree_id" ON "public"."infinite_agent_tree_nodes" ("tree_id");
-- Create index "idx_infinite_agent_tree_nodes_user_id" to table: "infinite_agent_tree_nodes"
CREATE INDEX "idx_infinite_agent_tree_nodes_user_id" ON "public"."infinite_agent_tree_nodes" ("user_id");
-- Create "referral_tree_snapshots" table
CREATE TABLE "public"."referral_tree_snapshots" (
  "id" bigserial NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "root_user_id" uuid NOT NULL,
  "snapshot_date" timestamptz NOT NULL,
  "total_nodes" bigint NULL DEFAULT 0,
  "max_depth" bigint NULL DEFAULT 0,
  "direct_count" bigint NULL DEFAULT 0,
  "active_users" bigint NULL DEFAULT 0,
  "trading_users" bigint NULL DEFAULT 0,
  "infinite_agent_user_id" uuid NULL,
  "has_infinite_agent" boolean NULL DEFAULT false,
  "description" text NULL,
  "is_valid" boolean NULL DEFAULT true,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_referral_tree_snapshots_infinite_agent_config" FOREIGN KEY ("infinite_agent_user_id") REFERENCES "public"."infinite_agent_configs" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_snapshots_infinite_agent_user" FOREIGN KEY ("infinite_agent_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_snapshots_root_user" FOREIGN KEY ("root_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_referral_tree_snapshots_infinite_agent_user_id" to table: "referral_tree_snapshots"
CREATE INDEX "idx_referral_tree_snapshots_infinite_agent_user_id" ON "public"."referral_tree_snapshots" ("infinite_agent_user_id");
-- Create index "idx_referral_tree_snapshots_root_user_id" to table: "referral_tree_snapshots"
CREATE INDEX "idx_referral_tree_snapshots_root_user_id" ON "public"."referral_tree_snapshots" ("root_user_id");
-- Create index "idx_referral_tree_snapshots_snapshot_date" to table: "referral_tree_snapshots"
CREATE INDEX "idx_referral_tree_snapshots_snapshot_date" ON "public"."referral_tree_snapshots" ("snapshot_date");
-- Create "referral_tree_nodes" table
CREATE TABLE "public"."referral_tree_nodes" (
  "id" bigserial NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "tree_snapshot_id" bigint NOT NULL,
  "user_id" uuid NOT NULL,
  "parent_user_id" uuid NULL,
  "referrer_id" uuid NULL,
  "depth" bigint NULL DEFAULT 0,
  "level" bigint NULL DEFAULT 0,
  "position" bigint NULL DEFAULT 0,
  "is_active" boolean NULL DEFAULT true,
  "is_trading" boolean NULL DEFAULT false,
  "agent_level_id" bigint NULL DEFAULT 1,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_referral_tree_nodes_agent_level" FOREIGN KEY ("agent_level_id") REFERENCES "public"."agent_levels" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_nodes_parent_user" FOREIGN KEY ("parent_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_nodes_referrer" FOREIGN KEY ("referrer_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_nodes_tree_snapshot" FOREIGN KEY ("tree_snapshot_id") REFERENCES "public"."referral_tree_snapshots" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_referral_tree_nodes_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_referral_tree_nodes_parent_user_id" to table: "referral_tree_nodes"
CREATE INDEX "idx_referral_tree_nodes_parent_user_id" ON "public"."referral_tree_nodes" ("parent_user_id");
-- Create index "idx_referral_tree_nodes_referrer_id" to table: "referral_tree_nodes"
CREATE INDEX "idx_referral_tree_nodes_referrer_id" ON "public"."referral_tree_nodes" ("referrer_id");
-- Create index "idx_referral_tree_nodes_tree_snapshot_id" to table: "referral_tree_nodes"
CREATE INDEX "idx_referral_tree_nodes_tree_snapshot_id" ON "public"."referral_tree_nodes" ("tree_snapshot_id");
-- Create index "idx_referral_tree_nodes_user_id" to table: "referral_tree_nodes"
CREATE INDEX "idx_referral_tree_nodes_user_id" ON "public"."referral_tree_nodes" ("user_id");
