-- Create "meme_commission_ledger" table
CREATE TABLE "public"."meme_commission_ledger" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "recipient_user_id" uuid NOT NULL,
  "source_user_id" uuid NOT NULL,
  "source_transaction_id" character varying(100) NOT NULL,
  "source_transaction_type" character varying(20) NOT NULL,
  "commission_amount" numeric(36,18) NOT NULL,
  "commission_asset" character varying(10) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING_CLAIM',
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "claimed_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_meme_commission_ledger_recipient_user" FOREIGN KEY ("recipient_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_meme_commission_ledger_source_user" FOREIGN KEY ("source_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_meme_commission_ledger_recipient_user_id" to table: "meme_commission_ledger"
CREATE INDEX "idx_meme_commission_ledger_recipient_user_id" ON "public"."meme_commission_ledger" ("recipient_user_id");
-- Create index "idx_meme_commission_ledger_source_user_id" to table: "meme_commission_ledger"
CREATE INDEX "idx_meme_commission_ledger_source_user_id" ON "public"."meme_commission_ledger" ("source_user_id");
-- Create index "idx_meme_commission_ledger_status" to table: "meme_commission_ledger"
CREATE INDEX "idx_meme_commission_ledger_status" ON "public"."meme_commission_ledger" ("status");
