package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	global.GVA_NATS_DEX = nats.InitNatsJetStream(global.GVA_CONFIG.NatsDex)
	global.GVA_NATS_MEME = nats.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		// initializer.Redis()
		// initializer.RedisList()
	}

	if global.GVA_DB != nil {
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	go initializer.InitTask()

	quit := make(chan os.Signal, 1)

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	fmt.Println("Shutting down server...")
}
