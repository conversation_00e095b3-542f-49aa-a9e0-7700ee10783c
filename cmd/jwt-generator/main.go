package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// convertDuration converts duration strings like "7d" to Go duration format like "168h"
func convertDuration(duration string) string {
	if strings.HasSuffix(duration, "d") {
		// Extract number of days
		daysStr := strings.TrimSuffix(duration, "d")
		days, err := strconv.Atoi(daysStr)
		if err != nil {
			return duration // Return original if can't parse
		}
		// Convert days to hours
		return fmt.Sprintf("%dh", days*24)
	}
	return duration // Return as-is for other formats
}

func main() {
	// Command line flags
	var (
		userID = flag.String("user-id", "", "User ID (UUID). If not provided, a random UUID will be generated")
		email  = flag.String("email", "<EMAIL>", "User email")
		help   = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		printHelp()
		return
	}

	// Initialize config
	global.GVA_VP = initializer.Viper()

	// Parse or generate user ID
	var parsedUserID uuid.UUID
	var err error

	if *userID == "" {
		parsedUserID = uuid.New()
		fmt.Printf("Generated new User ID: %s\n", parsedUserID.String())
	} else {
		parsedUserID, err = uuid.Parse(*userID)
		if err != nil {
			fmt.Printf("Error: Invalid User ID format: %v\n", err)
			fmt.Println("User ID must be a valid UUID (e.g., 123e4567-e89b-12d3-a456-************)")
			os.Exit(1)
		}
	}

	// Convert duration format if needed (e.g., "7d" -> "168h")
	jwtConfig := global.GVA_CONFIG.JWT
	jwtConfig.ExpiresTime = convertDuration(jwtConfig.ExpiresTime)
	jwtConfig.BufferTime = convertDuration(jwtConfig.BufferTime)

	// Generate JWT token
	token, err := utils.GenerateJWTToken(parsedUserID, *email, jwtConfig)
	if err != nil {
		fmt.Printf("Error generating JWT token: %v\n", err)
		os.Exit(1)
	}

	// Print results
	fmt.Println("\n=== JWT Token Generated Successfully ===")
	fmt.Printf("User ID: %s\n", parsedUserID.String())
	fmt.Printf("Email: %s\n", *email)
	fmt.Printf("Issuer: %s\n", global.GVA_CONFIG.JWT.Issuer)
	fmt.Printf("Expires: %s\n", global.GVA_CONFIG.JWT.ExpiresTime)
	fmt.Println("\n=== Bearer Token ===")
	fmt.Printf("Authorization: Bearer %s\n", token)
	fmt.Println("\n=== For curl/Postman ===")
	fmt.Printf("Bearer %s\n", token)
	fmt.Println("\n=== Raw Token ===")
	fmt.Println(token)

	// Validate the generated token
	fmt.Println("\n=== Token Validation ===")
	claims, err := utils.ValidateJWTToken(token, jwtConfig)
	if err != nil {
		fmt.Printf("Warning: Token validation failed: %v\n", err)
	} else {
		fmt.Printf("✓ Token is valid\n")
		fmt.Printf("✓ User ID from token: %s\n", claims.UserID.String())
		fmt.Printf("✓ Email from token: %s\n", claims.Email)
		fmt.Printf("✓ Expires at: %s\n", claims.ExpiresAt.Time.Format("2006-01-02 15:04:05"))
	}
}

func printHelp() {
	fmt.Println("JWT Token Generator for xbit-agent")
	fmt.Println("==================================")
	fmt.Println()
	fmt.Println("This tool generates JWT Bearer tokens for local testing.")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  go run cmd/jwt-generator/main.go [flags]")
	fmt.Println()
	fmt.Println("Flags:")
	fmt.Println("  -user-id string")
	fmt.Println("        User ID (UUID). If not provided, a random UUID will be generated")
	fmt.Println("  -email string")
	fmt.Println("        User email (default: <EMAIL>)")
	fmt.Println("  -help")
	fmt.Println("        Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  # Generate token with random user ID")
	fmt.Println("  go run cmd/jwt-generator/main.go")
	fmt.Println()
	fmt.Println("  # Generate token with specific user ID and email")
	fmt.Println("  go run cmd/jwt-generator/main.go -user-id=123e4567-e89b-12d3-a456-************ -email=<EMAIL>")
	fmt.Println()
	fmt.Println("  # Generate token with specific email only")
	fmt.Println("  go run cmd/jwt-generator/main.go -email=<EMAIL>")
	fmt.Println()
	fmt.Println("Environment:")
	fmt.Println("  The tool uses the same JWT configuration as your application.")
	fmt.Println("  Make sure your environment variables are set correctly:")
	fmt.Println("  - JWT_SECRET")
	fmt.Println("  - JWT_EXPIRES_TIME")
	fmt.Println("  - JWT_ISSUER")
}
