package main

import (
	"testing"
)

func TestConvertDuration(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"7d", "168h"},
		{"1d", "24h"},
		{"30d", "720h"},
		{"24h", "24h"}, // Should remain unchanged
		{"1h", "1h"},   // Should remain unchanged
		{"30m", "30m"}, // Should remain unchanged
		{"", ""},       // Empty string should remain unchanged
	}

	for _, test := range tests {
		result := convertDuration(test.input)
		if result != test.expected {
			t.Errorf("convertDuration(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestConvertDuration_InvalidInput(t *testing.T) {
	// Test with invalid day format
	result := convertDuration("abcd")
	expected := "abcd" // Should return original string
	if result != expected {
		t.<PERSON><PERSON>rf("convertDuration(%q) = %q, expected %q", "abcd", result, expected)
	}

	// Test with invalid number
	result = convertDuration("xd")
	expected = "xd" // Should return original string
	if result != expected {
		t.<PERSON><PERSON><PERSON>("convertDuration(%q) = %q, expected %q", "xd", result, expected)
	}
}
