package config

type SpecializedDB struct {
	Disable   bool   `mapstructure:"disable" json:"disable" yaml:"disable"`
	Type      string `mapstructure:"type" json:"type" yaml:"type"`
	AliasName string `mapstructure:"alias-name" json:"alias-name" yaml:"alias-name"`
	GeneralDB `yaml:",inline" mapstructure:",squash"`
}

func (s *SpecializedDB) Dsn() string {
	switch s.Type {
	case "pgsql":
		return "host=" + s.Path + " user=" + s.Username + " password=" + s.Password + " dbname=" + s.Dbname + " port=" + s.Port + " " + s.Config
	default:
		return ""
	}
}

func (s *SpecializedDB) GetLogMode() string {
	return s.LogMode
}
